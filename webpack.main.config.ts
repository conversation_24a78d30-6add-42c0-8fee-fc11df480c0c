import type { Configuration } from "webpack";
import { rules } from "./webpack.rules";
import { plugins } from "./webpack.plugins";
import CopyWebpackPlugin from "copy-webpack-plugin";
import path from "path";

export const mainConfig: Configuration = {
  /**
   * This is the main entry point for your application, it's the first file
   * that runs in the main process.
   */
  entry: {
    index: "./src/index.ts",
    "camera.worker": "./src/services/ipc/camera.worker.ts",
    "timelapse.worker": "./src/services/ipc/timelapse.worker.ts",
    "zip-photo.worker": "./src/services/ipc/zip-photo.worker.ts",
    "reupload.worker": "./src/services/ipc/reupload.worker.ts",
    "ai.worker": "./src/services/ipc/ai.worker.ts",
  },
  output: {
    filename: "[name].js",
    globalObject: "this",
  },

  // Put your normal webpack config below here
  module: {
    rules: rules.concat([
      {
        test: /\.node$/,
        use: "node-loader",
      },
      {
        test: /\.(m?js|node)$/,
        parser: { amd: false },
        use: {
          loader: "@timfish/webpack-asset-relocator-loader",
          options: {
            outputAssetBase: "resources/node_modules",
          },
        },
      },
      {
        test: /\.worker\.ts$/,
        use: [
          {
            loader: "ts-loader",
            options: {
              transpileOnly: true,
              compilerOptions: {
                module: "commonjs",
              },
            },
          },
        ],
        type: "javascript/auto",
      },
    ]),
  },
  plugins: [
    ...plugins,
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.resolve(
            __dirname,
            "node_modules/ffmpeg-static/ffmpeg.exe"
          ),
          to: path.resolve(__dirname, ".webpack/main/ffmpeg.exe"),
        },
        {
          from: path.join(__dirname, "src", "password-dialog.html"),
          to: path.join(__dirname, ".webpack/main/password-dialog.html"),
        },
      ],
    }),
  ],
  resolve: {
    extensions: [".js", ".ts", ".jsx", ".tsx", ".css", ".json"],
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  target: ["electron-main", "node"],
  externals: {
    serialport: "commonjs serialport",
    sharp: "commonjs sharp",
    "@vinacogroup/napi-canon-cameras":
      "commonjs @vinacogroup/napi-canon-cameras",
    electron: "commonjs electron",
    pm2: "commonjs pm2",
  },
  node: {
    __dirname: false,
    __filename: false,
  },
};
