import type { Configuration } from "webpack";
import path from "path";
import { rules } from "./webpack.rules";
import { plugins } from "./webpack.plugins";

rules.push({
  test: /\.css$/,
  use: [
    { loader: "style-loader" },
    { loader: "css-loader" },
    { loader: "postcss-loader" },
  ],
});

rules.push({
  test: /\.(png|svg|jpg|jpeg|gif)$/i,
  type: "asset/resource",
});

rules.push({
  test: /\.(woff|woff2|eot|ttf|otf)$/,
  use: [
    {
      loader: "file-loader",
      options: {
        name: "[name].[ext]",
        outputPath: "fonts/",
      },
    },
  ],
});

export const rendererConfig: Configuration = {
  module: {
    rules,
  },
  plugins,
  resolve: {
    alias: {
      "@/primary-screens": path.resolve(__dirname, "./src/screens/primary"),
      "@/common-screens": path.resolve(__dirname, "./src/screens/common"),
      "@/components": path.resolve(__dirname, "./src/components"),
      "@/assets": path.resolve(__dirname, "./src/assets"),
      "@/cores": path.resolve(__dirname, "./src/cores"),
      "@/hooks": path.resolve(__dirname, "./src/hooks"),
      "@/graphql": path.resolve(__dirname, "./src/graphql"),
      "@/contexts": path.resolve(__dirname, "./src/contexts"),
      "@/secondary-screens": path.resolve(__dirname, "./src/screens/secondary"),
      "@/redux": path.resolve(__dirname, "./src/redux"),
    },
    extensions: [".js", ".ts", ".jsx", ".tsx", ".css"],
    fallback: {
      fs: false,
      path: false,
      url: false,
      assert: false,
      stream: false,
      util: false,
      child_process: false,
      worker_threads: false,
      module: false,
    },
  },
  externals: {
    "@grpc/grpc-js": "commonjs @grpc/grpc-js",
    "@grpc/proto-loader": "commonjs @grpc/proto-loader",
  },
  target: "electron-renderer",
};
