import { BrowserWindow, ipcMain } from "electron";

const initIpcRouter = async (mainWindow: BrowserWindow, secondaryWindow?: BrowserWindow) => {
  ipcMain.handle("primary-navigate", (event, path) => {
    mainWindow.webContents.send("on-primary-navigate", path);
  });     

  ipcMain.handle("secondary-navigate", (event, path) => {
    secondaryWindow?.webContents?.send("on-secondary-navigate", path);
  });
};

export { initIpcRouter };
