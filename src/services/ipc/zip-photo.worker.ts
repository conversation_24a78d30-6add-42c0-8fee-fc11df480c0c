import path from "path";
import { isMainThread, workerData } from "worker_threads";
import archiver from "archiver";
import sharp from "sharp";
import fsPromises from "fs/promises";
import { createWriteStream } from "fs";
import { retryUpload } from "@/cores/utils/upload";

const CLIENT_APP_UPDATE_ORDER = `
  mutation clientAppCreateImage($input: CreateImageInput!, $file: Upload!) {
    clientAppCreateImage(input: $input, file: $file) {
      message
      captureMode
      domain
    }
  }
`;

if (!isMainThread) {
  const tryForUpload = async () => {
    let zipPath: string | null = null;
    let output: any = null;
    let archive: any = null;

    try {
      const params = workerData.params;
      await fsPromises.mkdir(params.baseDir, { recursive: true });

      zipPath = path.join(params.baseDir, `order-${Date.now()}.zip`);
      output = createWriteStream(zipPath);
      archive = archiver("zip", {
        zlib: { level: 9 },
      });

      // Handle output stream errors
      output.on("error", (err: any) => {
        console.error("Output stream error:", err);
        throw err;
      });

      // Log archive progress
      archive.on("progress", (progress: any) => {
        console.log("Archive progress:", progress);
      });

      output.on("close", async () => {
        console.log("Archive created:", zipPath);
        try {
          const result = await retryUpload({
            orderId: params.orderId,
            captureMode: params.captureMode,
            fileUrl: zipPath,
            apiUrl: params.apiUrl,
            accessToken: params.accessToken,
            workerName: "ZipPhotoWorker",
            query: CLIENT_APP_UPDATE_ORDER,
          });

          console.log("Upload result:", result);
          process.exit(0);
        } catch (error) {
          console.error("Error uploading:", error);
          throw error;
        }
      });

      archive.on("warning", (err: any) => {
        console.warn("Archive warning:", err);
      });

      archive.on("error", (err: any) => {
        console.error("Archive error:", err);
        throw err;
      });

      archive.pipe(output);

      // Process photos one by one to avoid memory issues
      for (const photo of params.photos) {
        try {
          const compressedBuffer = await sharp(photo)
            .webp({ quality: 90 })
            .resize(1500, 1000, { fit: "cover" })
            .toBuffer();

          archive.append(compressedBuffer, { name: path.basename(photo) });
        } catch (err) {
          console.error("Error processing photo:", photo, err);
          throw err;
        }
      }

      try {
        archive.file(params.finalUrl, {
          name: path.basename(params.finalUrl),
        });
      } catch (err) {
        console.error("Error processing final image:", err);
        throw err;
      }

      await archive.finalize();
      console.log("Archive finalized successfully");
    } catch (error) {
      console.error("Error in updateOrder:", error);
      console.error("Error stack:", error.stack);
      throw error;
    }
  };

  tryForUpload();
}
