import ffmpeg from "fluent-ffmpeg";
import fs from "fs";
import path from "path";
import { isMainThread, parentPort, workerData } from "worker_threads";
import { retryUpload } from "@/cores/utils/upload";

export const CLIENT_APP_UPDATE_TIMELAPSE_VIDEO = `
  mutation clientAppCreateTimeLapse(
    $input: CreateImageInput!
    $file: Upload!
  ) {
    clientAppCreateTimeLapse(input: $input, file: $file) {
      isSuccess
    }
  }
`;

if (!isMainThread) {
  const ffmpegPath = path.join(__dirname, "ffmpeg.exe");

  // Exit if FFmpeg binary is missing
  if (!fs.existsSync(ffmpegPath)) {
    console.error("[TimelapseWorker] FFmpeg binary not found at:", ffmpegPath);
    process.exit(1);
  }
  console.log("[TimelapseWorker] FFmpeg binary found at:", ffmpegPath);
  ffmpeg.setFfmpegPath(ffmpegPath);

  // Check for frames
  if (workerData.timelapseFrames.length === 0) {
    console.warn("[TimelapseWorker] No frames captured for timelapse");
    parentPort?.postMessage({
      action: "TIMELAPSE_FAILED",
      data: { id: workerData.orderId, error: "No frames" },
    });
    process.exit(0);
  }

  try {
    const timestamp = Date.now();
    const mp4Path = path.join(workerData.baseDir, `timelapse_${timestamp}.mp4`);
    const webmPath = path.join(
      workerData.baseDir,
      `timelapse_${timestamp}.webm`
    );

    const fileListPath = path.join(workerData.timelapseFolder, "filelist.txt");
    const fileListContent = fs
      .readdirSync(workerData.timelapseFolder)
      .filter((file) => file.endsWith(".jpg"))
      .sort()
      .map((file) => `file '${path.join(workerData.timelapseFolder, file)}'`)
      .join("\n");

    fs.writeFileSync(fileListPath, fileListContent);

    // Create MP4 from JPEG frames
    const mp4Command = ffmpeg()
      .input(fileListPath)
      .inputOptions(["-f", "concat", "-safe", "0"])
      .outputOptions([
        "-c:v",
        "libx264",
        "-preset",
        "medium",
        "-crf",
        "28",
        "-pix_fmt",
        "yuv420p",
        "-movflags",
        "+faststart",
        "-vf",
        "scale=960:-2",
        "-framerate",
        "30",
      ])
      .output(mp4Path);

    mp4Command
      .on("start", (commandLine) =>
        console.log("[TimelapseWorker] MP4 command:", commandLine)
      )
      .on("error", (err, stdout, stderr) => {
        console.error("[TimelapseWorker] MP4 creation failed:", stderr);
        parentPort?.postMessage({
          action: "TIMELAPSE_FAILED",
          data: { id: workerData.orderId, error: err.message },
        });
        process.exit(1);
      })
      .on("end", () => {
        console.log("[TimelapseWorker] MP4 created:", mp4Path);

        // Convert MP4 to WebM
        ffmpeg(mp4Path)
          .format("webm")
          .videoCodec("libvpx-vp9")
          .outputOptions([
            "-b:v",
            "800k", // giảm bitrate
            "-speed",
            "4", // tăng tốc độ encode
            "-threads",
            "0", // để FFmpeg tự tối ưu số luồng
            "-hide_banner", // giữ nguyên để giảm log
            "-vf",
            "scale=640:-2", // giảm độ phân giải
            "-crf",
            "30", // tăng CRF để encode nhanh hơn
            "-deadline",
            "realtime", // ưu tiên tốc độ tối đa
            "-cpu-used",
            "4", // tương đương -speed 4
          ])
          .on("start", (commandLine) =>
            console.log("[TimelapseWorker] WebM command:", commandLine)
          )
          .on("end", async () => {
            console.log("[TimelapseWorker] WebM created:", webmPath);

            // Notify parent thread of success
            parentPort?.postMessage({
              action: "TIMELAPSE_COMPLETED",
              data: {
                id: workerData.orderId,
                videoUrl: webmPath,
                mp4Url: mp4Path,
              },
            });

            // Cleanup frames after both videos are created

            try {
              const params = {
                orderId: workerData.orderId,
                captureMode: workerData.captureMode,
                fileUrl: mp4Path,
                apiUrl: workerData.apiUrl,
                accessToken: workerData.token,
                workerName: "TimelapseWorker",
                query: CLIENT_APP_UPDATE_TIMELAPSE_VIDEO,
              };

              await retryUpload(params);
            } catch (error) {
              console.log("[TimelapseWorker] Upload failed:", error);
            } finally {
              workerData.timelapseFrames
                .filter((frame: string) => frame.includes("frame_"))
                .forEach((frame: string) => {
                  try {
                    fs.unlinkSync(frame);
                  } catch (_err) {
                    console.log(
                      "[TimelapseWorker] Delete frame failed:",
                      frame
                    );
                  }
                });

              try {
                fs.unlinkSync(fileListPath);
              } catch (_err) {
                console.log(
                  "[TimelapseWorker] Delete file list failed:",
                  fileListPath
                );
              }

              process.exit(0);
            }
          })
          .on("error", (err) => {
            console.error(
              "[TimelapseWorker] WebM conversion failed:",
              err.message
            );
            parentPort?.postMessage({
              action: "TIMELAPSE_FAILED",
              data: { id: workerData.orderId, error: err.message },
            });
            process.exit(1);
          })
          .save(webmPath);
      })
      .run();
  } catch (error) {
    console.error("[TimelapseWorker] Unexpected error:", error);
    parentPort?.postMessage({
      action: "TIMELAPSE_FAILED",
      data: { id: workerData.orderId, error: error.message },
    });
    process.exit(1);
  }
}
