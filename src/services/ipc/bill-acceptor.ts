import { BrowserWindow, ipcMain } from "electron";
import { billAcceptorService } from "../bill-acceptor";

const registerBillAccepterEventHandlers = async (mainWindow: BrowserWindow) => {
  billAcceptorService.events.on("bill-value", (bill: any) => {
    mainWindow.webContents.send("received-cash", bill);
  });
};

const registerBillAcceptorStartPollingHandler = async () => {
  ipcMain.handle("billing-start-polling", async () => {
    billAcceptorService.toggleBillAcceptor(false);
  });
};

const registerBillAcceptorStopPollingHandler = async () => {
  ipcMain.handle("billing-stop-polling", async () => {
    billAcceptorService.toggleBillAcceptor(true);
  });
};

const initBillAcceptor = async (mainWindow: BrowserWindow) => {
  await registerBillAccepterEventHandlers(mainWindow);
};

const initBillAcceptorWithoutWindow = async () => {
  await registerBillAcceptorStartPollingHandler();
  await registerBillAcceptorStopPollingHandler();
};

export { initBillAcceptor, initBillAcceptorWithoutWindow };
