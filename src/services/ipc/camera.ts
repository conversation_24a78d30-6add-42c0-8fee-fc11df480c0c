import { BrowserWindow, ipc<PERSON>ain, app } from "electron";
import { isMain<PERSON>hread, Worker } from "worker_threads";
import path from "path";
import { relayService } from "../relay";
let worker: Worker | null = null;
const timelapseWorkerPath = path.join(__dirname, "timelapse.worker.js");
const aiWorkerPath = path.join(__dirname, "ai.worker.js");

const initIpcCamera = async (
  mainWindow: BrowserWindow,
  secondaryWindow?: BrowserWindow,
  noRelay = false
) => {
  if (isMainThread) {
    const workerPath = path.join(__dirname, "camera.worker.js");
    const timelapseFolder = path.join(app?.getPath("userData"), "timelapse");

    const notify = (eventName: string, data: any) => {
      if (eventName === "timelapse-completed") {
        console.log("timelapse-completed", data);
      }

      if (secondaryWindow) {
        secondaryWindow.webContents.send(eventName, data);
      }

      mainWindow.webContents.send(eventName, data);
    };

    ipcMain.handle("setup-camera", async () => {
      await setupCamera();
    });

    ipcMain.handle("capture-photo", async () => {
      worker.postMessage({ action: "CAPTURE_PHOTO" });
    });

    ipcMain.handle(
      "register-capture-workspace",
      async (_, workspace: string) => {
        const baseDir = path.join(app?.getPath("userData"), workspace);

        worker.postMessage({
          action: "REGISTER_CAPTURE_WORKSPACE",
          baseDir,
        });
      }
    );

    ipcMain.handle("start-live-view", async () => {
      worker.postMessage({ action: "START_LIVE_VIEW" });
    });

    ipcMain.handle("stop-live-view", async () => {
      worker.postMessage({ action: "STOP_LIVE_VIEW" });
    });

    ipcMain.handle("start-timelapse", async (_, interval = 1000) => {
      worker.postMessage({ action: "START_TIMELAPSE", interval });
    });

    ipcMain.handle("stop-timelapse", async (_, payload: any) => {
      const baseDir = path.join(app?.getPath("userData"), payload.dir);

      worker.postMessage({
        action: "STOP_TIMELAPSE",
        baseDir,
        orderId: payload.id,
        token: payload.token,
        apiUrl: payload.apiUrl,
        captureMode: payload.captureMode,
      });
    });

    ipcMain.handle("restart-liveview", async () => {
      try {
        console.log("[CameraService] Restarting live view with current camera");
        worker.postMessage({
          action: "START_LIVE_VIEW",
          useCurrentCamera: true,
        });
      } catch (error) {
        console.error("[CameraService] Error restarting liveview:", error);
      }
    });

    ipcMain.handle("get-cameras", async () => {
      try {
        console.log("[CameraService] Getting cameras list");
        worker.postMessage({ action: "GET_CAMERAS" });
      } catch (error) {
        console.error("[CameraService] Error getting cameras list:", error);
      }
    });

    ipcMain.handle("switch-camera", async (_, cameraIndex: number) => {
      try {
        console.log(
          `[CameraService] Switching to camera at index ${cameraIndex}`
        );
        worker.postMessage({
          action: "SWITCH_CAMERA",
          cameraIndex,
        });
      } catch (error) {
        console.error(
          `[CameraService] Error switching to camera at index ${cameraIndex}:`,
          error
        );
      }
    });

    ipcMain.handle("turn-off-camera", async () => {
      try {
        await relayService.turnOffCamera();
      } catch (error) {
        console.error("[CameraService] Error turning off camera:", error);
      }
    });

    ipcMain.handle("cleanup-camera", async () => {
      worker.postMessage({ action: "CLEAN_CAMERA" });
    });

    ipcMain.handle("disconnect-relay", async () => {
      try {
        if (!noRelay) {
          await relayService.turnOff();
        }
      } catch (error) {
        console.error("[CameraService] Error disconnecting relay:", error);
      }
    });

    const setupCamera = async (isReconnect = false) => {
      try {
        if (worker) {
          worker.removeAllListeners();
          worker.terminate();
          worker = null;
        }

        worker = new Worker(workerPath, {
          workerData: {
            timelapseFolder,
          },
        });

        worker.postMessage({ action: "REGISTER_CAMERA_CONNECTION" });
        worker.setMaxListeners(20);
        worker.on("message", async (message: any) => {
          switch (message.action) {
            case "LIVE_VIEW_IMAGE":
              notify("liveViewImage", message.data);
              break;
            case "PHOTO_CAPTURED":
              notify("takePicture", message.data);
              break;
            case "PHOTO_CAPTURE_ERROR":
              notify("photo-capture-error", message.data);
              break;
            case "CAMERA_NOT_CONNECTED":
              notify("camera-not-connected", message.data);

              try {
                await relayService.turnOffCamera();
                console.log("reconnect camera");
                await new Promise((resolve) => setTimeout(resolve, 3000));
                setupCamera(true);
                console.log("reconnect camera success");
              } catch (error) {
                console.error(
                  "[CameraService] Error turning on camera:",
                  error
                );
              }
              break;
            case "CAMERAS_LIST":
              console.log(
                "[CameraService] Received cameras list:",
                message.data
              );
              notify("camerasList", message.data);
              break;
            case "CAMERA_SWITCHED":
              console.log("[CameraService] Camera switched:", message.data);
              notify("cameraSwitched", message.data);
              break;
            case "CAMERA_SWITCH_ERROR":
            case "CAMERAS_LIST_ERROR":
              console.error("[CameraService] Camera error:", message.data);
              notify("cameraError", message.data);
              break;
            case "MAKING_TIMELAPSE":
              try {
                const timelapseWorker = new Worker(timelapseWorkerPath, {
                  workerData: message.data,
                });

                timelapseWorker.on("message", (message: any) => {
                  if (message.action === "TIMELAPSE_COMPLETED") {
                    notify("timelapse-completed", message.data);
                  }
                });

                timelapseWorker.on("error", (error: any) => {
                  console.error("Error processing timelapse:", error);
                });

                timelapseWorker.on("exit", (code: any) => {
                  console.log(
                    `[Timelapse Worker] Timelapse worker exited with code ${code}`
                  );
                });
              } catch (error) {
                console.error("Error processing timelapse:", error);
              }
              break;
            case "REMOVE_BACKGROUND":
              try {
                const aiWorker = new Worker(aiWorkerPath, {
                  workerData: message.data,
                });

                aiWorker.on("error", (error: any) => {
                  console.error("Error processing timelapse:", error);
                });

                aiWorker.on("exit", (code: any) => {
                  console.log(`[AI Worker] AI worker exited with code ${code}`);
                });
              } catch (error) {
                console.error("Error processing timelapse:", error);
              }
              break;
          }
        });

        worker.on("error", (error) => {
          console.error("[CameraService] Worker error:", error);
        });

        worker.on("exit", (code) => {
          console.log(`[CameraService] Worker exited with code ${code}`);
        });

        if (!noRelay && !isReconnect) {
          await relayService.turnOn();
        }

        if (isReconnect) {
          await relayService.turnOnCamera();
        }
      } catch (error) {
        console.error("[CameraService] Setup camera error:", error);
      }
    };
  }
};

export { initIpcCamera };
