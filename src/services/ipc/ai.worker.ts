import { spawn } from "child_process";
import { isMainThread, workerData } from "worker_threads";

if (!isMainThread) {
  const removeBackgroundExecute = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      console.log("workerData", workerData);

      const jsonData = JSON.stringify(workerData);
      const child = spawn("curl", [
        "-X",
        "POST",
        "-H",
        "Content-Type: application/json",
        "-d",
        jsonData,
        "http://localhost:7860/remove-bg",
      ]);

      child.stdout.on("data", (data) => {
        console.log(`[Snapbox stdout]: ${data}`);
      });

      child.stderr.on("data", (data) => {
        console.error(`[Snapbox stderr]: ${data}`);
      });

      child.on("close", (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`[Snapbox] exited with code ${code}`));
        }
      });

      child.on("error", reject);
    });
  };

  removeBackgroundExecute()
    .then(() => {
      console.log("✅ Background removed successfully.");
      process.exit(0); // exit đúng sau khi xong
    })
    .catch((err) => {
      console.error("❌ Failed to remove background:", err);
      process.exit(1);
    });
}
