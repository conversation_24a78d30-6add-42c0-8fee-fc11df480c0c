import { app, ipcMain } from 'electron';
import { localStorageService, STORAGE_KEYS } from '../storage';
import { IDownloadImageParams } from '@/cores/types';
import path from 'path';
import fs from 'fs';
import fsPromises from 'fs/promises';
import https from 'https';

const insertAppearanceSetting = async (data: any) => {
  try {
    localStorageService.setItem(STORAGE_KEYS.APPEARANCES, data);
  } catch (error) {
    console.error('Error inserting appearance setting:', error);
    throw error;
  }
};

const insertWaitingScreen = async (data: any) => {
  try {
    localStorageService.setItem(STORAGE_KEYS.WAITING_SCREENS, data);
  } catch (error) {
    console.error('Error inserting waiting screen:', error);
    throw error;
  }
};

const insertLayouts = async (data: any) => {
  try {
    localStorageService.setItem(STORAGE_KEYS.LAYOUTS, data);
  } catch (error) {
    console.error('Error inserting layouts:', error);
    throw error;
  }
};

const insertSizes = async (data: any) => {
  try {
    localStorageService.setItem(STORAGE_KEYS.SIZES, data);
  } catch (error) {
    console.error('Error inserting sizes:', error);
    throw error;
  }
};

const insertStickers = async (data: any) => {
  try {
    localStorageService.setItem(STORAGE_KEYS.STICKERS, data);
  } catch (error) {
    console.error('Error inserting stickers:', error);
    throw error;
  }
};

const insertFrames = async (data: any) => {
  try {
    localStorageService.setItem(STORAGE_KEYS.FRAMES, data);
  } catch (error) {
    console.error('Error inserting frames:', error);
    throw error;
  }
};

const insertPrintSetting = async (data: any) => {
  try {
    localStorageService.setItem(STORAGE_KEYS.PRINT_SETTINGS, data);
  } catch (error) {
    console.error('Error inserting print settings:', error);
    throw error;
  }
};

const retrivedRecords = async () => {
  try {
    const clientAppGetAppearanceSetting = localStorageService.getItem(STORAGE_KEYS.APPEARANCES);
    const clientAppWaitingScreen = localStorageService.getItem(STORAGE_KEYS.WAITING_SCREENS);
    const clientAppGetLayouts = localStorageService.getItem(STORAGE_KEYS.LAYOUTS);
    const clientAppGetSettingSizes = localStorageService.getItem(STORAGE_KEYS.SIZES);
    const clientAppGetStickers = localStorageService.getItem(STORAGE_KEYS.STICKERS);
    const clientAppGetFrames = localStorageService.getItem(STORAGE_KEYS.FRAMES);
    const clientAppGetPrintSetting = localStorageService.getItem(STORAGE_KEYS.PRINT_SETTINGS);

    return JSON.stringify({
      clientAppGetAppearanceSetting,
      clientAppWaitingScreen,
      clientAppGetLayouts,
      clientAppGetSettingSizes,
      clientAppGetStickers,
      clientAppGetFrames,
      clientAppGetPrintSetting
    });
  } catch (error) {
    console.error('Error retrieving records:', error);
    throw error;
  }
};

const clearDatabase = async () => {
  try {
    localStorageService.clear();
  } catch (error) {
    console.error('Error clearing database:', error);
    throw error;
  }
};

const destroyDatabase = async () => {
  try {
    localStorageService.clear();
  } catch (error) {
    console.error('Error destroying database:', error);
    throw error;
  }
};

const downloadImage = async (params: IDownloadImageParams) => {
  const { url, type, filename } = params;
  
  // Create base directory for images
  const baseDir = path.join(app.getPath('userData'), 'images', type);
  await fsPromises.mkdir(baseDir, { recursive: true });
  
  const localPath = path.join(baseDir, filename);
  
  return new Promise<string>((resolve, reject) => {
    const options = {
      // Ưu tiên IPv4 trước
      family: 4,
      timeout: 10000 // Timeout sau 5 giây
    };

    https.get(url, options, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download: ${response.statusCode}`));
        return;
      }

      const fileStream = fs.createWriteStream(localPath);
      response.pipe(fileStream);

      fileStream.on('finish', () => {
        fileStream.close();
        resolve(`file://${localPath}`);
      });

      fileStream.on('error', (err) => {
        console.log('Error downloading image [1]:', err);
        fsPromises.unlink(localPath).catch(console.error);
        reject(err);
      });
    }).on('error', (err: any) => {
      console.log('Error downloading image [2]:', err);
      // Nếu lỗi là ETIMEDOUT, thử lại với IPv6
      if (err.code === 'ETIMEDOUT') {
        https.get(url, { family: 6, timeout: 5000 }, (response) => {
          if (response.statusCode !== 200) {
            reject(new Error(`Failed to download: ${response.statusCode}`));
            return;
          }

          const fileStream = fs.createWriteStream(localPath);
          response.pipe(fileStream);

          fileStream.on('finish', () => {
            fileStream.close();
            resolve(`file://${localPath}`);
          });

          fileStream.on('error', (finalErr) => {
            console.log('Final error downloading image:', finalErr);
            fsPromises.unlink(localPath).catch(console.error);
            reject(finalErr);
          });
        }).on('error', (finalErr) => {
          console.log('Final error downloading image:', finalErr);
          reject(finalErr);
        });
      } else {
        reject(err);
      }
    });
  });
}


const getRxDatabase = async () => {
  // Trả về một đối tượng giả lập để tương thích với code cũ
  return {
    collections: {
      appearances: {
        findOne: () => ({
          exec: async () => localStorageService.getItem(STORAGE_KEYS.APPEARANCES)
        })
      },
      waiting_screens: {
        findOne: () => ({
          exec: async () => localStorageService.getItem(STORAGE_KEYS.WAITING_SCREENS)
        })
      },
      layouts: {
        find: () => ({
          exec: async () => localStorageService.getItem(STORAGE_KEYS.LAYOUTS)
        })
      },
      sizes: {
        find: () => ({
          exec: async () => localStorageService.getItem(STORAGE_KEYS.SIZES)
        })
      },
      stickers: {
        find: () => ({
          exec: async () => localStorageService.getItem(STORAGE_KEYS.STICKERS)
        })
      },
      frames: {
        find: () => ({
          exec: async () => localStorageService.getItem(STORAGE_KEYS.FRAMES)
        }),
        findOne: (id: string) => ({
          exec: async () => {
            const frames = localStorageService.getItem(STORAGE_KEYS.FRAMES) || [];
            return frames.find((frame: any) => frame.id === id);
          }
        })
      },
      print_settings: {
        findOne: () => ({
          exec: async () => localStorageService.getItem(STORAGE_KEYS.PRINT_SETTINGS)
        })
      }
    }
  };
};

const initRxDatabase = async () => {
  ipcMain.handle('insert-appearence', (_, data) => insertAppearanceSetting(data));
  ipcMain.handle('insert-waiting-screen', (_, data) => insertWaitingScreen(data));
  ipcMain.handle('insert-layouts', (_, data) => insertLayouts(data));
  ipcMain.handle('insert-sizes', (_, data) => insertSizes(data));
  ipcMain.handle('insert-stickers', (_, data) => insertStickers(data));
  ipcMain.handle('insert-frames', (_, data) => insertFrames(data));
  ipcMain.handle('download-image', (_, params) => downloadImage(params));
  ipcMain.handle('retrived-records', () => retrivedRecords());
  ipcMain.handle('insert-print-setting', (_, data) => insertPrintSetting(data));
}


export {
  initRxDatabase,
};

