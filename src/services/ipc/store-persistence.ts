import {
  saveReduxState,
  loadReduxState,
  clearReduxState,
} from "@/redux/store.persistence";
import { ipcMain } from "electron";

// Thiết lập IPC handlers cho main process
export const initStorePersistenceIpc = async () => {
  // Handler để lưu state
  ipcMain.handle("redux:save-state", (_, state) => {
    return saveReduxState(state);
  });

  // Handler để lấy state
  ipcMain.handle("redux:load-state", () => {
    return loadReduxState();
  });

  // Handler để xóa state
  ipcMain.handle("redux:clear-state", () => {
    return clearReduxState();
  });
};
