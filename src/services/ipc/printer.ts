import axios from "axios";
import { ipcMain } from "electron";
import path from "path";
import fs from "fs";
import sharp from "sharp";
import { downloadImage } from "@/cores/utils/img";
import { getConfig } from "@/cores/utils/config";

const config = getConfig();

const _isUrl = (string: string) => {
  try {
    return string?.startsWith("http://") || string?.startsWith("https://");
  } catch {
    return false;
  }
};

const _downloadImage = async (url: string) => {
  console.log(`[DEBUG] Đang tải ảnh từ URL: ${url}`);
  const localPath = path.join(__dirname, `temp_${Date.now()}.png`);
  const isDownloaded = await downloadImage({ url, localUrl: localPath });

  if (!isDownloaded) {
    throw new Error("[downloadImage] Không tải đ<PERSON>");
  }

  return localPath;
};

const updateMachineStatus = async (options: any) => {
  try {
    if (!options?.apiUrl) {
      throw new Error("[updateMachineStatus] Không tìm thấy apiUrl");
    }

    if (!options?.accessToken) {
      throw new Error("[updateMachineStatus] Không tìm thấy accessToken");
    }

    fs.readFile(
      "C:\\DNP\\HotFolderPrint\\Logs\\printer_status.txt",
      "utf8",
      async (err, data) => {
        if (err) {
          console.error("Error reading log file:", err);
          return;
        }

        const jsonData = JSON.parse(data);
        const rx1 = jsonData.find((item: any) => item.Name === "RX1HS");

        if (!rx1 || !rx1.MediaRemaining || !rx1.Status) {
          console.error("Không tìm thấy dữ liệu máy in RX1HS");
          return;
        }

        await axios.post(
          options?.apiUrl,
          {
            query: `
          mutation (
            $pendingPrints: Float!
            $remainingMedia: Float!
            $remainingInk: Float!
            $state: String!
          ) {
            clientAppUpdateMachine(
              input: {
                state: $state
                pendingPrints: $pendingPrints
                remainingInk: $remainingInk
                remainingMedia: $remainingMedia
              }
            ) {
              success
            }
          }
        `,
            variables: {
              pendingPrints: 0,
              remainingMedia: rx1.MediaRemaining - options.quantity,
              remainingInk: 0,
              state: rx1.Status,
            },
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${options.accessToken}`,
            },
          }
        );
      }
    );
  } catch (error) {
    console.error(
      "[Printer Worker] Lỗi khi cập nhật trạng thái máy in:",
      error
    );
  }
};

async function execute(
  inputPath: string,
  outputPath: string,
  copies = 1,
  paddings: any
) {
  let imageBuffer = await sharp(inputPath).toBuffer();
  const metadata = await sharp(imageBuffer).metadata();

  // Xoay ảnh nếu width > height
  if (metadata.width && metadata.height && metadata.width > metadata.height) {
    imageBuffer = await sharp(imageBuffer).rotate(90).toBuffer();
    // Cập nhật metadata sau khi xoay
    const newMetadata = await sharp(imageBuffer).metadata();
    metadata.width = newMetadata.width;
    metadata.height = newMetadata.height;
  }

  const width = metadata.width || 0;
  const height = metadata.height || 0;

  const originalFileName = path.basename(inputPath);
  const fileExtension = path.extname(originalFileName);
  const fileNameWithoutExt = originalFileName.slice(0, -fileExtension.length);
  const imagesToProcess = [];

  imagesToProcess.push({ buffer: imageBuffer, suffix: "" });

  // Xử lý và lưu từng ảnh
  for (let imageIndex = 0; imageIndex < imagesToProcess.length; imageIndex++) {
    const { buffer, suffix } = imagesToProcess[imageIndex];
    for (let i = 0; i < copies; i++) {
      // Tạo tên file cho mỗi bản sao
      const timestamp = Date.now();
      const copyFileName =
        copies > 1
          ? `${fileNameWithoutExt}${suffix}_${i + 1}_${timestamp}${fileExtension}`
          : `${fileNameWithoutExt}${suffix}_${timestamp}${fileExtension}`;

      // Tạo đường dẫn đầy đủ đến file trong thư mục output
      const copyOutputPath = `${outputPath}\\${copyFileName}`;
      console.log(`[DEBUG] copyOutputPath: ${copyOutputPath}`);
      await sharp({
        create: {
          width,
          height,
          channels: 3,
          background: { r: 255, g: 255, b: 255 }, // Nền trắng
        },
      })
        .composite([
          {
            input: await sharp(buffer)
              .resize(paddings.width, paddings.height) // Thu nhỏ ảnh
              .toBuffer(),
            top: paddings.top,
            left: paddings.left,
          },
        ])
        .toFile(copyOutputPath)
        .then((res) => {
          console.log(`[DEBUG] Lưu ảnh thành công: ${res}`);
        })
        .catch((err) => {
          console.error("❌ Lỗi khi lưu ảnh:", err);
        });
    }
  }
}

const checkInternetConnection = async () => {
  console.log(`[DEBUG] Đang kiểm tra kết nối internet...`);
  try {
    await axios.get("https://www.google.com", { timeout: 5000 });
    console.log(`[DEBUG] Kết nối internet OK`);
    return true;
  } catch (error) {
    console.log(`[DEBUG] Không có kết nối internet: ${error}`);
    return false;
  }
};

export const initIpcPrinter = async () => {
  ipcMain.handle("dnp-print", async (_, payload: any) => {
    console.log(`[DEBUG] Nhận yêu cầu in ảnh:`, JSON.stringify(payload));
    try {
      const { imgUrl, options } = payload;
      const copies = options.quantity;
      const cutable = options.cutable;
      const paddings = options.paddings;
      let printUrl = imgUrl;
      const targetFolder = cutable ? "4x6_2IN" : "4x6";
      let output = path.join("C:", "DNP", "Hot Folder", "Prints", targetFolder);

      if (config.sharedPrinterDir) {
        output = `\\${config.sharedPrinterDir}\\${targetFolder}`;
      }

      console.log(`[DEBUG] config: ${JSON.stringify(config)}`);
      console.log(`[DEBUG] sharedPrinterDir: ${config.sharedPrinterDir}`);
      console.log(`[DEBUG] output: ${output}`);

      fs.access(output, fs.constants.W_OK, (err) => {
        if (err) {
          console.error("❌ Không có quyền ghi vào thư mục:", output);
          console.error(err.message);
        } else {
          console.log("✅ Có quyền ghi vào thư mục:", output);
        }
      });

      if (_isUrl(imgUrl)) {
        console.log(`[DEBUG] imgUrl là URL, đang kiểm tra kết nối internet...`);
        const hasInternet = await checkInternetConnection();
        if (!hasInternet) {
          console.log(`[DEBUG] Không có kết nối internet, hủy in ảnh`);
          return;
        }

        printUrl = await _downloadImage(imgUrl);
      }

      console.log(`[DEBUG] Bắt đầu thực hiện in ảnh...`);
      await execute(printUrl, output, copies, paddings);
      console.log(`[DEBUG] Hoàn thành in ảnh`);
      // updateMachineStatus(options);
    } catch (error) {
      console.error(`[DEBUG] Lỗi chi tiết khi in:`, error);
      console.error(
        `[DEBUG] Stack trace:`,
        error instanceof Error ? error.stack : "Không có stack trace"
      );
    }
  });
};
