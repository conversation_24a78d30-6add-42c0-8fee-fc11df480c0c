import { BrowserWindow } from "electron";
import { relayService } from "../relay";
import {
  initBillAcceptor,
  initBillAcceptorWithoutWindow,
} from "./bill-acceptor";
import { initIpcCamera } from "./camera";
import { initMainWindow, initSecondaryWindow } from "./electron-window";
import { initIpcRouter } from "./router";
import { initShared, initSharedWithoutWindow } from "./shared";
import { initIpcPrinter } from "./printer";
import { initRxDatabase } from "./rx-database";
import { getConfig } from "@/cores/utils/config";
import { billAcceptorService } from "../bill-acceptor";
import { initIpcLark } from "./lark";
import { initStorePersistenceIpc } from "./store-persistence";

const config = getConfig();

const initIpc = async (
  mainWindow: BrowserWindow,
  secondaryWindow?: BrowserWindow
) => {
  try {
    if (!config.noRelay) {
      await relayService.connect(
        config.relayPort,
        config.baudRate,
        config.newRelay,
        config.enableMultipleCameras
      );
    }

    billAcceptorService.connect();

    await initShared(mainWindow);
    await initBillAcceptor(mainWindow);
    await initIpcCamera(mainWindow, secondaryWindow, config.noRelay);
    await initIpcRouter(mainWindow, secondaryWindow);
  } catch (error) {
    console.error("Error initializing IPC:", error);
  }
};

const initIpcWithoutWindow = async () => {
  await initSharedWithoutWindow();
  await initBillAcceptorWithoutWindow();
  await initRxDatabase();
  await initIpcPrinter();
  await initIpcLark();
  await initStorePersistenceIpc();
};

const releaseNativeResources = async () => {
  try {
    if (!config.noRelay) {
      await relayService.turnOff();
      await relayService.cleanup();
    }

    billAcceptorService.close();
  } catch (error) {
    console.error("Error releasing native resources:", error);
  }
};

const disconnectRelay = async () => {
  try {
    if (!config.noRelay) {
      await relayService.turnOff();
    }
  } catch (error) {
    console.error("Error disconnecting relay:", error);
  }
};

export {
  disconnectRelay,
  initIpc,
  initMainWindow,
  initSecondaryWindow,
  releaseNativeResources,
  initRxDatabase,
  initIpcWithoutWindow,
};
