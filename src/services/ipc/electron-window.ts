import { BrowserWindow } from "electron";

declare const MAIN_WINDOW_WEBPACK_ENTRY: string;
declare const MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY: string;
declare const SECONDARY_WINDOW_WEBPACK_ENTRY: string;
declare const SECONDARY_WINDOW_PRELOAD_WEBPACK_ENTRY: string;

const wOptions: any = {
  autoHideMenuBar: true,
  icon: "./assets/icon.png",
  titleBarStyle: "hidden",
  show: false,
  fullscreen: true,
  frame: false,
  webPreferences: {
    preload: MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY,
    nodeIntegration: true,
    contextIsolation: false,
    webSecurity: true,
    sandbox: false,
    nodeIntegrationInWorker: false,
    allowRunningInsecureContent: false,
    additionalArguments: [
      "--allow-file-access-from-files",
      "--allow-file-access",
    ],
  },
};

export const initMainWindow = async (display: any) => {
  if (process.env.NODE_ENV === "development") {
    wOptions.width = 1920;
    wOptions.height = 1080;
    wOptions.fullscreen = false;
  }

  const wdw = new BrowserWindow({
    ...wOptions,
    x: display.bounds.x,
    y: display.bounds.y,
  });

  // Add this line to open DevTools
  if (process.env.NODE_ENV === "development") {
    wdw.webContents.openDevTools();
  }

  wdw.webContents.session.setPermissionRequestHandler(
    (_, permission, callback) => {
      if (permission === "media") {
        callback(true);
      } else {
        callback(false);
      }
    }
  );

  wdw.webContents.on("did-fail-load", (_, errorCode, errorDescription) => {
    console.error("[Primary] Failed to load:", errorCode, errorDescription);
  });

  wdw.on("ready-to-show", () => {
    console.log("ready-to-show event triggered");
    if (process.env.NODE_ENV !== "development") {
      wdw.setFullScreen(true);
    }
    wdw.show();
  });

  // Now load the URL
  try {
    await wdw.loadURL(MAIN_WINDOW_WEBPACK_ENTRY);
    console.log("[Primary] URL loaded successfully");
  } catch (error) {
    console.error("[Primary] Error loading URL:", error);
  }

  wdw.webContents.session.webRequest.onHeadersReceived(
    (details: any, callback: any) => {
      callback({ responseHeaders: details.responseHeaders });
    }
  );

  console.info("[Primary] Window created successfully");
  return wdw;
};

export const initSecondaryWindow = async (display: any) => {
  const wdw = new BrowserWindow({
    x: display.bounds.x,
    y: display.bounds.y,
    autoHideMenuBar: true,
    icon: "./assets/icon.png",
    titleBarStyle: "hidden",
    show: false,
    fullscreen: true,
    frame: false,
    webPreferences: {
      preload: SECONDARY_WINDOW_PRELOAD_WEBPACK_ENTRY,
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: true,
      sandbox: false,
      nodeIntegrationInWorker: false,
      allowRunningInsecureContent: false,
      additionalArguments: [
        "--allow-file-access-from-files",
        "--allow-file-access",
      ],
    },
  });

  if (process.env.NODE_ENV === "development") {
    wdw.webContents.openDevTools();
  }

  wdw.webContents.session.setPermissionRequestHandler(
    (_, permission, callback) => {
      if (permission === "media") {
        callback(true);
      } else {
        callback(false);
      }
    }
  );

  wdw.webContents.on("did-fail-load", (_, errorCode, errorDescription) => {
    console.error("[Secondary] Failed to load:", errorCode, errorDescription);
  });

  wdw.on("ready-to-show", () => {
    console.log("[Secondary] ready-to-show event triggered");
    if (process.env.NODE_ENV === "development") {
      wdw.maximize();
    } else {
      wdw.setFullScreen(true);
    }
    wdw.show();
  });

  // Now load the URL
  try {
    await wdw.loadURL(SECONDARY_WINDOW_WEBPACK_ENTRY);
    console.log("[Secondary] URL loaded successfully");
  } catch (error) {
    console.error("[Secondary] Error loading URL:", error);
  }

  wdw.webContents.session.webRequest.onHeadersReceived(
    (details: any, callback: any) => {
      callback({ responseHeaders: details.responseHeaders });
    }
  );

  console.info("[Secondary] Window created successfully");
  return wdw;
};
