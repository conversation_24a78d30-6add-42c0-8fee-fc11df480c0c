import { isMainThread, parentPort, worker<PERSON><PERSON>, Worker } from "worker_threads";
import ffmpeg from "fluent-ffmpeg";
import fs from "fs";
import path from "path";
import sharp from "sharp";
import { cameraService } from "../camera";
import fsPromises from "fs/promises";

if (!isMainThread) {
  let timelapseFrames: string[] = [];
  let currentFrame = 0;
  let currentFrameName = 1;

  const ffmpegPath = path.join(__dirname, "ffmpeg.exe");

  if (!fs.existsSync(ffmpegPath)) {
    console.error("FFmpeg binary not found at:", ffmpegPath);
  } else {
    console.log("FFmpeg binary found at:", ffmpegPath);
    ffmpeg.setFfmpegPath(ffmpegPath);
  }

  const saveFrame = async (payload: string, isBase64 = true) => {
    currentFrame++;
    const framePath = path.join(
      workerData.timelapseFolder,
      `frame_${String(currentFrameName).padStart(4, "0")}.jpg`
    );

    if (currentFrame % 4 !== 0) {
      return;
    }

    try {
      currentFrameName++;
      if (!fs.existsSync(workerData.timelapseFolder)) {
        await fsPromises.mkdir(workerData.timelapseFolder, { recursive: true });
      }

      if (isBase64) {
        const base64Data = payload.replace(/^data:image\/\w+;base64,/, "");
        const buffer = Buffer.from(base64Data, "base64");
        await sharp(buffer)
          .resize(960, 640, {
            fit: "cover",
            position: "center",
          })
          .flop()
          .toFile(framePath);
      } else {
        await sharp(payload)
          .resize(960, 640, {
            fit: "cover",
            position: "center",
          })
          .toFile(framePath);
      }

      timelapseFrames.push(framePath);
    } catch (error) {
      console.error("Error saving frame:", error);
    }
  };

  parentPort?.on("message", async (message) => {
    try {
      switch (message.action) {
        case "CAPTURE_PHOTO":
          try {
            console.log("[CameraWorker] Capturing photo");
            await cameraService.takePicture();
          } catch (e) {
            console.error("Error capturing photo:", e);

            // Phân tích lỗi để cung cấp thông tin chi tiết hơn
            let errorMessage = "Unknown error";
            let errorType = "GENERAL_ERROR";

            if (e instanceof Error) {
              errorMessage = e.message;

              // Kiểm tra các loại lỗi cụ thể
              if (
                errorMessage.includes("FOCUS_FAILED") ||
                errorMessage.includes("TAKE_PICTURE_AF_NG") ||
                errorMessage.includes("FOCUSING_POINT_NG")
              ) {
                errorType = "FOCUS_ERROR";
              } else if (errorMessage.includes("DEVICE_BUSY")) {
                errorType = "DEVICE_BUSY";
              } else if (
                errorMessage.includes("SESSION_NOT_OPEN") ||
                errorMessage.includes("COMM_DISCONNECTED")
              ) {
                errorType = "CONNECTION_ERROR";
              }
            }

            // Gửi thông tin lỗi chi tiết hơn
            parentPort?.postMessage({
              action: "PHOTO_CAPTURE_ERROR",
              data: {
                error: errorMessage,
                type: errorType,
              },
            });
          }
          break;
        case "REGISTER_CAPTURE_WORKSPACE":
          cameraService.registerCaptureWorkspace(
            message.baseDir,
            (localUrl: string, activeCameraIndex: number) => {
              parentPort?.postMessage({
                action: "PHOTO_CAPTURED",
                data: {
                  localUrl,
                  activeCameraIndex,
                },
              });

              saveFrame(localUrl, false);
            }
          );

          cameraService.events.on("photo:remove-background", (payload: any) => {
            parentPort?.postMessage({
              action: "REMOVE_BACKGROUND",
              data: payload,
            });
          });
          break;
        case "REGISTER_CAMERA_CONNECTION":
          cameraService.events.on("camera:not-connected", () => {
            parentPort?.postMessage({
              action: "CAMERA_NOT_CONNECTED",
            });
          });
          break;
        case "START_LIVE_VIEW":
          try {
            await cameraService.startLiveView(
              async (imageDataURL: any, activeCameraIndex: number) => {
                if (
                  typeof imageDataURL === "string" &&
                  imageDataURL.startsWith("data:image")
                ) {
                  // Gửi frame đến UI
                  parentPort?.postMessage({
                    action: "LIVE_VIEW_IMAGE",
                    data: {
                      imageDataURL,
                      activeCameraIndex,
                    },
                  });

                  // Lưu frame nếu cần
                  await saveFrame(imageDataURL);
                } else {
                  console.warn(
                    "[CameraWorker] Received invalid live view frame:",
                    typeof imageDataURL === "string"
                      ? imageDataURL.substring(0, 20) + "..."
                      : typeof imageDataURL
                  );
                }
              }
            );
          } catch (error) {
            console.error("Error starting live view:", error);

            // Thông báo lỗi để UI có thể hiển thị thông báo
            parentPort?.postMessage({
              action: "CAMERA_NOT_CONNECTED",
              data: { error: "Failed to start live view" },
            });
          }
          break;
        case "GET_CAMERAS":
          try {
            console.log("[CameraWorker] Getting camera list");

            // Nếu camera chưa được kết nối, thử kết nối tất cả các camera
            if (!cameraService.getCamera()) {
              console.log(
                "[CameraWorker] No camera connected, attempting to connect to all cameras"
              );

              // Sử dụng connectAllCameras để kết nối tất cả các camera cùng lúc
              await cameraService.connectAllCameras();
            }

            // Lấy danh sách camera
            const cameras = cameraService.getCameras();
            parentPort?.postMessage({
              action: "CAMERAS_LIST",
              data: cameras,
            });
          } catch (error) {
            console.error("[CameraWorker] Error getting cameras:", error);
          }
          break;
        case "SWITCH_CAMERA":
          try {
            console.log(
              `[CameraWorker] Switching to camera at index ${message.cameraIndex}`
            );
            const result = await cameraService.switchCamera(
              message.cameraIndex
            );
            const { success, alreadyConnected } = result;

            console.log(
              `[CameraWorker] Camera switch ${success ? "successful" : "failed"}, camera was ${alreadyConnected ? "already connected" : "not connected before"}`
            );

            // Gửi thông tin về việc camera đã được kết nối trước đó hay không
            parentPort?.postMessage({
              action: "CAMERA_SWITCHED",
              data: {
                success,
                cameraIndex: message.cameraIndex,
                alreadyConnected,
              },
            });
          } catch (error) {
            console.error("Error switching camera:", error);
            parentPort?.postMessage({
              action: "CAMERA_SWITCH_ERROR",
              data: { error: "Failed to switch camera" },
            });
          }
          break;

        case "CLEAN_CAMERA":
          try {
            await cameraService.cleanup();
          } catch (error) {
            console.error("Error during camera cleanup:", error);
          }
          break;
        case "START_TIMELAPSE":
          try {
            if (fs.existsSync(workerData.timelapseFolder)) {
              await fsPromises.rm(workerData.timelapseFolder, {
                recursive: true,
                force: true,
              });
            }
            await fsPromises.mkdir(workerData.timelapseFolder, {
              recursive: true,
            });
            timelapseFrames = [];
          } catch (error) {
            console.error("Error creating timelapse directory:", error);
          }
          break;
        case "STOP_TIMELAPSE":
          if (timelapseFrames.length === 0) {
            console.warn("No frames captured for timelapse");
            return;
          }

          parentPort?.postMessage({
            action: "MAKING_TIMELAPSE",
            data: {
              baseDir: message.baseDir,
              timelapseFolder: workerData.timelapseFolder,
              timelapseFrames,
              orderId: message.orderId,
              apiUrl: message.apiUrl,
              token: message.token,
              captureMode: message.captureMode,
            },
          });
          break;
        default:
          console.error("Unknown action:", message.action);
      }
    } catch (error) {
      console.error("Error handling message:", error);
    }
  });
}

process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
  // Cleanup resources if needed
});

process.on("unhandledRejection", (error) => {
  console.error("Unhandled Rejection:", error);
  // Cleanup resources if needed
});
