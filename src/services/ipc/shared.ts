import { MergePhotosToFrameParams } from "@/cores/types";
import dayjs from "dayjs";
import { app, BrowserWindow, ipcMain } from "electron";
import fsPromises from "fs/promises";
import path from "path";
import QRCode from "qrcode";
import sharp from "sharp";
import https from "https";
import _, { has } from "lodash";
import { Worker } from "worker_threads";
import fs from "fs";
import { getNoBackgroundFileFromUrl } from "@/cores/helpers";

// Hàm giám sát bộ nhớ
const logMemoryUsage = (label = "") => {
  const memoryUsage = process.memoryUsage();
  console.log(
    `Memory Usage ${label ? `(${label})` : ""}: RSS=${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB, HeapTotal=${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB, HeapUsed=${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`
  );
};

// Thêm cache cho image buffers
const imageBufferCache = new Map<string, Buffer>();

const _isUrl = (string: string) => {
  try {
    return string?.startsWith("http://") || string?.startsWith("https://");
  } catch {
    return false;
  }
};

const _fetchImage = async (
  url: string,
  retries = 5,
  timeout = 30000
): Promise<Buffer> => {
  try {
    // Ghi log bộ nhớ trước khi tải ảnh
    if (process.env.DEBUG_MEMORY === "true") {
      logMemoryUsage(`Before fetching image: ${url.substring(0, 30)}...`);
    }

    // Kiểm tra cache - chỉ sử dụng cache cho ảnh nhỏ hơn 1MB để tránh chiếm quá nhiều bộ nhớ
    const cachedBuffer = imageBufferCache.get(url);
    if (cachedBuffer) {
      // Kiểm tra kích thước cache để tránh lưu trữ quá nhiều ảnh lớn
      if (cachedBuffer.length < 1024 * 1024) {
        // Nhỏ hơn 1MB
        return cachedBuffer;
      } else {
        // Xóa cache cho ảnh lớn để giải phóng bộ nhớ
        imageBufferCache.delete(url);
      }
    }

    return new Promise<Buffer>((resolve, reject) => {
      const tryDownload = (useIPv6 = false) => {
        const options = {
          family: useIPv6 ? 6 : 4,
          timeout: timeout,
          headers: {
            Accept: "image/*",
            "Cache-Control": "no-cache",
          },
        };

        console.log(
          `Đang tải ảnh từ URL: ${url} (${useIPv6 ? "IPv6" : "IPv4"})`
        );

        const request = https.get(url, options, (response) => {
          if (response.statusCode !== 200) {
            reject(new Error(`HTTP error! status: ${response.statusCode}`));
            return;
          }

          const contentType = response.headers["content-type"];
          if (!contentType?.includes("image/")) {
            reject(new Error(`Invalid content type: ${contentType}`));
            return;
          }

          // Kiểm tra kích thước ảnh trước khi tải
          const contentLength = parseInt(
            response.headers["content-length"] || "0",
            10
          );
          if (contentLength > 10 * 1024 * 1024) {
            // Lớn hơn 10MB
            reject(new Error(`Image too large: ${contentLength} bytes`));
            return;
          }

          const chunks: Buffer[] = [];

          response.on("data", (chunk) => chunks.push(chunk));

          response.on("end", () => {
            const buffer = Buffer.concat(chunks);
            if (!buffer || buffer.length === 0) {
              reject(new Error("Empty image buffer received"));
              return;
            }
            console.log(
              `Tải ảnh thành công, kích thước: ${buffer.length} bytes`
            );

            // Chỉ cache ảnh nhỏ hơn 1MB
            if (buffer.length < 1024 * 1024) {
              imageBufferCache.set(url, buffer);
            }

            resolve(buffer);
          });
        });

        request.on("error", (error: any) => {
          if (!useIPv6 && error.code === "ETIMEDOUT") {
            console.log("IPv4 timeout, thử lại với IPv6...");
            tryDownload(true);
          } else {
            reject(error);
          }
        });

        // Tự động hủy request sau timeout
        request.setTimeout(timeout, () => {
          request.destroy(new Error("Request timeout"));
        });
      };

      tryDownload();
    }).catch(async (error) => {
      if (retries > 0) {
        const currentAttempt = 6 - retries;
        console.log(`Lần thử ${currentAttempt}/5 tải ảnh thất bại: ${url}`);
        console.log(`Lỗi: ${error.message}`);

        const delay = Math.min(1000 * Math.pow(2, currentAttempt), 15000);
        console.log(`Chờ ${delay}ms trước khi thử lại...`);
        await new Promise((resolve) => setTimeout(resolve, delay));

        return _fetchImage(url, retries - 1, timeout);
      }
      throw new Error(
        `Không thể tải ảnh sau 5 lần thử. URL: ${url}. Lỗi: ${error.message}`
      );
    });
  } catch (error: any) {
    console.error("Lỗi tải ảnh:", error);
    throw error;
  } finally {
    // Ghi log bộ nhớ sau khi tải ảnh
    if (process.env.DEBUG_MEMORY === "true") {
      logMemoryUsage(`After fetching image: ${url.substring(0, 30)}...`);
    }
  }
};

const _rotateImage: any = async (frame: any, processedImage: sharp.Sharp) => {
  try {
    // Validate input
    if (!frame || !processedImage) {
      throw new Error("Invalid frame or image for rotation");
    }

    if (!frame.width || !frame.height) {
      console.error("Invalid frame dimensions for rotation:", frame);
      throw new Error("Invalid frame dimensions for rotation");
    }

    // Normalize angle to 0-360 range
    const angle = _toNumber(frame.angle);
    const normalizedAngle = ((angle % 360) + 360) % 360;

    // First resize to exact frame dimensions with error handling
    let resizedBuffer;
    try {
      resizedBuffer = await processedImage
        .resize(frame.width, frame.height, {
          fit: "cover",
          position: "center",
          background: { r: 255, g: 255, b: 255, alpha: 0 },
        })
        .toBuffer();

      if (!resizedBuffer || resizedBuffer.length === 0) {
        throw new Error("Failed to resize image before rotation");
      }
    } catch (resizeError) {
      console.error("Error resizing image before rotation:", resizeError);
      throw new Error(
        `Failed to resize image before rotation: ${resizeError.message}`
      );
    }

    // Create new Sharp instance with error handling
    try {
      processedImage = sharp(resizedBuffer, { failOn: "none" });
    } catch (sharpError) {
      console.error("Error creating Sharp instance for rotation:", sharpError);
      throw new Error(
        `Failed to create Sharp instance for rotation: ${sharpError.message}`
      );
    }

    // Normalize angle to -180 to 180 range for easier calculations
    const normalizedAngleAfterResize = Math.round(
      (((normalizedAngle % 360) + 540) % 360) - 180
    );

    // Apply rotation around center with error handling
    try {
      processedImage = processedImage.rotate(normalizedAngleAfterResize, {
        background: { r: 255, g: 255, b: 255, alpha: 0 },
      });
    } catch (rotateError) {
      console.error("Error rotating image:", rotateError);
      throw new Error(`Failed to rotate image: ${rotateError.message}`);
    }

    // Get buffer and metadata with error handling
    let bufferProcessedImage, boxMetadata;
    try {
      bufferProcessedImage = await processedImage.toBuffer();

      if (!bufferProcessedImage || bufferProcessedImage.length === 0) {
        throw new Error("Failed to get buffer after rotation");
      }

      boxMetadata = await sharp(bufferProcessedImage, {
        failOn: "none",
      }).metadata();

      if (!boxMetadata || !boxMetadata.width || !boxMetadata.height) {
        throw new Error("Failed to get metadata after rotation");
      }
    } catch (metadataError) {
      console.error("Error getting metadata after rotation:", metadataError);
      throw new Error(
        `Failed to get metadata after rotation: ${metadataError.message}`
      );
    }

    // Calculate adjusted coordinates
    const adjustedX =
      frame.x_coordinate - (boxMetadata.width - frame.width) / 2;
    const adjustedY =
      frame.y_coordinate - (boxMetadata.height - frame.height) / 2;

    // Apply final processing with error handling
    try {
      processedImage = processedImage
        .withMetadata({ density: 300 })
        .sharpen({
          sigma: 0.3,
          m1: 0.1,
          m2: 0.2,
        })
        .png({
          quality: 90, // Giảm từ 100 để giảm sử dụng bộ nhớ
          compressionLevel: 6,
          adaptiveFiltering: true,
          force: true,
          palette: false,
        });

      // Get final buffer with error handling
      const finalBuffer = await processedImage.toBuffer();

      if (!finalBuffer || finalBuffer.length === 0) {
        throw new Error("Failed to get final buffer after rotation");
      }

      // Return the processed image with adjusted coordinates
      return {
        data: finalBuffer,
        position: {
          left: Math.round(adjustedX),
          top: Math.round(adjustedY),
        },
      };
    } catch (finalProcessingError) {
      console.error(
        "Error in final processing after rotation:",
        finalProcessingError
      );
      throw new Error(
        `Failed in final processing after rotation: ${finalProcessingError.message}`
      );
    }
  } catch (error) {
    console.error("Detailed error in rotation processing:", error);

    // Return a fallback result instead of throwing to prevent the entire process from failing
    // This allows the application to continue even if one image rotation fails
    console.log("Returning fallback for failed rotation");
    return {
      data: await processedImage.toBuffer(),
      position: {
        left: frame.x_coordinate,
        top: frame.y_coordinate,
      },
    };
  }
};

const _processImage = async (
  imagePath: string,
  frame: any,
  isMixed: boolean,
  filterType?: string,
  hasBackground?: boolean,
  backgroundBuffer?: any
) => {
  try {
    if (!imagePath) {
      console.error("_processImage: No image path provided");
      throw new Error("No image path provided");
    }

    console.log(
      `_processImage: Processing image: ${typeof imagePath === "string" ? imagePath.substring(0, 50) + "..." : "non-string input"}`
    );

    // Get buffer from URL, base64 or filesystem path
    let imageInput;
    const mainUrl = hasBackground
      ? getNoBackgroundFileFromUrl(imagePath)
      : imagePath;

    try {
      if (_isBase64Image(mainUrl)) {
        imageInput = _getBufferFromBase64(mainUrl);
      } else if (_isUrl(mainUrl)) {
        imageInput = await _fetchImage(mainUrl);
      } else {
        imageInput = await sharp(mainUrl, { failOn: "none" })
          .withMetadata()
          .toBuffer();
      }

      if (!imageInput || imageInput.length === 0) {
        throw new Error("Failed to get valid image buffer");
      }
    } catch (inputError) {
      console.error("Error getting image input:", inputError);
      throw new Error(`Failed to process image input: ${inputError.message}`);
    }

    // Validate frame dimensions
    if (!frame.width || !frame.height) {
      console.error("Invalid frame dimensions:", frame);
      throw new Error("Invalid frame dimensions");
    }

    const { width: frameWidth, height: frameHeight } = frame;

    // Get metadata with error handling
    let metadata;
    try {
      metadata = await sharp(imageInput, { failOn: "none" }).metadata();
      if (!metadata || !metadata.width || !metadata.height) {
        throw new Error("Invalid image metadata");
      }

      if (hasBackground) {
        const resizedBackground = await sharp(backgroundBuffer)
          .resize(metadata.width, metadata.height)
          .toBuffer();

        imageInput = await sharp(resizedBackground)
          .composite([
            {
              input: imageInput,
              blend: "over",
            },
          ])
          .png()
          .toBuffer();
      }
    } catch (metadataError) {
      console.error("Error getting image metadata:", metadataError);
      throw new Error(`Failed to get image metadata: ${metadataError.message}`);
    }

    const isVerticalFrame = frameHeight > frameWidth;

    const { extractOptions } = _calculateDimensions(
      metadata,
      frameWidth,
      frameHeight,
      isVerticalFrame
    );

    // Create Sharp instance with error handling
    let processedImage;
    try {
      processedImage = sharp(imageInput, { failOn: "none" });

      if (extractOptions) {
        processedImage = processedImage.extract(extractOptions);
      }

      if (filterType) {
        switch (filterType) {
          case "mono":
            processedImage = processedImage.grayscale(true);
            break;
          case "mix":
            if (isMixed) {
              processedImage = processedImage.grayscale(true);
            }
            break;
          case "vivid_warm":
            processedImage = processedImage
              .modulate({
                brightness: 0.9,
                saturation: 1.2,
              })
              .linear(1.3, -50);
            break;
          case "vivid_cool":
            processedImage = processedImage.modulate({
              brightness: 0.9,
              saturation: 0.8,
              hue: 10,
            });
            break;
          case "dramatic":
            processedImage = processedImage
              .modulate({
                brightness: 0.9,
                saturation: 0.8,
                hue: -10,
              })
              .linear(1.45, -50)
              .recomb([
                [0.94, 0.06, 0.02],
                [0.05, 0.93, 0.02],
                [0.02, 0.05, 0.93],
              ]);
            break;
        }
      }
    } catch (processingError) {
      console.error("Error during image processing:", processingError);
      throw new Error(
        `Failed during image processing: ${processingError.message}`
      );
    }

    // Handle rotation if needed
    if (_.isNumber(frame.angle) && frame.angle !== 0) {
      try {
        return await _rotateImage(frame, processedImage);
      } catch (rotationError) {
        console.error("Error rotating image:", rotationError);
        throw new Error(`Failed to rotate image: ${rotationError.message}`);
      }
    }

    // Final image processing
    try {
      const finalImage = processedImage
        .resize(frameWidth, frameHeight, {
          fit: "cover",
          withoutEnlargement: true,
          kernel: sharp.kernel.cubic,
          position: "center",
          background: { r: 255, g: 255, b: 255, alpha: 1 },
          fastShrinkOnLoad: false,
        })
        .withMetadata({
          density: 300,
        })
        .sharpen({
          sigma: 0.3,
          m1: 0.1,
          m2: 0.2,
        })
        .png({
          quality: 100,
          compressionLevel: 6, // Reduced from 9 to avoid potential memory issues
          adaptiveFiltering: true,
          force: true,
          palette: false, // Đảm bảo không giới hạn bảng màu
        });

      return await finalImage.toBuffer({ resolveWithObject: true });
    } catch (finalProcessingError) {
      console.error("Error in final image processing:", finalProcessingError);
      throw new Error(
        `Failed in final image processing: ${finalProcessingError.message}`
      );
    }
  } catch (error) {
    console.error("Error in _processImage:", error);
    throw error;
  }
};

const _calculateDimensions = (
  metadata: any,
  frameWidth: number,
  frameHeight: number,
  isVerticalFrame: boolean
) => {
  return isVerticalFrame
    ? _calculateVerticalDimensions(metadata, frameWidth, frameHeight)
    : _calculateHorizontalDimensions(metadata, frameWidth, frameHeight);
};

const _calculateVerticalDimensions = (
  metadata: any,
  frameWidth: number,
  frameHeight: number
) => {
  const originalHeight = metadata.height;
  const targetWidth = Math.floor(originalHeight * (frameWidth / frameHeight));

  const extractOptions =
    targetWidth < metadata.width
      ? {
          left: Math.floor((metadata.width - targetWidth) / 2),
          top: 0,
          width: targetWidth,
          height: originalHeight,
        }
      : null;

  return {
    extractOptions,
    targetDimensions: { width: targetWidth, height: originalHeight },
  };
};

const _calculateHorizontalDimensions = (
  metadata: any,
  frameWidth: number,
  frameHeight: number
) => {
  const originalWidth = metadata.width;
  const targetHeight = Math.floor(originalWidth * (frameHeight / frameWidth));

  const extractOptions =
    targetHeight < metadata.height
      ? {
          left: 0,
          top: Math.floor((metadata.height - targetHeight) / 2),
          width: originalWidth,
          height: targetHeight,
        }
      : null;

  return {
    extractOptions,
    targetDimensions: { width: originalWidth, height: targetHeight },
  };
};

const calculateStickerPosition = (
  sticker: { x: number; y: number },
  viewWidth: number,
  viewHeight: number,
  frameWidth: number,
  frameHeight: number,
  stickerSize: number
) => {
  const scaleRatioX = frameWidth / viewWidth;
  const scaleRatioY = frameHeight / viewHeight;

  // Chuyển từ centered sang top-left trong hệ toạ độ view
  const viewTopLeftX = sticker.x + viewWidth / 2;
  const viewTopLeftY = sticker.y + viewHeight / 2;

  // Scale lên kích thước frame thực tế và căn giữa sticker
  const finalX = Math.round(viewTopLeftX * scaleRatioX - stickerSize / 2);
  const finalY = Math.round(viewTopLeftY * scaleRatioY - stickerSize / 2);

  return { finalX, finalY };
};

const _convertAppUrlToPath = (urlString: string) => {
  try {
    if (urlString.startsWith("app:///")) {
      // Remove 'app:///' and decode the remaining path
      const pathPart = decodeURIComponent(urlString.replace("app:///", ""));
      // Convert to proper file system path
      return path.normalize(pathPart);
    }
    return urlString;
  } catch (error) {
    console.error("Error converting app URL:", error);
    throw error;
  }
};

const _isBase64Image = (str: string) => {
  try {
    return str.startsWith("data:image/");
  } catch {
    return false;
  }
};

const _getBufferFromBase64 = (base64String: string) => {
  // Remove data URL prefix if present
  const base64Data = base64String.replace(/^data:image\/\w+;base64,/, "");
  return Buffer.from(base64Data, "base64");
};

// Cải thiện hàm cleanup để giải phóng bộ nhớ hiệu quả hơn
const cleanup = (layers: any) => {
  // Xóa cache để giải phóng bộ nhớ
  imageBufferCache.clear();

  // Nếu không có layers, không cần xử lý thêm
  if (!layers || !Array.isArray(layers)) return;

  // Xóa tham chiếu đến tất cả các buffer để GC có thể thu hồi
  layers.forEach((layer: any) => {
    if (!layer) return;

    if (layer.input instanceof Buffer) {
      layer.input = null;
    }

    // Xóa các thuộc tính khác có thể chứa dữ liệu lớn
    if (layer.raw) layer.raw = null;
    if (layer.data) layer.data = null;
  });

  // Gợi ý cho garbage collector
  if (global.gc) {
    try {
      global.gc();
    } catch (e) {
      console.log("Failed to force garbage collection:", e);
    }
  }
};

const _toNumber = (value: any) => {
  if (_.isNumber(value)) {
    return value;
  }
  const v = Number(value);
  return _.isNaN(v) ? 0 : v;
};

const mergePhotosToFrame = async (
  _: any,
  {
    framePath,
    photoMaps,
    positions,
    filename,
    filterType,
    stickers,
    dates = [],
    viewWidth,
    viewHeight,
    qrCodes = [],
    qrUrl,
    dir,
    dateColor,
    background,
  }: MergePhotosToFrameParams
) => {
  // Ghi log bộ nhớ khi bắt đầu
  logMemoryUsage("mergePhotosToFrame start");

  let layers: any[] = [];
  let withoutQrLayers: any[] = [];

  try {
    // Convert app:/// URL to file system path if necessary
    const resolvedFramePath = _convertAppUrlToPath(framePath);
    console.log("Resolved frame path:", resolvedFramePath);

    // Thêm try-catch riêng cho việc đọc frame
    let frameBuffer;
    try {
      frameBuffer = _isBase64Image(resolvedFramePath)
        ? _getBufferFromBase64(resolvedFramePath)
        : _isUrl(resolvedFramePath)
          ? await _fetchImage(resolvedFramePath)
          : await sharp(resolvedFramePath).toBuffer(); // Đọc file trực tiếp thành buffer
    } catch (frameError) {
      console.error("[mergePhotosToFrame] Error loading frame:", frameError);
      throw new Error(`Failed to load frame: ${frameError.message}`);
    }

    // Kiểm tra buffer hợp lệ
    if (!frameBuffer || frameBuffer.length === 0) {
      console.error("[mergePhotosToFrame] Invalid frame buffer");
      throw new Error("Invalid frame buffer");
    }

    // Kiểm tra metadata của frame
    let frameMetadata;
    try {
      frameMetadata = await sharp(frameBuffer).metadata();
      console.log("[mergePhotosToFrame] Frame metadata:", frameMetadata);
    } catch (metadataError) {
      console.error(
        "[mergePhotosToFrame] Error getting frame metadata:",
        metadataError
      );
      throw new Error(`Failed to get frame metadata: ${metadataError.message}`);
    }

    let qrCodePromises: any[] = [];
    let stickerPromises: any[] = [];
    let datePromises: any[] = [];

    const baseDir = path.join(app.getPath("userData"), dir);
    await fsPromises.mkdir(baseDir, { recursive: true });
    const localPath = path.join(baseDir, filename);
    const withoutQRLocalPath = path.join(baseDir, `without-qr-${filename}`);
    const hasBackground = background?.fileUrl?.includes("https");
    let backgroundBuffer: any;

    if (hasBackground) {
      backgroundBuffer = await _fetchImage(background?.fileUrl);
    }

    // Xử lý ảnh song song
    const photoPromises = positions.map(async (frameItem) => {
      try {
        const { path } = photoMaps[frameItem.id] ?? {};
        const isMixed = frameItem.position % 2 === 0;

        const { data: resizedPhoto, position } = await _processImage(
          path,
          frameItem,
          isMixed,
          filterType,
          hasBackground,
          backgroundBuffer
        );

        return {
          input: resizedPhoto,
          top: position ? Math.round(position.top) : frameItem.y_coordinate,
          left: position ? Math.round(position.left) : frameItem.x_coordinate,
        };
      } catch (err) {
        console.error(`[mergePhotosToFrame] Error processing photo:`, err);
        return null;
      }
    });

    datePromises =
      dates?.map(async (date) => {
        const w = date.width || 126;
        const h = date.height ? date.height * 1.5 : 30;

        const svgText = `
          <svg width="${w}" height="${h}">
            <text x="0%" y="80%" dominant-baseline="middle" style="fill:${dateColor};font-size:20px;font-family:Arial;">${dayjs().format("DD.MM.YYYY")}</text>
          </svg>
        `;

        const textBuffer = Buffer.from(svgText);
        const textProcessed = sharp(textBuffer);

        const { data: textProcessedBuffer, position } = await _rotateImage(
          date,
          textProcessed
        );
        return {
          input: textProcessedBuffer,
          top: position.top,
          left: position.left,
        };
      }) ?? [];

    if (qrCodes?.length > 0 && qrUrl) {
      qrCodePromises = qrCodes?.map(async (qrCode) => {
        const wQR = qrCode.width || 100;
        const hQR = qrCode.height || 100;

        const qrCodeImage = await QRCode.toDataURL(qrUrl, {
          margin: 1,
          width: wQR,
        });

        const qrBuffer = _getBufferFromBase64(qrCodeImage);
        const resizedQrCode = sharp(qrBuffer).resize(wQR, hQR, {
          fit: "cover",
          background: { r: 255, g: 255, b: 255, alpha: 0 },
        });

        const { data: resizedQrCodeBuffer, position } = await _rotateImage(
          qrCode,
          resizedQrCode
        );
        return {
          input: resizedQrCodeBuffer,
          top: position.top,
          left: position.left,
        };
      });
    }

    // Xử lý stickers song song
    stickerPromises =
      stickers?.map(async (sticker) => {
        try {
          const stickerRotation = _toNumber(sticker.rotation);
          const stickerPath = _convertAppUrlToPath(sticker.image);
          const stickerBuffer = _isUrl(stickerPath)
            ? await _fetchImage(stickerPath)
            : stickerPath;

          const stickerSize = sticker.width || 50;
          const scaledSize = Math.round(
            stickerSize * (frameMetadata.width / viewWidth)
          );
          const { finalX, finalY } = calculateStickerPosition(
            sticker,
            viewWidth,
            viewHeight,
            frameMetadata.width,
            frameMetadata.height,
            scaledSize
          );

          const resizedSticker = await sharp(stickerBuffer)
            .resize(scaledSize, scaledSize)
            .rotate(stickerRotation)
            .toBuffer();

          return {
            input: resizedSticker,
            top: finalY,
            left: finalX,
          };
        } catch (err) {
          console.error(`[mergePhotosToFrame] Error processing sticker:`, err);
          return null;
        }
      }) ?? [];

    // Xử lý ảnh theo lô để giảm sử dụng bộ nhớ đỉnh điểm
    // Xử lý từng loại một thay vì tất cả cùng lúc
    console.log(
      "[mergePhotosToFrame] Processing photos in batches to reduce memory usage"
    );

    // Xử lý ảnh chính trước
    logMemoryUsage("Before processing photos");
    const photoLayers = await Promise.all(photoPromises);
    logMemoryUsage("After processing photos");

    // Giải phóng bộ nhớ sau khi xử lý xong ảnh
    if (global.gc) {
      try {
        global.gc();
      } catch (e) {
        console.log("Failed to force garbage collection after photos:", e);
      }
    }
    logMemoryUsage("After GC (photos)");

    // Xử lý stickers
    const stickerLayers = await Promise.all(stickerPromises);

    // Xử lý QR codes
    const qrCodeLayers = await Promise.all(qrCodePromises);

    // Xử lý dates
    const dateLayers = await Promise.all(datePromises);

    // Kết hợp tất cả layers và lọc bỏ các layer null/undefined
    layers = [
      ...(photoLayers.filter(Boolean) as any[]),
      { input: frameBuffer },
      ...(stickerLayers.filter(Boolean) as any[]),
      ...dateLayers,
      ...qrCodeLayers,
    ];

    // Validate all layers before compositing
    const validLayers = layers.filter((layer) => {
      if (!layer || !layer.input || !(layer.input instanceof Buffer)) {
        console.log("[mergePhotosToFrame] Skipping invalid layer:", layer);
        return false;
      }
      return true;
    });

    console.log(
      `[mergePhotosToFrame] Compositing ${validLayers.length} valid layers`
    );

    // Tạo composite một lần with error handling
    try {
      const composite = sharp(frameBuffer, { failOn: "none" })
        .composite(validLayers)
        .withMetadata({ density: 300 })
        .sharpen({
          sigma: 0.3,
          m1: 0.1,
          m2: 0.2,
        });

      // Save as PNG with optimized settings
      await composite
        .png({
          quality: 90, // Giảm từ 100 để giảm sử dụng bộ nhớ
          compressionLevel: 6,
          adaptiveFiltering: true,
          force: true,
          palette: false,
        })
        .toFile(localPath);
    } catch (compositeError) {
      console.error(
        "[mergePhotosToFrame] Error during composite operation:",
        compositeError
      );
      throw new Error(`Failed to composite images: ${compositeError.message}`);
    }

    if (qrCodes?.length && dates?.length) {
      withoutQrLayers = [
        ...(photoLayers.filter(Boolean) as any[]),
        { input: frameBuffer },
        ...(stickerLayers.filter(Boolean) as any[]),
      ];

      // Validate withoutQrLayers
      const validWithoutQrLayers = withoutQrLayers.filter((layer) => {
        if (!layer || !layer.input || !(layer.input instanceof Buffer)) {
          console.log(
            "[mergePhotosToFrame] Skipping invalid layer in withoutQrLayers:",
            layer
          );
          return false;
        }
        return true;
      });

      try {
        // Create a new sharp instance for the withoutQR version
        await sharp(frameBuffer, { failOn: "none" })
          .composite(validWithoutQrLayers)
          .withMetadata({ density: 300 })
          .toFormat("png")
          .png({
            quality: 90, // Giảm từ 100 để giảm sử dụng bộ nhớ
            compressionLevel: 6,
            adaptiveFiltering: true,
            force: true,
            palette: false,
          })
          .toFile(withoutQRLocalPath);
      } catch (withoutQrError) {
        console.error(
          "[mergePhotosToFrame] Error creating withoutQR image:",
          withoutQrError
        );
        // Don't throw here, just log the error and continue
      }

      return {
        localPath,
        withoutQRLocalPath,
      };
    }

    logMemoryUsage("Before returning result");
    return {
      localPath,
      withoutQRLocalPath: localPath,
    };
  } catch (error) {
    logMemoryUsage("Error occurred");
    console.error(
      "[mergePhotosToFrame] Error merging photos with clipping:",
      error
    );
    console.error("[mergePhotosToFrame] Error details:", error.stack);

    throw error;
  } finally {
    logMemoryUsage("Before cleanup");
    cleanup(layers);
    cleanup(withoutQrLayers);
    logMemoryUsage("After cleanup");
  }
};

type UpdateOrderParams = {
  params: {
    photos: string[];
    finalUrl: string;
    orderId: string;
    captureMode: string;
    apiUrl: string;
    accessToken: string;
  };
};

const initUpdateOrder = async () => {
  ipcMain.handle(
    "update-order",
    async (event, { params }: UpdateOrderParams) => {
      const baseDir = path.join(app.getPath("userData"), "temp");
      const orderDir = path.join(
        app.getPath("userData"),
        "orders",
        params.orderId
      );

      const workerPath = path.join(__dirname, "zip-photo.worker.js");
      const worker = new Worker(workerPath, {
        workerData: {
          params: {
            ...params,
            baseDir,
            orderDir,
          },
        },
      });

      worker.on("error", (err) => {
        console.error("Worker error:", err);
      });

      worker.on("exit", (code) => {
        console.log(`Worker exited with code ${code}`);
      });
    }
  );
};

// Hàm định kỳ giải phóng bộ nhớ
const setupMemoryMonitoring = () => {
  // Ghi log bộ nhớ ban đầu
  logMemoryUsage("Initial memory state");

  // Định kỳ giải phóng bộ nhớ và ghi log
  const memoryMonitoringInterval = setInterval(() => {
    logMemoryUsage("Periodic check");

    // Xóa cache để giải phóng bộ nhớ
    imageBufferCache.clear();

    // Gợi ý cho garbage collector
    if (global.gc) {
      try {
        global.gc();
        logMemoryUsage("After GC");
      } catch (e) {
        console.log("Failed to force garbage collection:", e);
      }
    }
  }, 60000); // Mỗi phút

  return memoryMonitoringInterval;
};

const initReupload = async () => {
  ipcMain.handle(
    "reupload-files",
    async (event, { orderId, apiUrl, accessToken }) => {
      try {
        const baseDir = path.join(app.getPath("userData"), "temp");
        const workerPath = path.join(__dirname, "reupload.worker.js");
        const orderDir = path.join(app.getPath("userData"), "orders", orderId);

        console.log("[ReuploadWorker] Worker path:", workerPath);
        console.log("[ReuploadWorker] Base dir:", baseDir);
        console.log("[ReuploadWorker] Order dir:", orderDir);

        const worker = new Worker(workerPath, {
          workerData: {
            baseDir,
            orderId,
            apiUrl,
            orderDir,
            accessToken,
          },
        });

        worker.on("error", (err) => {
          console.error("[ReuploadWorker] Worker error:", err);
        });

        worker.on("exit", (code) => {
          console.log(`[ReuploadWorker] Worker exited with code ${code}`);
        });
      } catch (error) {
        console.error("[ReuploadWorker] Error initializing worker:", error);
      }
    }
  );
};

const initShared = async (mainWindow: BrowserWindow) => {
  // Thiết lập giám sát bộ nhớ
  const memoryMonitoringInterval = setupMemoryMonitoring();

  // Đảm bảo dừng giám sát khi cửa sổ đóng
  mainWindow.on("closed", () => {
    clearInterval(memoryMonitoringInterval);
  });
};

const initSharedWithoutWindow = async () => {
  ipcMain.handle("merge-photos-to-frame", mergePhotosToFrame);
  ipcMain.handle("get-temp-path", () => app.getPath("temp"));

  await initUpdateOrder();
  await initReupload();
};

export { initShared, initSharedWithoutWindow, UpdateOrderParams };
