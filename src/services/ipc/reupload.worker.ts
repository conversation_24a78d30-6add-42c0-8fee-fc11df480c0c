import path from "path";
import { isMainThread, workerData } from "worker_threads";
import archiver from "archiver";
import { createWriteStream } from "fs";
import { retryUpload } from "@/cores/utils/upload";
import fsPromises from "fs/promises";
import { ECaptureMode } from "@/cores/enums";
import sharp from "sharp";

const CLIENT_APP_UPDATE_ORDER = `
  mutation clientAppCreateImage($input: CreateImageInput!, $file: Upload!) {
    clientAppCreateImage(input: $input, file: $file) {
      message
      captureMode
      domain
    }
  }
`;

const CLIENT_APP_UPDATE_TIMELAPSE_VIDEO = `
  mutation clientAppCreateTimeLapse(
    $input: CreateImageInput!
    $file: Upload!
  ) {
    clientAppCreateTimeLapse(input: $input, file: $file) {
      isSuccess
    }
  }
`;

if (!isMainThread) {
  const execute = async () => {
    try {
      const { baseDir, orderDir, orderId, apiUrl, accessToken } = workerData;

      const files = await fsPromises.readdir(orderDir);
      const pictures = files.filter((file) => file.startsWith("flipped_"));
      const finalFile = files.find(
        (file) =>
          file.startsWith("without-qr-merged-") || file.startsWith("merged-")
      );

      const timelapseFile = files.find((file) => file.startsWith("timelapse_"));
      if (timelapseFile) {
        try {
          const timelapseFilePath = path.join(orderDir, timelapseFile);

          await retryUpload({
            orderId,
            captureMode: ECaptureMode.AUTO,
            fileUrl: timelapseFilePath,
            apiUrl,
            accessToken,
            workerName: "ReuploadWorker",
            query: CLIENT_APP_UPDATE_TIMELAPSE_VIDEO,
          });

          console.log(
            `[ReuploadWorker] Timelapse file ${timelapseFile} uploaded successfully`
          );
        } catch (error) {
          console.error("Error uploading timelapse file:", error);
        }
      }

      if (pictures.length > 0) {
        const timestamp = Date.now();
        const zipPath = path.join(baseDir, `order-${timestamp}.zip`);
        const output = createWriteStream(zipPath);
        const archive = archiver("zip", {
          zlib: { level: 9 },
        });

        archive.on("progress", (progress: any) => {
          console.log("Archive progress:", progress);
        });

        output.on("close", async () => {
          console.log("Archive created:", zipPath);
          try {
            await retryUpload({
              orderId,
              captureMode: ECaptureMode.AUTO,
              fileUrl: zipPath,
              apiUrl,
              accessToken,
              workerName: "ReuploadWorker",
              query: CLIENT_APP_UPDATE_ORDER,
            });

            console.log("[ReuploadWorker] Images uploaded successfully");
          } catch (error) {
            console.error("Error uploading:", error);
            throw error;
          } finally {
            process.exit(0);
          }
        });

        archive.on("warning", (err: any) => {
          console.warn("Archive warning:", err);
        });

        archive.on("error", (err: any) => {
          console.error("Archive error:", err);
          throw err;
        });

        archive.pipe(output);

        for (const photo of pictures) {
          try {
            const photoPath = path.join(orderDir, photo);
            const compressedBuffer = await sharp(photoPath)
              .webp({ quality: 90 })
              .resize(1500, 1000, { fit: "cover" })
              .toBuffer();

            archive.append(compressedBuffer, { name: path.basename(photo) });
          } catch (err) {
            console.error("Error processing photo:", photo, err);
            throw err;
          }
        }

        if (finalFile) {
          const finalFilePath = path.join(orderDir, finalFile);
          archive.file(finalFilePath, { name: path.basename(finalFilePath) });
        }

        await archive.finalize();
      }
    } catch (error) {
      console.error("[ReuploadWorker] Error:", error);
      process.exit(1);
    }
  };

  execute();
}
