import { EventEmitter } from "events";

export class CameraServiceError extends <PERSON>rror {
  constructor(
    message: string,
    public readonly code: CameraErrorCode
  ) {
    super(message);
    this.name = "CameraServiceError";
  }
}

export enum CameraErrorCode {
  NO_CAMERA = "NO_CAMERA",
  CONNECTION_ERROR = "CONNECTION_ERROR",
  LIVE_VIEW_ERROR = "LIVE_VIEW_ERROR",
  LIVE_VIEW_NOT_SUPPORTED = "LIVE_VIEW_NOT_SUPPORTED",
  LIVE_VIEW_START_ERROR = "LIVE_VIEW_START_ERROR",
  CAPTURE_ERROR = "CAPTURE_ERROR",
  DOWNLOAD_ERROR = "DOWNLOAD_ERROR",
  DEVICE_BUSY = "DEVICE_BUSY",
  PROCESSING_ERROR = "PROCESSING_ERROR",
  NOT_SUPPORTED = "NOT_SUPPORTED",
  FOCUS_ERROR = "FOCUS_ERROR",
}

export interface CameraConfig {
  liveViewFPS: number;
  imageQuality: any;
  maxFrameDrops: number;
  bufferSize: number;
}

export type CameraEventHandler = (
  imageDataUrl: string,
  activeCameraIndex: number
) => void;

export interface CameraEventMap {
  "camera:connected": void;
  "camera:property": any;
  "liveview:start": void;
  "liveview:stop": void;
  "liveview:frame": string;
  "photo:captured": string;
  "photo:remove-background": any;
  error: Error;
  "camera:not-connected": Error;
  "cameras:list": CameraInfo[];
  "camera:switched": number | { index: number; alreadyConnected: boolean };
  "camera:countdownUpdate": number;
}

export interface CameraInfo {
  index: number;
  name: string;
}

export enum EvfMode {
  Quick = 0x00,
  Live = 0x01,
  LiveFace = 0x02,
  LiveMulti = 0x03,
  LiveZone = 0x04,
  LiveSingleExpandCross = 0x05,
  LiveSingleExpandAround = 0x06,
  LiveZoneLargeH = 0x07,
  LiveZoneLargeV = 0x08,
  LiveCatchAF = 0x09,
  LiveSpotAF = 0x0a,
  FlexibleZone1 = 0x0b,
  FlexibleZone2 = 0x0c,
  FlexibleZone3 = 0x0d,
  WholeArea = 0x0e,
  NoTraking_Spot = 0x0f,
  NoTraking_1Point = 0x10,
  NoTraking_ExpandCross = 0x11,
  NoTraking_ExpandAround = 0x12,
}

export class TypedEventEmitter {
  private eventEmitter = new EventEmitter();
  public removeAllListeners(): void {
    this.eventEmitter.removeAllListeners();
  }

  public emit<K extends keyof CameraEventMap>(
    event: K,
    data?: CameraEventMap[K]
  ): boolean {
    return this.eventEmitter.emit(event, data);
  }

  public on<K extends keyof CameraEventMap>(
    event: K,
    listener: (data: CameraEventMap[K]) => void
  ): void {
    this.eventEmitter.on(event, listener);
  }

  public off<K extends keyof CameraEventMap>(
    event: K,
    listener: (data: CameraEventMap[K]) => void
  ): void {
    this.eventEmitter.off(event, listener);
  }
}
