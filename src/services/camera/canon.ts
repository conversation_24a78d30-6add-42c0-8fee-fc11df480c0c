import {
  <PERSON><PERSON><PERSON><PERSON>,
  CameraB<PERSON>er,
  CameraProperty,
  Camera,
  watchCameras,
  FileChangeEvent,
  ImageQuality,
  Option,
  /* @ts-ignore */
} from "@vinacogroup/napi-canon-cameras";

// Đ<PERSON><PERSON> nghĩa interface cho LiveViewImage
interface LiveViewImage {
  getDataURL(): string;
}

import {
  CameraServiceError,
  CameraErrorCode,
  CameraConfig,
  CameraEventHandler,
  TypedEventEmitter,
  CameraInfo,
} from "./type";

// @ts-ignore
import sharp from "sharp";
import { BaseCameraService } from "./base";
import { getConfig } from "@/cores/utils/config";

const DEFAULT_CONFIG: CameraConfig = {
  liveViewFPS: 30, // Giảm xuống 30 FPS
  imageQuality: ImageQuality.ID.LargeJPEGFine,
  maxFrameDrops: 2, // Số frame có thể bỏ qua liên tiếp
  bufferSize: 2, // Số frame buffer
};

const DELAYS = {
  CONNECT: 100,
  LIVE_VIEW_START: 100,
  LIVE_VIEW_STOP: 100,
  PHOTO_CAPTURE: 100,
  PROPERTY_SET: 100,
} as const;

const config = getConfig();

// Định nghĩa interface cho thông tin camera đã kết nối
interface CameraState {
  isCapturing: boolean;
  lastCaptureTime: number;
  isReady: boolean;
}

interface ConnectedCamera {
  camera: Camera;
  isLiveViewActive: boolean;
  liveViewCallback: CameraEventHandler | null;
  state: CameraState;
}

interface CameraEventMap {
  "camera:connected": { index: number; success: boolean };
  "camera:disconnected": { index: number };
  "camera:switched": { index: number; alreadyConnected: boolean };
  "camera:error": { error: Error };
  "camera:liveViewStarted": void;
  "camera:liveViewStopped": void;
  "camera:liveViewFrame": { image: string };
  "camera:photoCaptured": { localUrl: string; position: number };
  "camera:countdownUpdate": number;
}

class CanonService extends BaseCameraService {
  private static instance: CanonService;
  private cameras: Camera[] = []; // Danh sách tất cả camera được phát hiện
  private connectedCameras: Map<number, ConnectedCamera> = new Map(); // Danh sách camera đã kết nối
  private camera: Camera | null = null; // Camera hiện tại đang được sử dụng (active camera)
  private activeCameraIndex = 0; // Chỉ số của camera hiện tại
  public events: TypedEventEmitter;
  protected config: CameraConfig;
  private photoCount = 0;
  private liveViewCallback: CameraEventHandler;
  private globalEventHandler: ((eventName: string, event: any) => void) | null =
    null;
  private currentPhotoCallback:
    | ((eventName: string, localUrl: string) => void)
    | null = null;
  private isCleaningUp = false;
  private workspace: string;
  private connectionRetryCount = 0;
  private readonly MAX_CONNECTION_RETRIES: number = 3;
  private readonly CAPTURE_COOLDOWN = 2000; // 2 seconds cooldown between captures
  private readonly SWITCH_TIMEOUT = 5000; // 5 seconds timeout for camera switch
  private sharedCountdown = 0;
  private isSwitchingCamera = false;

  private constructor() {
    super();

    console.log("[CameraService] Initializing service");
    this.events = new TypedEventEmitter();
    this.config = DEFAULT_CONFIG;
    this.initialize();
  }

  public static getInstance(): CanonService {
    if (!CanonService.instance) {
      CanonService.instance = new CanonService();
    }
    return CanonService.instance;
  }

  protected initialize(): void {
    console.log("[CanonService] Setting up service");
    this.setupEventListeners();
    this.setupCleanup();
  }

  private setupCleanup(): void {
    process.on("SIGINT", () => {
      this.cleanup();
      process.exit();
    });
  }

  private setupEventListeners(): void {
    console.log("[CanonService] Setting up event listeners.");
    const globalEventHandler = (eventName: string, event: any) => {
      // Only log specific important events
      const importantEvents = [
        CameraBrowser.EventName.LiveViewStart,
        CameraBrowser.EventName.LiveViewStop,
        Camera.EventName.FileCreate,
        Camera.EventName.DownloadRequest,
        CameraBrowser.EventName.CameraConnect,
        CameraBrowser.EventName.CameraDisconnect,
      ];

      if (!importantEvents.includes(eventName)) {
        return;
      }

      // For PropertyChangeValue, only log if it's the EVF output device
      if (eventName === CameraBrowser.EventName.PropertyChangeValue) {
        if (event.property.identifier === CameraProperty.ID.Evf_OutputDevice) {
          this.events.emit("camera:property", event.property);
        }
      }

      try {
        switch (eventName) {
          case CameraBrowser.EventName.LiveViewStart:
            this.events.emit("liveview:start");
            break;
          case CameraBrowser.EventName.LiveViewStop:
            this.events.emit("liveview:stop");
            break;
          case Camera.EventName.FileCreate:
          case Camera.EventName.DownloadRequest:
            this.currentPhotoCallback?.(eventName, event);
            break;
        }
      } catch (error) {
        this.handleError(error);
      }
    };

    this.globalEventHandler = globalEventHandler;
  }

  private handleError(error: Error): void {
    console.error("[CameraService] Error:", error);
    this.events.emit("error", error);
  }

  /**
   * Kiểm tra xem có thể kết nối với camera không
   * @param cameraIndex Chỉ số của camera cần kiểm tra (nếu không cung cấp, kiểm tra khả năng kết nối nói chung)
   * @returns true nếu có thể kết nối, false nếu không
   */
  private canConnect(cameraIndex?: number): boolean {
    if (this.isCleaningUp) {
      console.log("[CameraService] Cannot connect while cleaning up");
      return false;
    }

    // Nếu không cung cấp cameraIndex, kiểm tra khả năng kết nối nói chung
    if (cameraIndex === undefined) {
      return true;
    }

    // Kiểm tra xem camera đã được kết nối chưa
    if (this.connectedCameras.has(cameraIndex)) {
      // Camera đã được kết nối, nhưng vẫn cho phép kết nối lại
      console.log(
        `[CameraService] Camera at index ${cameraIndex} already connected, but allowing reconnection`
      );
      return true;
    }

    return true;
  }

  /**
   * Lấy danh sách camera từ cameraBrowser
   * @returns true nếu tìm thấy camera, false nếu không
   */
  private fetchCameraList(): boolean {
    try {
      this.cameras = cameraBrowser.getCameras();
      console.log(`[CameraService] Found ${this.cameras.length} cameras`);

      if (this.cameras.length === 0) {
        console.error("[CameraService] No cameras found");
        this.events.emit("camera:not-connected", new Error("No cameras found"));
        return false;
      }

      return true;
    } catch (error) {
      console.error("[CameraService] Error getting cameras:", error);
      this.cameras = [];
      this.events.emit(
        "camera:not-connected",
        new Error("Error getting camera list")
      );
      return false;
    }
  }

  /**
   * Thiết lập các thuộc tính camera
   * @param targetCamera Camera cần thiết lập thuộc tính (nếu không cung cấp, sử dụng camera hiện tại)
   */
  private async setupCameraProperties(targetCamera?: Camera): Promise<void> {
    const camera = targetCamera || this.camera;
    if (!camera) return;

    // Thiết lập từng thuộc tính riêng biệt để xử lý lỗi tốt hơn
    try {
      // Thiết lập SaveTo
      camera.setProperties({
        [CameraProperty.ID.SaveTo]: Option.SaveTo.Host,
        [CameraProperty.ID.ImageQuality]: ImageQuality.ID.LargeJPEGFine,
      });

      // Đợi để đảm bảo các thuộc tính được áp dụng
      await this.delay(DELAYS.PROPERTY_SET);
    } catch (propError) {
      console.warn(
        "[CameraService] Error setting camera properties:",
        propError
      );
      // Tiếp tục mặc dù có lỗi
    }
  }

  /**
   * Thiết lập event handler cho camera
   * @param targetCamera Camera cần thiết lập event handler (nếu không cung cấp, sử dụng camera hiện tại)
   */
  private setupEventHandler(targetCamera?: Camera): void {
    const camera = targetCamera || this.camera;
    if (!camera || !this.globalEventHandler) return;

    try {
      camera.setEventHandler(this.globalEventHandler);
    } catch (handlerError) {
      console.warn(
        "[CameraService] Error setting event handler:",
        handlerError
      );
    }
  }

  /**
   * Hoàn tất kết nối camera
   */
  private finalizeConnection(): void {
    try {
      watchCameras();
    } catch (watchError) {
      console.warn("[CameraService] Error watching cameras:", watchError);
    }

    console.log("[CameraService] Camera connected successfully");

    // Phát sự kiện kết nối thành công
    this.events.emit("camera:connected");

    // Phát sự kiện danh sách camera
    this.emitCamerasList();

    // Reset số lần thử kết nối
    this.connectionRetryCount = 0;
  }

  /**
   * Thử kết nối với camera
   * @param cameraIndex Chỉ số của camera cần kết nối
   * @returns true nếu kết nối thành công, false nếu không
   */
  private async tryConnectCamera(cameraIndex: number): Promise<boolean> {
    const camera = this.cameras[cameraIndex];
    if (!camera) return false;

    try {
      // Kiểm tra xem camera đã được kết nối chưa
      if (this.connectedCameras.has(cameraIndex)) {
        console.log(
          `[CameraService] Camera at index ${cameraIndex} already connected`
        );

        // Cập nhật camera hiện tại
        this.camera = camera;
        this.activeCameraIndex = cameraIndex;

        return true;
      }

      // Kết nối với camera
      camera.connect(true);
      await this.delay(DELAYS.CONNECT);

      // Thiết lập các thuộc tính
      await this.setupCameraProperties(camera);

      // Thiết lập event handler
      this.setupEventHandler(camera);

      // Lưu camera vào danh sách đã kết nối
      this.connectedCameras.set(cameraIndex, {
        camera,
        isLiveViewActive: false,
        liveViewCallback: null,
        state: { isCapturing: false, lastCaptureTime: 0, isReady: true },
      });

      // Cập nhật camera hiện tại
      this.camera = camera;
      this.activeCameraIndex = cameraIndex;

      // Hoàn tất kết nối
      this.finalizeConnection();

      return true;
    } catch (error) {
      console.error(
        `[CameraService] Connection error for camera ${cameraIndex}:`,
        error
      );
      return false;
    }
  }

  /**
   * Thử kết nối lại với camera sau khi thất bại
   * @param cameraIndex Chỉ số của camera cần kết nối lại
   * @returns true nếu kết nối thành công, false nếu không
   */
  private async retryConnection(cameraIndex: number): Promise<boolean> {
    // Tăng số lần thử kết nối
    this.connectionRetryCount++;

    // Nếu chưa vượt quá số lần thử tối đa, thử lại
    if (this.connectionRetryCount < this.MAX_CONNECTION_RETRIES) {
      console.log(
        `[CameraService] Retrying connection to camera ${cameraIndex} (${this.connectionRetryCount}/${this.MAX_CONNECTION_RETRIES})`
      );
      await this.delay(1000);
      return await this.attemptConnection(cameraIndex);
    } else {
      // Đã vượt quá số lần thử, phát sự kiện lỗi
      console.error(
        `[CameraService] Failed to connect to camera ${cameraIndex} after ${this.MAX_CONNECTION_RETRIES} attempts`
      );
      this.events.emit(
        "camera:not-connected",
        new Error(
          `Failed to connect after ${this.MAX_CONNECTION_RETRIES} attempts`
        )
      );
      return false;
    }
  }

  /**
   * Thực hiện kết nối với camera
   * @param cameraIndex Chỉ số của camera cần kết nối (nếu không cung cấp, sử dụng activeCameraIndex)
   * @returns true nếu kết nối thành công, false nếu không
   */
  private async attemptConnection(cameraIndex?: number): Promise<boolean> {
    try {
      // Lấy danh sách camera
      if (!this.fetchCameraList()) {
        return false;
      }

      // Sử dụng cameraIndex nếu được cung cấp, nếu không sử dụng activeCameraIndex
      const targetIndex =
        cameraIndex !== undefined ? cameraIndex : this.activeCameraIndex;

      // Kiểm tra xem chỉ số camera có hợp lệ không
      if (targetIndex < 0 || targetIndex >= this.cameras.length) {
        console.error(`[CameraService] Invalid camera index: ${targetIndex}`);
        return false;
      }

      // Cập nhật activeCameraIndex
      this.activeCameraIndex = targetIndex;

      // Đợi một chút trước khi kết nối
      await this.delay(DELAYS.CONNECT);

      // Thử kết nối với camera
      if (await this.tryConnectCamera(targetIndex)) {
        return true;
      }

      // Nếu kết nối thất bại, thử lại
      return await this.retryConnection(targetIndex);
    } catch (error: any) {
      console.error("[CameraService] Connection failed:", error);
      this.events.emit("camera:not-connected", error);
      return false;
    }
  }

  /**
   * Kết nối với camera
   * @param cameraIndex Chỉ số của camera cần kết nối (nếu không cung cấp, sử dụng activeCameraIndex)
   */
  public async connect(cameraIndex?: number): Promise<void> {
    const targetIndex =
      cameraIndex !== undefined ? cameraIndex : this.activeCameraIndex;

    if (!this.canConnect(targetIndex)) {
      return;
    }

    console.log(
      `[CameraService] Attempting to connect to camera at index ${targetIndex}`
    );
    this.connectionRetryCount = 0;
    await this.attemptConnection(targetIndex);
  }

  /**
   * Kết nối với tất cả các camera được phát hiện
   * Hàm này sẽ kết nối với tất cả các camera và đặt camera đầu tiên làm camera hoạt động
   */
  public async connectAllCameras(): Promise<void> {
    // Lấy danh sách camera
    if (!this.fetchCameraList()) {
      return;
    }

    console.log(
      `[CameraService] Attempting to connect to all ${this.cameras.length} cameras`
    );

    // Kết nối với camera đầu tiên trước
    await this.connect(0);

    // Kết nối với các camera còn lại
    if (this.cameras.length > 1) {
      for (let i = 1; i < this.cameras.length; i++) {
        try {
          console.log(
            `[CameraService] Connecting to additional camera at index ${i}`
          );

          // Kết nối với camera nhưng không chuyển đổi sang camera đó
          const camera = this.cameras[i];
          if (!camera) continue;

          // Kiểm tra xem camera đã được kết nối chưa
          if (this.connectedCameras.has(i)) {
            console.log(
              `[CameraService] Camera at index ${i} already connected`
            );
            continue;
          }

          // Kết nối với camera
          camera.connect(true);
          await this.delay(DELAYS.CONNECT);

          // Thiết lập các thuộc tính
          await this.setupCameraProperties(camera);

          // Thiết lập event handler
          this.setupEventHandler(camera);

          // Lưu camera vào danh sách đã kết nối
          this.connectedCameras.set(i, {
            camera,
            isLiveViewActive: false,
            liveViewCallback: null,
            state: { isCapturing: false, lastCaptureTime: 0, isReady: true },
          });

          console.log(
            `[CameraService] Successfully connected to camera at index ${i}`
          );
        } catch (error) {
          console.error(
            `[CameraService] Error connecting to camera at index ${i}:`,
            error
          );
          // Tiếp tục với camera tiếp theo nếu có lỗi
        }
      }
    }

    // Phát sự kiện danh sách camera sau khi kết nối tất cả
    this.emitCamerasList();

    console.log(
      `[CameraService] Connected to ${this.connectedCameras.size}/${this.cameras.length} cameras`
    );
  }

  // Phát sự kiện danh sách camera
  private emitCamerasList(): void {
    // Đảm bảo cameras là một mảng
    if (!Array.isArray(this.cameras)) {
      console.warn("[CameraService] cameras is not an array");
      return;
    }

    // Tạo danh sách camera
    const cameraInfoList = this.cameras.map((_camera, index) => ({
      index,
      name: `Camera ${index + 1}`,
    }));

    console.log(
      `[CameraService] Emitting cameras list with ${cameraInfoList.length} cameras`
    );
    this.events.emit("cameras:list", cameraInfoList);
  }

  // Lấy danh sách camera
  public getCameras(): CameraInfo[] {
    return this.cameras.map((_camera, index) => ({
      index,
      name: `Camera ${index + 1}`,
    }));
  }

  // Lấy chỉ số camera hiện tại
  public getActiveCameraIndex(): number {
    return this.activeCameraIndex;
  }

  /**
   * Kiểm tra xem có thể chuyển đổi camera không
   * @param index Chỉ số camera muốn chuyển đến
   * @returns true nếu có thể chuyển đổi, false nếu không
   */
  private canSwitchCamera(index: number): boolean {
    if (this.isCleaningUp) {
      console.log("[CameraService] Cannot switch camera while cleaning up");
      return false;
    }

    // Kiểm tra xem chỉ số camera có hợp lệ không
    if (index < 0 || index >= this.cameras.length) {
      console.error(`[CameraService] Invalid camera index: ${index}`);
      return false;
    }

    // Nếu đang sử dụng camera này, không cần chuyển đổi
    if (this.activeCameraIndex === index && this.camera) {
      console.log(`[CameraService] Already using camera at index ${index}`);
      return false;
    }

    return true;
  }

  /**
   * Chuyển đổi camera
   * @param index Chỉ số camera muốn chuyển đến
   * @returns true nếu chuyển đổi thành công, false nếu không
   * @returns object { success: boolean, alreadyConnected: boolean } để biết camera đã được kết nối trước đó hay không
   */
  public async switchCamera(
    index: number
  ): Promise<{ success: boolean; alreadyConnected: boolean }> {
    if (!this.canSwitchCamera(index)) {
      return { success: false, alreadyConnected: false };
    }

    if (this.isSwitchingCamera) {
      console.log("[CameraService] Another camera switch is in progress");
      return { success: false, alreadyConnected: false };
    }

    this.isSwitchingCamera = true;
    try {
      // Đợi camera hiện tại sẵn sàng
      if (!(await this.waitForCameraReady(this.activeCameraIndex))) {
        console.log("[CameraService] Current camera is not ready for switch");
        return { success: false, alreadyConnected: false };
      }

      const connectedCamera = this.connectedCameras.get(index);
      if (!connectedCamera) {
        console.log("[CameraService] Target camera not found");
        return { success: false, alreadyConnected: false };
      }

      // Đánh dấu camera đang switch
      connectedCamera.state.isReady = false;

      // Dừng live view hiện tại
      await this.stopLiveView();

      // Cập nhật camera mới
      this.camera = connectedCamera.camera;
      this.activeCameraIndex = index;

      // Đợi camera mới sẵn sàng
      await this.delay(1000);

      // Khôi phục live view nếu cần
      if (
        connectedCamera.isLiveViewActive &&
        connectedCamera.liveViewCallback
      ) {
        await this.startLiveView(connectedCamera.liveViewCallback);
      }

      // Đánh dấu camera đã sẵn sàng
      connectedCamera.state.isReady = true;

      return { success: true, alreadyConnected: true };
    } catch (error) {
      console.error("[CameraService] Error switching camera:", error);
      return { success: false, alreadyConnected: false };
    } finally {
      this.isSwitchingCamera = false;
    }
  }

  // Cấu hình cho LiveView
  private readonly LIVE_VIEW_CONFIG = {
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
    MAX_CONSECUTIVE_ERRORS: 10,
    RECONNECT_DELAY: 500,
  };

  /**
   * Kiểm tra xem có thể bắt đầu LiveView không
   * @returns true nếu có thể bắt đầu, false nếu không
   */
  private canStartLiveView(): boolean {
    if (this.isCleaningUp) {
      console.log("[CameraService] Cannot start live view while cleaning up");
      return false;
    }
    return true;
  }

  /**
   * Đảm bảo camera đã được kết nối trước khi bắt đầu LiveView
   * @returns true nếu camera đã kết nối, false nếu không
   */
  private async ensureCameraConnected(): Promise<boolean> {
    if (!this.camera) {
      console.log("[CameraService] No camera connected, attempting to connect");
      await this.connect();

      // Kiểm tra lại sau khi kết nối
      if (!this.camera) {
        console.error("[CameraService] Failed to connect to camera");
        return false;
      }
    }
    return true;
  }

  /**
   * Dừng LiveView hiện tại nếu đang hoạt động
   */
  private async stopExistingLiveView(): Promise<void> {
    if (this.isLiveViewActive()) {
      this.stopLiveView();
      await this.delay(DELAYS.LIVE_VIEW_STOP);
    }
  }

  /**
   * Cấu hình LiveView
   */
  private async configureLiveView(): Promise<void> {
    if (!this.camera) return;

    try {
      // Kiểm tra lại kết nối camera
      if (!this.camera) {
        console.log(
          "[CameraService] Session not open, attempting to reconnect"
        );
        await this.connect();
        await this.delay(DELAYS.CONNECT);
      }
    } catch (error) {
      console.error("[CameraService] Failed to configure live view:", error);
      throw error;
    }
  }

  /**
   * Bắt đầu LiveView trên camera
   * @throws Error nếu không thể bắt đầu LiveView
   */
  private async startCameraLiveView(): Promise<void> {
    if (!this.camera) {
      throw new Error("No camera available for live view");
    }

    try {
      this.camera.startLiveView();
      console.log("[CameraService] Live view started on camera");
    } catch (error) {
      await this.handleLiveViewStartError(error);
    }
  }

  /**
   * Xử lý lỗi khi bắt đầu LiveView
   * @param error Lỗi gặp phải
   * @throws Error nếu không thể xử lý lỗi
   */
  private async handleLiveViewStartError(error: any): Promise<void> {
    console.error("[CameraService] Error starting live view on camera:", error);

    // Kiểm tra loại lỗi
    const isSessionError =
      error.message &&
      (error.message.includes("SESSION_NOT_OPEN") ||
        error.message.includes("COMM_DISCONNECTED"));

    if (isSessionError) {
      // Thử kết nối lại camera
      console.log("[CameraService] Session error, attempting to reconnect");
      this.camera = null;
      await this.connect();

      if (!this.camera) {
        throw new Error("Failed to reconnect camera");
      }

      // Thử lại sau khi kết nối lại
      await this.delay(1000);
      console.log(
        "[CameraService] Retrying to start live view after reconnection"
      );
      this.camera.startLiveView();
    } else {
      // Thử lại một lần với lỗi khác
      await this.delay(1000);
      console.log("[CameraService] Retrying to start live view on camera");
      this.camera.startLiveView();
    }
  }

  /**
   * Kiểm tra xem lỗi có phải là SESSION_NOT_OPEN không
   */
  private isSessionNotOpenError(error: any): boolean {
    return (
      error?.message?.includes("SESSION_NOT_OPEN") ||
      error?.EDS_ERROR?.toString?.()?.includes("SESSION_NOT_OPEN") ||
      false
    );
  }

  /**
   * Xử lý frame LiveView
   * @param image Hình ảnh LiveView
   * @param lastProcessTime Thời điểm xử lý frame cuối cùng
   * @param consecutiveErrors Số lỗi liên tiếp
   * @returns Thông tin cập nhật: [thời điểm xử lý mới, số lỗi liên tiếp mới, cần khởi động lại session]
   */
  private processLiveViewFrame(
    image: LiveViewImage | null,
    lastProcessTime: number,
    consecutiveErrors: number
  ): [number, number, boolean] {
    let needSessionRestart = false;

    if (!image) {
      return [lastProcessTime, consecutiveErrors + 1, needSessionRestart];
    }

    try {
      const now = Date.now();
      const imageDataURL = image.getDataURL();

      // Kiểm tra xem imageDataURL có hợp lệ không
      if (
        typeof imageDataURL === "string" &&
        imageDataURL.startsWith("data:image")
      ) {
        // Đặt lại bộ đếm lỗi liên tiếp
        consecutiveErrors = 0;

        // Sử dụng setImmediate để không chặn luồng chính
        setImmediate(() => {
          // Log mỗi 100 frame để tránh quá nhiều log
          if (Math.random() < 0.01) {
            console.log("[CameraService] Emitting live view frame");
          }
          this.events.emit("liveview:frame", imageDataURL);
          this.liveViewCallback?.(imageDataURL, this.activeCameraIndex);
        });

        return [now, consecutiveErrors, needSessionRestart];
      } else {
        console.warn(
          "[CameraService] Invalid image data URL:",
          typeof imageDataURL === "string"
            ? imageDataURL.substring(0, 20) + "..."
            : typeof imageDataURL
        );
        return [lastProcessTime, consecutiveErrors + 1, needSessionRestart];
      }
    } catch (error) {
      // Kiểm tra lỗi SESSION_NOT_OPEN
      if (this.isSessionNotOpenError(error)) {
        console.warn(
          "[LiveView] SESSION_NOT_OPEN error detected, will attempt to reconnect"
        );
        needSessionRestart = true;
        return [lastProcessTime, consecutiveErrors + 1, needSessionRestart];
      }

      return [lastProcessTime, consecutiveErrors + 1, needSessionRestart];
    }
  }

  /**
   * Khởi động lại LiveView khi gặp quá nhiều lỗi
   * @param consecutiveErrors Số lỗi liên tiếp
   * @returns Số lỗi liên tiếp mới (0 nếu khởi động lại thành công)
   */
  private async restartLiveViewOnErrors(
    consecutiveErrors: number
  ): Promise<number> {
    if (consecutiveErrors <= this.LIVE_VIEW_CONFIG.MAX_CONSECUTIVE_ERRORS) {
      return consecutiveErrors;
    }

    console.warn(
      `[CameraService] Too many consecutive errors (${consecutiveErrors}), restarting live view`
    );

    try {
      if (!this.camera) return consecutiveErrors;

      this.camera.stopLiveView();
      await this.delay(500);
      this.camera.startLiveView();
      return 0; // Reset số lỗi liên tiếp
    } catch (error) {
      console.error("[CameraService] Error restarting live view:", error);

      // Thử kết nối lại camera
      try {
        console.log(
          "[CameraService] Attempting to reconnect camera after live view restart failure"
        );
        this.camera = null;
        await this.connect();

        if (this.camera) {
          await this.delay(500);
          this.camera.startLiveView();
          return 0; // Reset số lỗi liên tiếp
        }
      } catch (reconnectError) {
        console.error(
          "[CameraService] Failed to reconnect camera:",
          reconnectError
        );
      }

      return consecutiveErrors; // Giữ nguyên số lỗi
    }
  }

  /**
   * Xử lý lỗi SESSION_NOT_OPEN trong LiveView
   */
  private async handleLiveViewSessionError(): Promise<void> {
    console.log("[LiveView] Handling SESSION_NOT_OPEN error");

    try {
      // Dừng LiveView hiện tại
      if (this.camera) {
        try {
          this.camera.stopLiveView();
        } catch (stopError) {
          console.warn("[LiveView] Error stopping live view:", stopError);
        }
      }

      // Ngắt kết nối camera
      this.camera = null;
      await this.delay(1000);

      // Kết nối lại camera
      await this.connect();

      if (!this.camera) {
        console.error("[LiveView] Failed to reconnect camera");
        return;
      }

      // Khởi động lại LiveView
      await this.delay(1000);
      if (this.liveViewCallback) {
        try {
          this.camera.startLiveView();
          console.log(
            "[LiveView] Successfully restarted live view after SESSION_NOT_OPEN error"
          );
        } catch (startError) {
          console.error(
            "[LiveView] Failed to restart live view after reconnection:",
            startError
          );
        }
      }
    } catch (error) {
      console.error("[LiveView] Error handling SESSION_NOT_OPEN:", error);
    }
  }

  /**
   * Bắt đầu luồng xử lý frame LiveView
   */
  private startLiveViewProcessingLoop(): void {
    let lastProcessTime = Date.now();
    const targetFrameTime = 1000 / this.config.liveViewFPS;
    let consecutiveErrors = 0;
    let sessionErrorCount = 0;
    const MAX_SESSION_ERRORS = 5;
    let isHandlingSessionError = false;

    const processFrame = async () => {
      const now = Date.now();
      const timeSinceLastProcess = now - lastProcessTime;

      // Giới hạn tốc độ xử lý frame
      if (timeSinceLastProcess < targetFrameTime) {
        setImmediate(processFrame);
        return;
      }

      // Kiểm tra điều kiện để tiếp tục xử lý
      if (!this.isLiveViewActive() || !this.camera || !this.liveViewCallback) {
        setTimeout(processFrame, targetFrameTime);
        return;
      }

      try {
        // Lấy và xử lý frame
        const image = this.camera.getLiveViewImage();
        let needSessionRestart = false;
        [lastProcessTime, consecutiveErrors, needSessionRestart] =
          this.processLiveViewFrame(image, lastProcessTime, consecutiveErrors);

        // Xử lý lỗi SESSION_NOT_OPEN
        if (needSessionRestart) {
          sessionErrorCount++;

          // Nếu có quá nhiều lỗi SESSION_NOT_OPEN liên tiếp và chưa đang xử lý
          if (
            sessionErrorCount >= MAX_SESSION_ERRORS &&
            !isHandlingSessionError
          ) {
            isHandlingSessionError = true;
            console.log(
              `[LiveView] Too many SESSION_NOT_OPEN errors (${sessionErrorCount}), reconnecting camera`
            );

            // Xử lý lỗi SESSION_NOT_OPEN
            await this.handleLiveViewSessionError();

            // Reset các biến đếm
            sessionErrorCount = 0;
            consecutiveErrors = 0;
            isHandlingSessionError = false;
          }
        } else {
          // Reset biến đếm lỗi SESSION_NOT_OPEN nếu không có lỗi
          sessionErrorCount = 0;
        }

        // Khởi động lại LiveView nếu có quá nhiều lỗi
        if (
          consecutiveErrors > this.LIVE_VIEW_CONFIG.MAX_CONSECUTIVE_ERRORS &&
          !isHandlingSessionError
        ) {
          consecutiveErrors =
            await this.restartLiveViewOnErrors(consecutiveErrors);
        }
      } catch (error) {
        // Kiểm tra lỗi SESSION_NOT_OPEN
        if (this.isSessionNotOpenError(error)) {
          sessionErrorCount++;
          console.warn(
            `[LiveView] SESSION_NOT_OPEN error in getLiveViewImage (${sessionErrorCount}/${MAX_SESSION_ERRORS})`
          );

          // Xử lý lỗi SESSION_NOT_OPEN nếu có quá nhiều lỗi liên tiếp
          if (
            sessionErrorCount >= MAX_SESSION_ERRORS &&
            !isHandlingSessionError
          ) {
            isHandlingSessionError = true;
            await this.handleLiveViewSessionError();
            sessionErrorCount = 0;
            consecutiveErrors = 0;
            isHandlingSessionError = false;
          }
        }
        // Bỏ qua lỗi OBJECT_NOTREADY
        else if (!error.message?.includes("OBJECT_NOTREADY")) {
          consecutiveErrors++;
        }
      }

      // Tiếp tục vòng lặp
      setImmediate(processFrame);
    };

    // Bắt đầu vòng lặp xử lý frame
    processFrame();
  }

  /**
   * Bắt đầu luồng LiveView
   */
  private async startLiveViewStream(): Promise<void> {
    try {
      if (!this.camera) {
        console.error(
          "[CameraService] Cannot start live view stream: No camera available"
        );
        throw new Error("No camera available for live view");
      }

      console.log("[CameraService] Starting live view stream");

      // Bắt đầu LiveView trên camera
      await this.startCameraLiveView();

      // Bắt đầu luồng xử lý frame
      this.startLiveViewProcessingLoop();
    } catch (error) {
      console.error("[CameraService] Failed to start live view stream:", error);
      throw error;
    }
  }

  /**
   * Thử bắt đầu LiveView với cơ chế thử lại
   * @returns true nếu thành công, false nếu không
   */
  private async tryStartLiveView(
    callback: CameraEventHandler
  ): Promise<boolean> {
    // Số lần thử tối đa
    let retryCount = 0;
    let lastError = null;

    while (retryCount < this.LIVE_VIEW_CONFIG.MAX_RETRIES) {
      try {
        if (retryCount > 0) {
          console.log(
            `[CameraService] Retry attempt ${retryCount}/${this.LIVE_VIEW_CONFIG.MAX_RETRIES} for starting live view`
          );
          // Đợi một chút trước khi thử lại
          await this.delay(this.LIVE_VIEW_CONFIG.RETRY_DELAY);
        }

        // Đảm bảo camera đã được kết nối
        if (!(await this.ensureCameraConnected())) {
          throw new Error("Failed to connect to camera");
        }

        // Lưu callback
        if (!this.liveViewCallback) {
          this.liveViewCallback = callback;
        }

        // Dừng liveview hiện tại nếu có
        await this.stopExistingLiveView();

        // Cấu hình liveview
        await this.configureLiveView();

        // Bắt đầu stream liveview
        await this.startLiveViewStream();

        console.log("[CameraService] Live view started successfully");
        return true;
      } catch (error) {
        lastError = error;
        console.error(
          `[CameraService] Failed to start live view (attempt ${retryCount + 1}/${this.LIVE_VIEW_CONFIG.MAX_RETRIES}):`,
          error
        );

        // Xử lý lỗi SESSION_NOT_OPEN
        if (error.message && error.message.includes("SESSION_NOT_OPEN")) {
          await this.handleSessionNotOpenError();
        }

        retryCount++;
      }
    }

    // Nếu đã thử hết số lần mà vẫn thất bại
    if (lastError) {
      console.error(
        "[CameraService] Failed to start live view after all retries"
      );
      throw new CameraServiceError(
        `Failed to start live view: ${lastError.message}`,
        CameraErrorCode.LIVE_VIEW_START_ERROR
      );
    }

    return false;
  }

  /**
   * Xử lý lỗi SESSION_NOT_OPEN
   */
  private async handleSessionNotOpenError(): Promise<void> {
    console.log(
      "[CameraService] Session not open, attempting to reconnect camera"
    );

    try {
      // Đảm bảo camera được ngắt kết nối trước
      this.camera = null;
      await this.delay(this.LIVE_VIEW_CONFIG.RECONNECT_DELAY);

      // Kết nối lại
      await this.connect();
      await this.delay(this.LIVE_VIEW_CONFIG.RECONNECT_DELAY);
    } catch (reconnectError) {
      console.error(
        "[CameraService] Failed to reconnect camera:",
        reconnectError
      );
    }
  }

  /**
   * Bắt đầu LiveView
   * @param callback Callback để nhận frame LiveView
   */
  public async startLiveView(callback: CameraEventHandler): Promise<void> {
    if (!this.canStartLiveView()) {
      return;
    }

    console.log("[CameraService] Starting live view");
    await this.tryStartLiveView(callback);
  }

  public stopLiveView(): void {
    if (this.isCleaningUp) {
      console.log("[CameraService] Already cleaning up, skipping stopLiveView");
      return;
    }
    // Clear interval and ensure it's nullified
    if (this.liveViewInterval) {
      clearInterval(this.liveViewInterval);
      this.liveViewInterval = null;
    }

    try {
      // Stop live view on camera if available
      if (this.camera) {
        this.camera.stopLiveView();
      }
    } catch (error) {
      console.error("[CameraService] Error while stopping live view:", error);
    } finally {
      console.log("[CameraService] Live view stopped");
    }
  }

  // Photo Capture Methods
  /**
   * Kiểm tra xem camera có sẵn sàng để chụp ảnh không
   * @throws CameraServiceError nếu camera không sẵn sàng
   */
  private checkCameraReadiness(): void {
    if (this.isCleaningUp) {
      console.log("[CameraService] Cannot take picture while cleaning up");
      throw new CameraServiceError(
        "Cannot take picture while camera is cleaning up",
        CameraErrorCode.CAPTURE_ERROR
      );
    }

    if (!this.camera) {
      throw new CameraServiceError(
        "Camera not connected",
        CameraErrorCode.NO_CAMERA
      );
    }
  }

  /**
   * Kiểm tra trạng thái camera và đợi nếu camera đang bận
   */
  private async checkCameraState(): Promise<void> {
    try {
      // Một số camera có thể có phương thức kiểm tra trạng thái
      if (typeof (this.camera as any).getState === "function") {
        const state = (this.camera as any).getState();
        console.log(`[PhotoCapture] Camera state: ${state}`);

        // Nếu camera đang bận, đợi một chút
        if (state === "busy") {
          console.log("[PhotoCapture] Camera is busy, waiting before capture");
          await this.delay(2000);
        }
      }
    } catch (stateError) {
      // Bỏ qua lỗi khi kiểm tra trạng thái
      console.warn("[PhotoCapture] Error checking camera state:", stateError);
    }
  }

  /**
   * Dừng liveview trước khi chụp ảnh
   */
  private async stopLiveViewForCapture(): Promise<void> {
    if (this.isLiveViewActive()) {
      console.log("[PhotoCapture] Stopping live view for capture");

      try {
        await this.stopExistingLiveView();
        // Đợi một chút sau khi dừng liveview
        await this.delay(1000);
      } catch (lvError) {
        console.warn(
          "[PhotoCapture] Error stopping liveview, continuing anyway:",
          lvError
        );
        // Đợi lâu hơn nếu không thể dừng liveview
        await this.delay(2000);
      }
    }
  }

  /**
   * Xử lý lỗi chụp ảnh và ném lỗi với thông tin chi tiết
   * @param error Lỗi gốc
   * @throws CameraServiceError với thông tin chi tiết
   */
  private handleCaptureError(error: any): never {
    console.error("[PhotoCapture] Capture failed:", error);

    // Thêm thông tin chi tiết về lỗi
    let errorMessage = `Failed to take picture: ${error.message}`;
    let errorCode = CameraErrorCode.CAPTURE_ERROR;

    // Phân loại lỗi
    if (error.message) {
      if (this.isDeviceBusyError(error)) {
        errorMessage = "Camera is busy. Please wait and try again.";
        errorCode = CameraErrorCode.DEVICE_BUSY;
      } else if (this.isNotSupportedError(error)) {
        errorMessage = "This operation is not supported by the camera.";
        errorCode = CameraErrorCode.NOT_SUPPORTED;
      } else if (this.isFocusError(error)) {
        errorMessage =
          "Camera could not focus. Please try again or adjust lighting.";
        errorCode = CameraErrorCode.FOCUS_ERROR;
      }
    }

    throw new CameraServiceError(errorMessage, errorCode);
  }

  /**
   * Chụp ảnh
   * @throws CameraServiceError nếu có lỗi
   */
  public async takePicture(): Promise<void> {
    const cameraIndex = this.activeCameraIndex;

    if (!(await this.lockCameraForCapture(cameraIndex))) {
      console.log(
        `[PhotoCapture] Camera ${cameraIndex} is not ready for capture`
      );
      return;
    }

    try {
      console.log(
        `[PhotoCapture] Starting capture with camera index: ${cameraIndex}`
      );
      console.log(`[PhotoCapture] Current camera: ${this.camera?.description}`);

      // Verify camera index trước khi chụp
      if (!(await this.verifyCameraIndex())) {
        throw new Error("Camera index verification failed");
      }

      // Kiểm tra camera có sẵn sàng không
      if (!this.camera) {
        throw new Error("Camera is not ready");
      }

      try {
        // Thử lấy một thuộc tính của camera để kiểm tra kết nối
        await this.camera.getProperty(CameraProperty.ID.SaveTo);
      } catch (error) {
        console.error("[PhotoCapture] Camera not ready:", error);
        throw new Error("Camera is not ready");
      }

      // Thực hiện chụp ảnh
      const success = await this.tryTakePicture();
      if (!success) {
        throw new Error("Failed to take picture");
      }

      console.log(
        `[PhotoCapture] Photo captured successfully with camera index: ${cameraIndex}`
      );
    } catch (error) {
      console.error("[PhotoCapture] Error taking picture:", error);
      throw error;
    } finally {
      await this.unlockCamera(cameraIndex);
    }
  }

  public registerCaptureWorkspace(
    workspace: string,
    callback: (localUrl: string, activeCameraIndex: number) => void
  ): void {
    this.workspace = workspace;

    console.log("[CanonService] Register capture workspace:", workspace);

    this.currentPhotoCallback = async (eventName, event: any) => {
      if (this.isPhotoEvent(eventName)) {
        try {
          await this.handlePhotoDownload(event, callback);

          console.log(
            "[CanonService] registerCaptureWorkspace - activeCameraIndex",
            this.activeCameraIndex
          );

          await this.delay(500);

          this.restoreLiveView().catch(console.warn);
        } catch (error) {
          console.error("[CanonService] Error handling photo capture:", error);
          this.handleError(error);
        }
      }
    };
  }

  // Cấu hình cho việc chụp ảnh
  private readonly CAPTURE_CONFIG = {
    MAX_RETRIES: 5,
    MAX_FOCUS_RETRIES: 3,
    MAX_BUSY_RETRIES: 3,
    FOCUS_DELAY: 1000,
    BUSY_RETRY_DELAY: 2000,
    STABILIZE_DELAY: 2000,
    ERROR_RETRY_DELAY: 1500,
  };

  // Hằng số cho các lệnh camera
  private readonly CAMERA_COMMANDS = {
    PRESS_SHUTTER: 0x04,
    PRESS_HALFWAY: 0x01,
    PRESS_COMPLETELY: 0x02,
    PRESS_OFF: 0x00,
  };

  /**
   * Lấy thông tin model của camera
   * @returns Tên model của camera
   */
  private getCameraModel(): string {
    if (!this.camera) return "unknown";

    try {
      // Sử dụng thuộc tính description có sẵn trong Camera
      const model = this.camera.description;
      console.log(`[PhotoCapture] Camera model: ${model}`);
      return model;
    } catch (descError) {
      console.warn(
        "[PhotoCapture] Error getting camera description:",
        descError
      );

      // Nếu không lấy được description, thử sử dụng getProperty
      try {
        // Thử lấy model ID hoặc tên sản phẩm
        const modelProperty = this.camera.getProperty(0x00000005); // EDS_PROP_PRODUCT_NAME
        if (modelProperty) {
          const model = modelProperty.toString();
          console.log(`[PhotoCapture] Camera model from property: ${model}`);
          return model;
        }
      } catch (propError) {
        console.warn(
          "[PhotoCapture] Error getting camera property:",
          propError
        );
      }
    }

    return "unknown";
  }

  /**
   * Thử lấy nét trước khi chụp ảnh
   * @returns true nếu lấy nét thành công hoặc đã bỏ qua, false nếu có lỗi
   */
  private async tryFocus(): Promise<boolean> {
    try {
      console.log("[PhotoCapture] Attempting to focus");

      // Thử thiết lập chế độ lấy nét tự động
      try {
        this.camera.setProperties({
          [CameraProperty.ID.AFMode]: 0, // 0 = One-Shot AF
        });
        await this.delay(500);
      } catch (focusModeError) {
        if (this.isNotSupportedError(focusModeError)) {
          console.log(
            "[PhotoCapture] AF mode setting not supported by this camera"
          );
          return false; // Không hỗ trợ lấy nét
        }
        console.warn("[PhotoCapture] Error setting AF mode:", focusModeError);
      }

      // Sử dụng sendCommand để lấy nét
      try {
        // Lấy các hằng số từ Camera.Command nếu có, hoặc sử dụng giá trị mặc định
        const pressAFCommand =
          Camera.Command?.PressShutterButton ||
          this.CAMERA_COMMANDS.PRESS_SHUTTER;
        const pressHalfway =
          Camera.PressShutterButton?.Halfway ||
          this.CAMERA_COMMANDS.PRESS_HALFWAY;
        const pressOff =
          Camera.PressShutterButton?.OFF || this.CAMERA_COMMANDS.PRESS_OFF;

        // Nhấn nút chụp nửa chừng để lấy nét
        this.camera.sendCommand(pressAFCommand, pressHalfway);
        console.log("[PhotoCapture] Sent half-press command for focusing");
        await this.delay(this.CAPTURE_CONFIG.FOCUS_DELAY);

        // Nhả nút chụp
        this.camera.sendCommand(pressAFCommand, pressOff);
        console.log("[PhotoCapture] Released half-press command");
        await this.delay(500);

        return true; // Lấy nét thành công
      } catch (cmdError) {
        if (this.isNotSupportedError(cmdError)) {
          console.log(
            "[PhotoCapture] Focus commands not supported by this camera"
          );
          return false; // Không hỗ trợ lấy nét
        }
        console.warn("[PhotoCapture] Error during focus command:", cmdError);
        return false; // Lỗi lấy nét
      }
    } catch (focusError) {
      console.error("[PhotoCapture] Focus error:", focusError);
      return false; // Lỗi lấy nét
    }
  }

  /**
   * Thử chụp ảnh bằng phương thức takePicture
   * @returns true nếu chụp thành công, false nếu có lỗi
   */
  private async tryTakePicture(): Promise<boolean> {
    let busyRetryCount = 0;

    console.log(
      `[PhotoCapture] Attempting to take picture with camera index: ${this.activeCameraIndex}`
    );
    console.log(
      `[PhotoCapture] Current camera: ${this.camera?.description || "unknown"}`
    );

    while (busyRetryCount < this.CAPTURE_CONFIG.MAX_BUSY_RETRIES) {
      try {
        // Verify camera index before taking picture
        if (!(await this.verifyCameraIndex())) {
          throw new Error("Camera index mismatch before capture");
        }

        console.log("[PhotoCapture] Executing takePicture command...");
        this.camera.takePicture();
        console.log(
          `[PhotoCapture] Photo captured successfully with camera index: ${this.activeCameraIndex}`
        );
        return true; // Chụp thành công
      } catch (error) {
        if (this.isDeviceBusyError(error)) {
          busyRetryCount++;
          console.log(
            `[PhotoCapture] Device busy, waiting before retry ${busyRetryCount}/${this.CAPTURE_CONFIG.MAX_BUSY_RETRIES}`
          );

          // Đợi lâu hơn giữa các lần thử khi device busy
          await this.delay(
            this.CAPTURE_CONFIG.BUSY_RETRY_DELAY * busyRetryCount
          );

          if (busyRetryCount >= this.CAPTURE_CONFIG.MAX_BUSY_RETRIES) {
            throw error; // Ném lỗi nếu đã thử quá số lần cho phép
          }
        } else if (this.isNotSupportedError(error)) {
          // Thử phương pháp thay thế
          return await this.tryAlternativeTakePicture();
        } else {
          throw error; // Ném lỗi nếu không phải lỗi đã biết
        }
      }
    }

    return false; // Không thể chụp ảnh sau tất cả các lần thử
  }

  /**
   * Thử chụp ảnh bằng phương pháp thay thế (sử dụng sendCommand)
   * Phương pháp này có thể hoạt động tốt hơn khi LiveView đang hoạt động
   * @returns true nếu chụp thành công, false nếu có lỗi
   */
  private async tryAlternativeTakePicture(): Promise<boolean> {
    try {
      console.log(
        `[PhotoCapture] Trying alternative capture method with camera index: ${this.activeCameraIndex}`
      );

      // Verify camera index before taking picture
      if (!(await this.verifyCameraIndex())) {
        throw new Error("Camera index mismatch before alternative capture");
      }

      // Lấy các hằng số từ Camera.Command nếu có, hoặc sử dụng giá trị mặc định
      const pressAFCommand =
        Camera.Command?.PressShutterButton ||
        this.CAMERA_COMMANDS.PRESS_SHUTTER;
      const pressHalfway =
        Camera.PressShutterButton?.Halfway ||
        this.CAMERA_COMMANDS.PRESS_HALFWAY;
      const pressCompletely =
        Camera.PressShutterButton?.Completely ||
        this.CAMERA_COMMANDS.PRESS_COMPLETELY;
      const pressOff =
        Camera.PressShutterButton?.OFF || this.CAMERA_COMMANDS.PRESS_OFF;

      // Kiểm tra xem LiveView có đang hoạt động không
      const isLiveViewActive = this.isLiveViewActive();

      // Nếu LiveView đang hoạt động, sử dụng phương pháp đặc biệt
      if (isLiveViewActive) {
        console.log(
          `[PhotoCapture] LiveView is active for camera index: ${this.activeCameraIndex}, using special capture sequence`
        );

        try {
          // Thử thiết lập chế độ lấy nét tự động
          try {
            this.camera.setProperties({
              [CameraProperty.ID.AFMode]: 0, // 0 = One-Shot AF
            });
          } catch (afError) {
            // Bỏ qua lỗi này, không quan trọng
            console.log(
              "[PhotoCapture] Setting AF mode failed, continuing anyway:",
              afError
            );
          }

          // Nhấn nút chụp nửa chừng để lấy nét
          this.camera.sendCommand(pressAFCommand, pressHalfway);
          await this.delay(300); // Đợi ngắn hơn để lấy nét

          // Nhấn nút chụp hoàn toàn
          this.camera.sendCommand(pressAFCommand, pressCompletely);
          await this.delay(300); // Đợi ngắn hơn

          // Nhả nút chụp
          this.camera.sendCommand(pressAFCommand, pressOff);

          // Đợi một chút để camera xử lý
          await this.delay(500);

          console.log(
            `[PhotoCapture] Special capture sequence completed with camera index: ${this.activeCameraIndex}`
          );
          return true; // Chụp thành công
        } catch (specialError) {
          console.log(
            "[PhotoCapture] Special capture sequence failed:",
            specialError
          );
          // Thử phương pháp thông thường nếu phương pháp đặc biệt thất bại
        }
      }

      // Phương pháp thông thường - chỉ nhấn hoàn toàn và nhả
      try {
        // Nhấn nút chụp hoàn toàn
        this.camera.sendCommand(pressAFCommand, pressCompletely);
        await this.delay(300); // Giảm thời gian chờ xuống 300ms

        // Nhả nút chụp
        this.camera.sendCommand(pressAFCommand, pressOff);

        console.log(
          `[PhotoCapture] Standard alternative capture completed with camera index: ${this.activeCameraIndex}`
        );
        return true; // Chụp thành công
      } catch (standardError) {
        console.log(
          "[PhotoCapture] Standard alternative capture failed:",
          standardError
        );

        // Thử một phương pháp khác nếu phương pháp thông thường thất bại
        try {
          // Thử sử dụng phương pháp khác nếu có
          if (typeof this.camera.sendCommand === "function") {
            // Thử sử dụng lệnh chụp ảnh trực tiếp nếu có
            const directCaptureCommand = 0x0d; // Giá trị thường dùng cho lệnh chụp trực tiếp
            this.camera.sendCommand(directCaptureCommand, 0);
            console.log(
              `[PhotoCapture] Direct capture command sent with camera index: ${this.activeCameraIndex}`
            );
            return true;
          }
        } catch (directError) {
          console.log(
            "[PhotoCapture] Direct capture command failed:",
            directError
          );
        }

        throw standardError; // Ném lỗi nếu tất cả các phương pháp đều thất bại
      }
    } catch (error) {
      console.error(
        `[PhotoCapture] All alternative capture methods failed for camera index: ${this.activeCameraIndex}`,
        error
      );
      throw error; // Ném lỗi nếu không thể chụp
    }
  }

  /**
   * Khởi động lại liveview
   * @returns true nếu khởi động lại thành công, false nếu có lỗi
   */
  private async restartLiveViewForCapture(): Promise<boolean> {
    try {
      this.stopLiveView();
      await this.delay(1000);

      await this.startLiveView(this.liveViewCallback);
      await this.delay(this.CAPTURE_CONFIG.STABILIZE_DELAY);
      return true;
    } catch (error) {
      console.warn(
        "[PhotoCapture] Error restarting liveview, continuing anyway:",
        error
      );
      await this.delay(3000); // Đợi lâu hơn nếu không thể khởi động lại liveview
      return false;
    }
  }

  /**
   * Kiểm tra xem lỗi có phải là DEVICE_BUSY không
   */
  private isDeviceBusyError(error: any): boolean {
    return (
      error?.message?.includes("DEVICE_BUSY") ||
      error?.EDS_ERROR?.toString?.()?.includes("DEVICE_BUSY") ||
      false
    );
  }

  /**
   * Kiểm tra xem lỗi có phải là NOT_SUPPORTED không
   */
  private isNotSupportedError(error: any): boolean {
    return (
      error?.message?.includes("NOT_SUPPORTED") ||
      error?.EDS_ERROR?.toString?.()?.includes("NOT_SUPPORTED") ||
      false
    );
  }

  /**
   * Kiểm tra xem lỗi có phải là lỗi lấy nét không
   */
  private isFocusError(error: any): boolean {
    const errorMessage = error?.message || error?.EDS_ERROR?.toString?.() || "";
    return (
      errorMessage.includes("FOCUS_FAILED") ||
      errorMessage.includes("TAKE_PICTURE_AF_NG") ||
      errorMessage.includes("FOCUSING_POINT_NG")
    );
  }

  /**
   * Kiểm tra xem lỗi có phải là lỗi chế độ quay phim không
   */
  private isMovieModeError(error: any): boolean {
    const errorMessage = error?.message || error?.EDS_ERROR?.toString?.() || "";
    return (
      errorMessage.includes("TAKE_PICTURE_SPECIAL_MOVIE_MODE_NG") ||
      errorMessage.includes("TAKE_PICTURE_MOVIE_CROP_NG")
    );
  }

  /**
   * Chuyển camera từ chế độ quay phim sang chế độ chụp ảnh
   */
  private async switchFromMovieMode(): Promise<boolean> {
    if (!this.camera) return false;

    try {
      console.log(
        "[PhotoCapture] Attempting to switch from movie mode to photo mode"
      );

      // Thử thiết lập chế độ chụp ảnh
      try {
        // Thiết lập chế độ chụp (0x00000012 = Shooting Mode)
        // Giá trị: 0 = P, 1 = Tv, 2 = Av, 3 = M, 4 = Bulb, 7 = Movie
        this.camera.setProperty(0x00000012, 0); // Chuyển sang chế độ P (Program)
        await this.delay(1000);

        // Kiểm tra xem đã chuyển thành công chưa
        const shootingMode = this.camera.getProperty(0x00000012);
        console.log(`[PhotoCapture] Current shooting mode: ${shootingMode}`);

        // Chuyển đổi giá trị thành số để so sánh
        const modeValue =
          typeof shootingMode === "object"
            ? (shootingMode as any).value || 0
            : Number(shootingMode);

        if (modeValue !== 7) {
          // Nếu không còn ở chế độ Movie
          console.log(
            "[PhotoCapture] Successfully switched from movie mode to photo mode"
          );
          return true;
        } else {
          console.log(
            "[PhotoCapture] Failed to switch from movie mode to photo mode"
          );
          return false;
        }
      } catch (error) {
        console.warn("[PhotoCapture] Error switching from movie mode:", error);
        return false;
      }
    } catch (error) {
      console.error("[PhotoCapture] Error in switchFromMovieMode:", error);
      return false;
    }
  }

  /**
   * Chụp và lưu ảnh với cơ chế thử lại
   * @param liveViewStopped Đã dừng LiveView trước khi gọi hàm này hay chưa
   */
  private async captureAndSavePhoto(liveViewStopped = false): Promise<void> {
    console.log(
      `[PhotoCapture] Starting capture with camera index: ${this.activeCameraIndex}`
    );
    console.log(
      `[PhotoCapture] Current camera: ${this.camera?.description || "unknown"}`
    );

    // Xác nhận camera index
    if (!(await this.verifyCameraIndex())) {
      throw new Error("Camera index mismatch before capture");
    }

    try {
      // Thử lấy nét trước khi chụp
      const focusSuccess = await this.tryFocus();
      if (!focusSuccess) {
        console.warn(
          "[PhotoCapture] Focus failed, continuing with capture anyway"
        );
      }

      // Thử chụp ảnh
      const captureSuccess = await this.tryTakePicture();
      if (!captureSuccess) {
        throw new Error("Failed to capture photo");
      }

      console.log(
        `[PhotoCapture] Photo captured successfully with camera index: ${this.activeCameraIndex}`
      );
    } catch (error) {
      console.error(
        `[PhotoCapture] Error capturing photo with camera index: ${this.activeCameraIndex}:`,
        error
      );
      throw error;
    }
  }

  private isPhotoEvent(eventName: string): boolean {
    return (
      eventName === Camera.EventName.FileCreate ||
      eventName === Camera.EventName.DownloadRequest
    );
  }

  private async handlePhotoDownload(
    event: FileChangeEvent,
    callback: (path: string, position: number) => void
  ): Promise<void> {
    const file = event.file;
    if (!file) {
      throw new CameraServiceError(
        "No file received from camera",
        CameraErrorCode.DOWNLOAD_ERROR
      );
    }

    await this.delay(DELAYS.PROPERTY_SET);

    try {
      // Đảm bảo thư mục workspace tồn tại
      console.log("[PhotoCapture] Workspace:", this.workspace);

      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const fs = require("fs");
      if (!fs.existsSync(this.workspace)) {
        fs.mkdirSync(this.workspace, { recursive: true });
      }

      this.photoCount++;

      // Tạo tên file độc đáo với số đếm có 4 chữ số (ví dụ: photo_0001.jpg)
      const uniqueFileName = `IMG_${this.photoCount.toString().padStart(4, "0")}`;
      file.downloadToPath(this.workspace);

      // Download trực tiếp vào workspace
      const originalPath = `${this.workspace}/${file.name}`;
      console.log("[PhotoCapture] Downloading to path:", originalPath);

      // Download file
      console.log("[PhotoCapture] File downloaded successfully");
      const finalFilePath = `${this.workspace}/flipped_${uniqueFileName}.jpg`;
      const finalNoBackgroundPath = `${this.workspace}/no_background_flipped_${uniqueFileName}.png`;

      callback(finalFilePath, this.activeCameraIndex);

      // Xử lý với Sharp để lật ngang
      await sharp(originalPath)
        .resize({
          width: 1500,
          height: 1000,
        })
        .flop()
        .toFile(finalFilePath);

      if (config.isEnableRemoveBackground) {
        this.events.emit("photo:remove-background", {
          input_path: finalFilePath,
          output_path: finalNoBackgroundPath,
        });
      }
    } catch (error) {
      console.error("[PhotoCapture] Error processing image:", error);
      throw new CameraServiceError(
        `Failed to process photo: ${error.message}`,
        CameraErrorCode.DOWNLOAD_ERROR
      );
    }
  }

  /**
   * Khôi phục LiveView sau khi chụp ảnh
   * Sử dụng cơ chế thử lại thông minh để tăng khả năng thành công
   */
  private async restoreLiveView(): Promise<void> {
    const MAX_RETRIES = 5; // Tăng số lần thử lên 5
    const BASE_RETRY_DELAY = 200; // Delay cơ bản
    let retries = MAX_RETRIES;
    let lastError: any = null;

    // Đợi một chút trước khi bắt đầu khôi phục LiveView
    await this.delay(100);

    while (retries > 0) {
      try {
        // Kiểm tra xem LiveView đã hoạt động chưa
        if (this.isLiveViewActive()) {
          console.log(
            "[PhotoCapture] LiveView is already active, no need to restore"
          );
          return;
        }

        // Thử khởi động lại LiveView
        if (retries === MAX_RETRIES) {
          console.log("[PhotoCapture] First attempt to restore LiveView");
        } else {
          console.log(
            `[PhotoCapture] Retry attempt ${MAX_RETRIES - retries + 1}/${MAX_RETRIES} to restore LiveView`
          );
        }

        // Khởi động lại LiveView
        await this.startLiveView(this.liveViewCallback);
        console.log(
          "[PhotoCapture] LiveView restored successfully at:",
          Date.now()
        );
        return;
      } catch (error: any) {
        lastError = error;
        console.error(
          `[CanonService] Error restoring LiveView: ${error.message}`
        );
        retries--;

        // Tính toán thời gian chờ dựa trên số lần thử còn lại
        const retryDelay = BASE_RETRY_DELAY * (MAX_RETRIES - retries + 1);

        if (error.message?.includes("DEVICE_BUSY") && retries > 0) {
          console.log(
            `[PhotoCapture] Device busy, retrying LiveView restore in ${retryDelay}ms... (${retries} attempts remaining)`
          );
          await this.delay(retryDelay);
          continue;
        } else if (error.message?.includes("SESSION_NOT_OPEN") && retries > 0) {
          console.log(
            `[PhotoCapture] Session not open, attempting to reconnect camera before retrying LiveView`
          );

          // Thử kết nối lại camera
          try {
            this.camera = null;
            await this.delay(500);
            await this.connect();
            await this.delay(500);
          } catch (reconnectError) {
            console.error(
              "[PhotoCapture] Error reconnecting camera:",
              reconnectError
            );
          }

          await this.delay(retryDelay);
          continue;
        }

        // Đối với các lỗi khác, đợi một chút và thử lại
        if (retries > 0) {
          console.log(
            `[PhotoCapture] Retrying LiveView restore in ${retryDelay}ms... (${retries} attempts remaining)`
          );
          await this.delay(retryDelay);
        } else {
          console.error(
            "[PhotoCapture] Failed to restore LiveView after all retries"
          );

          // Thử một cách tiếp cận khác nếu tất cả các lần thử đều thất bại
          try {
            console.log(
              "[PhotoCapture] Trying alternative approach to restore LiveView"
            );

            // Đợi lâu hơn và thử lại một lần cuối
            await this.delay(1000);

            // Thử kết nối lại camera
            this.camera = null;
            await this.connect();
            await this.delay(500);

            // Thử khởi động lại LiveView
            await this.startLiveView(this.liveViewCallback);
            console.log(
              "[PhotoCapture] LiveView restored with alternative approach"
            );
            return;
          } catch (finalError) {
            console.error(
              "[PhotoCapture] Final attempt to restore LiveView failed:",
              finalError
            );
            throw lastError; // Ném lỗi ban đầu
          }
        }
      }
    }
  }

  // Utility Methods
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  public getCamera(): Camera | null {
    return this.camera;
  }

  public async cleanup(): Promise<void> {
    console.log("[CanonService] Starting camera service cleanup");

    if (this.isCleaningUp) {
      console.log("[CanonService] Already cleaning up, skipping cleanup");
      return;
    }

    try {
      // First stop any active live view
      this.isCleaningUp = true;
      this.stopExistingLiveView();

      // Ngắt kết nối tất cả các camera đã kết nối
      for (const [index, connectedCamera] of this.connectedCameras.entries()) {
        try {
          // Clear event handler
          connectedCamera.camera.setEventHandler(null);

          // Disconnect camera
          connectedCamera.camera.disconnect();
          console.log(
            `[CanonService] Camera at index ${index} disconnected successfully`
          );
        } catch (error) {
          console.warn(
            `[CanonService] Error disconnecting camera at index ${index}:`,
            error
          );
        }
      }

      // Xóa danh sách camera đã kết nối
      this.connectedCameras.clear();

      console.log("[CanonService] Cleanup completed successfully");
    } catch (error) {
      console.error("[CanonService] Error during cleanup sequence:", error);
    } finally {
      this.isCleaningUp = false;
      // Clear all callbacks
      this.currentPhotoCallback = null;
      this.liveViewCallback = null;
      this.globalEventHandler = null;
      this.workspace = null;
      this.activeCameraIndex = 0;
      this.photoCount = 0;

      // Clear intervals
      if (this.liveViewInterval) {
        clearInterval(this.liveViewInterval);
        this.liveViewInterval = null;
      }

      // Final cleanup
      this.camera = null;
      this.events.removeAllListeners();
    }
  }

  protected isLiveViewActive(): boolean {
    try {
      if (this.camera) {
        const isActivated = this.camera.isLiveViewActive();
        return isActivated;
      }

      return false;
    } catch (error) {
      console.warn("[CameraService] Error checking live view status:", error);
      return false;
    }
  }

  private async verifyCameraIndex(): Promise<boolean> {
    const connectedCamera = this.connectedCameras.get(this.activeCameraIndex);
    if (!connectedCamera || connectedCamera.camera !== this.camera) {
      console.error(
        `[PhotoCapture] Camera mismatch: active index ${this.activeCameraIndex} does not match current camera`
      );
      console.error(
        `[PhotoCapture] Connected camera: ${connectedCamera?.camera?.description || "unknown"}`
      );
      console.error(
        `[PhotoCapture] Current camera: ${this.camera?.description || "unknown"}`
      );
      return false;
    }
    return true;
  }

  private async lockCameraForCapture(cameraIndex: number): Promise<boolean> {
    const camera = this.connectedCameras.get(cameraIndex);
    if (!camera) {
      console.log(`[PhotoCapture] Camera ${cameraIndex} not found`);
      return false;
    }

    const now = Date.now();
    if (camera.state.isCapturing) {
      console.log(`[PhotoCapture] Camera ${cameraIndex} is already capturing`);
      return false;
    }

    if (now - camera.state.lastCaptureTime < this.CAPTURE_COOLDOWN) {
      console.log(`[PhotoCapture] Camera ${cameraIndex} is in cooldown`);
      return false;
    }

    camera.state.isCapturing = true;
    camera.state.lastCaptureTime = now;
    return true;
  }

  private async unlockCamera(cameraIndex: number): Promise<void> {
    const camera = this.connectedCameras.get(cameraIndex);
    if (camera) {
      camera.state.isCapturing = false;
    }
  }

  private async waitForCameraReady(
    cameraIndex: number,
    timeout: number = this.SWITCH_TIMEOUT
  ): Promise<boolean> {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      const camera = this.connectedCameras.get(cameraIndex);
      if (camera && camera.state.isReady && !camera.state.isCapturing) {
        return true;
      }
      await this.delay(100);
    }
    return false;
  }
}

export const cameraService = CanonService.getInstance();
