import { BaseCameraService } from './base';
import { BrowserWindow, ipcMain } from 'electron';

export class ******************** extends BaseCameraService {
  private mainWindow: BrowserWindow | null = null;
  protected liveViewInterval: NodeJS.Timeout | null = null;

  constructor() {
    super();
    this.initialize();
  }

  protected initialize(): void {
    
  }

  public async connect(): Promise<void> {
    try {
      
      if (this.mainWindow) {
        this.mainWindow.webContents.send('camera:connect');
        await new Promise<void>((resolve) => {
          ipcMain.once('camera:connected', () => {
            
            resolve();
          });
        });
      }
    } catch (error) {
      console.error('[********************] Connection failed:', error);
      
      throw error;
    }
  }

  public async startLiveView(callback: (imageDataURL: string) => void): Promise<void> {
    try {
      if (!this.isLiveViewActive()) {
        if (this.mainWindow) {
          this.mainWindow.webContents.send('camera:start-live-view');
        }

        // Lắng nghe frames từ renderer
        ipcMain?.on('camera:frame', (_, imageDataURL: string) => {
          callback(imageDataURL);
          this.events.emit('liveview:frame', imageDataURL);
        });
      }

      
    } catch (error) {
      console.error('[********************] Live view failed:', error);
      
      throw error;
    }
  }

  public stopLiveView(): void {
    if (this.liveViewInterval) {
      clearInterval(this.liveViewInterval);
      this.liveViewInterval = null;
    }

    if (this.mainWindow) {
      this.mainWindow.webContents.send('camera:stop-live-view');
    }

    ipcMain.removeAllListeners('camera:frame');
  }

  public async takePicture(savePath: string, callback: (localUrl: string) => void): Promise<void> {
    try {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('camera:take-picture');
        const imageDataURL = await new Promise<string>((resolve) => {
          ipcMain.once('camera:picture-taken', (_, data) => resolve(data));
        });
        this.events.emit('photo:captured', imageDataURL);
      }
    } catch (error) {
      console.error('[********************] Capture failed:', error);
      
      throw error;
    }
  }

  public setMainWindow(window: BrowserWindow) {
    this.mainWindow = window;
  }

  public cleanup(): void {
    super.cleanup();
    this.stopLiveView();
    this.mainWindow = null;
    ipcMain.removeAllListeners('camera:frame');
    ipcMain.removeAllListeners('camera:picture-taken');
    ipcMain.removeAllListeners('camera:connected');
  }
}

export const cameraService = new ********************();