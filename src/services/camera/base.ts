import { TypedEventEmitter } from './type';

export abstract class BaseCameraService {
  public events: TypedEventEmitter;
  protected liveViewInterval: NodeJS.Timeout | null = null;
  protected stream: MediaStream | null = null;
  protected videoElement: HTMLVideoElement | null = null;
  protected canvas: HTMLCanvasElement | null = null;

  constructor() {
    this.events = new TypedEventEmitter();
    this.initialize();
  }

  protected initialize(): void {
    console.log('[BaseCameraService] Initializing service');
  }

  abstract connect(): Promise<void>;
  
  abstract startLiveView(callback: (imageDataURL: string) => void): Promise<void>;
  
  abstract stopLiveView(): void;
  
  abstract takePicture(savePath: string, callback: (localUrl: string) => void): Promise<void>;

  public cleanup(): void {
    try {
      console.log('[BaseCameraService] Starting cleanup sequence');
      
      // Stop live view first if active
      if (this.isLiveViewActive()) {
        this.stopLiveView();
      }

      // Clean up media streams
      if (this.stream) {
        this.stream.getTracks().forEach(track => {
          try {
            track.stop();
          } catch (error) {
            console.warn('[BaseCameraService] Error stopping track:', error);
          }
        });
        this.stream = null;
      }

      // Clear any remaining intervals
      if (this.liveViewInterval) {
        clearInterval(this.liveViewInterval);
        this.liveViewInterval = null;
      }

      console.log('[BaseCameraService] Cleanup sequence completed');
    } catch (error) {
      console.error('[BaseCameraService] Error during cleanup:', error);
    }
  }


  protected isLiveViewActive(): boolean {
    return false;
  }
} 