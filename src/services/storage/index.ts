export const STORAGE_KEYS = {
  FRAMES: "snapbox/frames",
  LAYOUTS: "snapbox/layouts",
  WAITING_SCREENS: "snapbox/waiting_screens",
  APPEARANCES: "snapbox/appearances",
  SIZES: "snapbox/sizes",
  STICKERS: "snapbox/stickers",
  PRINT_SETTINGS: "snapbox/print_settings",
  ORDER: "snapbox/order",
};

import Store from "electron-store";
const localStorage: any = new Store();

const isLocalStorageAvailable = () => {
  try {
    const testKey = "__storage_test__";
    localStorage.set(testKey, testKey);
    localStorage.delete(testKey);
    return true;
  } catch (e) {
    return false;
  }
};

export const localStorageService = {
  setItem: (key: string, value: any) => {
    try {
      if (!isLocalStorageAvailable()) {
        throw new Error("localStorage không khả dụng");
      }
      localStorage.set(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error saving to localStorage [${key}]:`, error);
      throw error;
    }
  },

  getItem: (key: string) => {
    try {
      if (!isLocalStorageAvailable()) {
        return null;
      }
      const item = localStorage.get(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error(`Error reading from localStorage [${key}]:`, error);
      return null;
    }
  },

  removeItem: (key: string) => {
    try {
      localStorage.delete(key);
    } catch (error) {
      console.error(`Error removing from localStorage [${key}]:`, error);
      throw error;
    }
  },

  clear: () => {
    try {
      localStorage.clear();
    } catch (error) {
      console.error("Error clearing localStorage:", error);
      throw error;
    }
  },

  // Helper để kiểm tra xem một key có tồn tại không
  hasItem: (key: string): boolean => {
    return localStorage.getItem(key) !== null;
  },

  // Helper để lấy tất cả keys
  getAllKeys: (): string[] => {
    return Object.keys(localStorage);
  },

  // Helper để lấy kích thước đã sử dụng
  getSize: (): number => {
    let total = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length * 2; // UTF-16 uses 2 bytes per char
      }
    }
    return total;
  },
};
