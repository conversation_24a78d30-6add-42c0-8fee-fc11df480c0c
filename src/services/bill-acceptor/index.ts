import { SerialPort } from "serialport";
import { EventEmitter } from "events";
import { getConfig } from "@/cores/utils/config";

// Types
interface Command {
  command: string;
  delay: number;
}

interface BillAcceptorConfig {
  billAcceptorPort: string;
  baudRate: number;
}

// Constants
const CONSTANTS = {
  BUFFER: {
    MAX_SIZE: 10,
    TIMEOUT: 1000,
  },
  COMMAND: {
    TIMEOUT: 2000,
    DEFAULT_DELAY: 50,
  },
  NOTE_VALUES: [10000, 20000, 50000, 100000, 200000, 500000],
  PROTOCOL: {
    START_BYTES: ["81", "80"] as const,
    POWER_UP: "8f",
    ACK: "02",
    ENABLE: "3E",
    DISABLE: "5E",
  },
} as const;

/**
 * BillAcceptorService - Handles communication with the bill acceptor device
 * Implements the Singleton pattern to ensure only one instance exists
 */
export class BillAcceptorService {
  private static instance: BillAcceptorService;
  private port: SerialPort;
  private buffer: Buffer[] = [];
  private isProcessingBuffer = false;
  private bufferTimeout: NodeJS.Timeout | null = null;
  private billValue = 0;
  private commandQueue: Command[] = [];
  private isProcessingCommand = false;
  private config: BillAcceptorConfig;
  public events = new EventEmitter();

  private constructor() {
    this.config = getConfig();
  }

  public static getInstance(): BillAcceptorService {
    if (!BillAcceptorService.instance) {
      BillAcceptorService.instance = new BillAcceptorService();
    }
    return BillAcceptorService.instance;
  }

  /**
   * Initializes connection to the bill acceptor
   */
  public connect(): void {
    try {
      this.initializeSerialPort();
      this.setupEventListeners();
    } catch (error) {
      console.error("Failed to connect to bill acceptor:", error);
      throw error;
    }
  }

  /**
   * Initializes the serial port with configured settings
   */
  private initializeSerialPort(): void {
    this.port = new SerialPort({
      path: this.config.billAcceptorPort,
      baudRate: this.config.baudRate,
      dataBits: 8,
      stopBits: 1,
      parity: "none",
    });
  }

  /**
   * Sets up event listeners for the serial port
   */
  private setupEventListeners(): void {
    this.port.on("open", this.handlePortOpen.bind(this));
    this.port.on("data", this.handlePortData.bind(this));
    this.port.on("error", this.handlePortError.bind(this));
  }

  /**
   * Handles port open event
   */
  private handlePortOpen(): void {
    console.log("Serial Port Opened");
    setTimeout(() => this.toggleBillAcceptor(true), 1000);
  }

  /**
   * Processes incoming data from the serial port
   */
  private handlePortData(data: Buffer): void {
    const hexData = data.toString("hex");
    if (this.isIgnorableData(hexData)) return;

    console.log("Received data:", hexData);
    this.processIncomingData(data);
  }

  /**
   * Handles port error events
   */
  private handlePortError(error: Error): void {
    console.error("Serial port error:", error);
    this.events.emit("error", error);
  }

  /**
   * Checks if the received data should be ignored
   */
  private isIgnorableData(hexData: string): boolean {
    return ["00", "3e", "5e"].includes(hexData);
  }

  /**
   * Main method for processing incoming data
   */
  private processIncomingData(data: Buffer): void {
    if (this.isProcessingBuffer) {
      console.warn("Buffer is currently being processed, skipping this data");
      return;
    }

    try {
      this.isProcessingBuffer = true;
      this.setupBufferTimeout();
      this.processBufferData(data);
    } catch (error) {
      console.error("Error processing buffer:", error);
      this.clearBuffer();
    } finally {
      this.cleanupBufferProcessing();
    }
  }

  /**
   * Sets up timeout for buffer processing
   */
  private setupBufferTimeout(): void {
    if (this.bufferTimeout) {
      clearTimeout(this.bufferTimeout);
    }

    this.bufferTimeout = setTimeout(() => {
      console.warn("Buffer processing timeout, clearing buffer");
      this.clearBuffer();
      this.isProcessingBuffer = false;
    }, CONSTANTS.BUFFER.TIMEOUT);
  }

  /**
   * Processes the incoming buffer data
   */
  private processBufferData(data: Buffer): void {
    const splitData = this.splitAndPushBuffers(data);

    if (this.isBufferSizeExceeded(splitData.length)) {
      console.warn("Buffer size exceeded limit, clearing buffer");
      this.clearBuffer();
      return;
    }

    this.buffer.push(...splitData);
    this.processBufferByLength();
  }

  /**
   * Checks if adding new data would exceed buffer size limit
   */
  private isBufferSizeExceeded(newDataLength: number): boolean {
    return this.buffer.length + newDataLength > CONSTANTS.BUFFER.MAX_SIZE;
  }

  /**
   * Processes buffer based on its length
   */
  private processBufferByLength(): void {
    if (this.buffer.length <= 1) {
      this.processSingleByteBuffer();
    } else if (this.buffer.length > 1) {
      this.processMultiByteBuffer();
    }
  }

  /**
   * Processes single byte buffer
   */
  private processSingleByteBuffer(): void {
    const firstByte = this.buffer[0].toString("hex");
    if (firstByte === "10") {
      this.handleBillValue();
    } else if (
      !CONSTANTS.PROTOCOL.START_BYTES.includes(firstByte as "81" | "80")
    ) {
      this.clearBuffer();
    }
  }

  /**
   * Processes multi-byte buffer
   */
  private processMultiByteBuffer(): void {
    const [firstByte, secondByte] = this.buffer.map((b) => b.toString("hex"));
    if (firstByte === "80" && secondByte === CONSTANTS.PROTOCOL.POWER_UP) {
      this.handlePowerUp();
    } else if (
      firstByte === "81" &&
      secondByte === CONSTANTS.PROTOCOL.POWER_UP
    ) {
      this.handleBillValue();
    } else if (
      !CONSTANTS.PROTOCOL.START_BYTES.includes(firstByte as "81" | "80")
    ) {
      this.clearBuffer();
    }
  }

  /**
   * Handles bill value processing and emits event
   */
  private handleBillValue(): void {
    if (this.buffer.length > 2) {
      const value = Number(this.buffer[2].toString("hex"));
      if (value >= 40 && value <= 45) {
        this.billValue = CONSTANTS.NOTE_VALUES[value % 40];
        console.log(`Bill Value - VND: ${this.billValue}`);
        this.writeSerialData(CONSTANTS.PROTOCOL.ACK);
        this.events.emit("bill-value", this.billValue);
      }
    }
    this.clearBuffer();
  }

  /**
   * Handles power up event
   */
  private handlePowerUp(): void {
    this.writeSerialData(CONSTANTS.PROTOCOL.ACK);
    console.log("Power Up command sent");
    setTimeout(() => this.toggleBillAcceptor(true), 1000);
    this.clearBuffer();
  }

  /**
   * Splits buffer into individual bytes
   */
  private splitAndPushBuffers(buffer: Buffer): Buffer[] {
    if (!buffer?.length) {
      console.warn("Empty buffer received");
      return [];
    }

    const buffers: Buffer[] = [];
    let tempBuffer = buffer;

    while (tempBuffer.length > 0) {
      const firstByte = tempBuffer.subarray(0, 1);
      buffers.push(firstByte);
      tempBuffer = tempBuffer.subarray(1);
    }

    return buffers;
  }

  /**
   * Processes the command queue
   */
  private async processCommandQueue(): Promise<void> {
    if (this.isProcessingCommand || !this.commandQueue.length) return;

    this.isProcessingCommand = true;
    const command = this.commandQueue.shift();

    if (!command) {
      this.isProcessingCommand = false;
      return;
    }

    try {
      await this.executeCommand(command);
    } catch (error) {
      console.error("Error processing command:", error);
    } finally {
      this.isProcessingCommand = false;
      if (this.commandQueue.length) {
        this.processCommandQueue();
      }
    }
  }

  /**
   * Executes a single command with timeout
   */
  private async executeCommand({ command, delay }: Command): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error("Command timeout"));
      }, CONSTANTS.COMMAND.TIMEOUT);

      const hexBuffer = Buffer.from(command, "hex");
      this.port.write(hexBuffer, (err) => {
        if (err) {
          clearTimeout(timeout);
          reject(err);
          return;
        }

        console.log("Command sent:", command);
        setTimeout(() => {
          clearTimeout(timeout);
          resolve();
        }, delay);
      });
    });
  }

  /**
   * Writes data to serial port
   */
  public writeSerialData(data: string): void {
    this.commandQueue.push({
      command: data,
      delay: CONSTANTS.COMMAND.DEFAULT_DELAY,
    });
    this.processCommandQueue();
  }

  /**
   * Toggles bill acceptor state
   */
  public toggleBillAcceptor(disable: boolean): void {
    console.log("Bill acceptor state:", disable ? "disabled" : "enabled");

    const command = disable
      ? CONSTANTS.PROTOCOL.DISABLE
      : CONSTANTS.PROTOCOL.ENABLE;
    this.commandQueue.push({
      command,
      delay: 1000,
    });
    this.processCommandQueue();
  }

  /**
   * Clears the buffer
   */
  private clearBuffer(): void {
    this.buffer = [];
  }

  /**
   * Cleans up buffer processing state
   */
  private cleanupBufferProcessing(): void {
    this.isProcessingBuffer = false;
    if (this.bufferTimeout) {
      clearTimeout(this.bufferTimeout);
      this.bufferTimeout = null;
    }
  }

  /**
   * Closes the serial port connection
   */
  public close(): void {
    this.toggleBillAcceptor(true);

    this.port.close((err) => {
      if (err) {
        console.error("Error closing port:", err.message);
      }
      console.log("Serial port closed");
    });
  }
}

// Export singleton instance
export const billAcceptorService = BillAcceptorService.getInstance();
