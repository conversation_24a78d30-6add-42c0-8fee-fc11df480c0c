import { SerialPort } from "serialport";
import { EventEmitter } from "events";
import { getConfig } from "@/cores/utils/config";

export class BillAcceptorService {
  private static instance: BillAcceptorService;
  private port: SerialPort;
  private buffer: Buffer[] = [];
  private noteValue = [10000, 20000, 50000, 100000, 200000, 500000];
  private billValue = 0;
  private disableBill = false;
  public events = new EventEmitter();
  private config = getConfig();

  public static getInstance(): BillAcceptorService {
    if (!BillAcceptorService.instance) {
      BillAcceptorService.instance = new BillAcceptorService();
    }
    return BillAcceptorService.instance;
  }

  public connect(): void {
    console.log("config: ", this.config);

    this.port = new SerialPort(
      {
        path: this.config.billAcceptorPort,
        baudRate: this.config.baudRate,
        dataBits: 8,
        stopBits: 1,
        parity: "none",
      },
      (err) => {
        if (err) {
          return console.error("Error opening port:", err.message);
        }
        console.log("Serial port opened successfully");
      }
    );

    this.port.on("open", () => {
      console.log("Serial Port Opened");
      setTimeout(() => {
        this.toggleBillAcceptor(true);
      }, 1000);
    });

    this.port.on("data", (data: Buffer) => {
      if (
        data.toString("hex") == "00" ||
        data.toString("hex") == "3e" ||
        data.toString("hex") == "5e"
      ) {
        return;
      }
      console.log("buffer: ", data);
      this.protocol106U(data);
    });
  }

  private protocol106U(data: any) {
    const Splitdata = this.splitAndPushBuffers(data);
    Splitdata.forEach((byte) => {
      this.buffer.push(byte);
    });

    if (this.buffer.length <= 1) {
      if (this.buffer[0].toString("hex") === "10") {
        console.log("billValue: ", this.billValue);
        this.events.emit("bill-value", this.billValue);
        this.buffer = [];
        return;
      }
      if (
        this.buffer[0].toString("hex") != "81" &&
        this.buffer[0].toString("hex") != "80"
      ) {
        this.buffer = [];
        return;
      }
    }
    if (this.buffer.length > 1) {
      if (
        this.buffer[0].toString("hex") === "80" &&
        this.buffer[1].toString("hex") === "8f"
      ) {
        this.writeSerialData("02");
        console.log("send Power Up");
        setTimeout(() => {
          this.toggleBillAcceptor(true);
        }, 1000);
        this.buffer = [];
        return;
      }
      if (
        this.buffer[0].toString("hex") != "81" &&
        this.buffer[0].toString("hex") != "80"
      ) {
        this.buffer = [];
        return;
      }
    }
    if (this.buffer.length > 2) {
      if (
        this.buffer[0].toString("hex") === "81" &&
        this.buffer[1].toString("hex") === "8f" &&
        Number(this.buffer[2].toString("hex")) >= 40 &&
        Number(this.buffer[2].toString("hex")) <= 45
      ) {
        this.writeSerialData("02");
        const value = Number(this.buffer[2].toString("hex"));
        this.billValue = this.noteValue[value % 40];
        console.log(`bill Value - VND: ${this.noteValue[value % 40]}`);
        this.buffer = [];
        return;
      }
      if (
        this.buffer[0].toString("hex") != "81" &&
        this.buffer[1].toString("hex") != "8f"
      ) {
        this.buffer = [];
        return;
      }
    }
    if (this.buffer.length >= 3) {
      this.buffer = [];
      return;
    }
  }

  private splitAndPushBuffers(buffer: Buffer): Buffer[] {
    const buffers: Buffer[] = [];
    let tempBuffer = buffer;

    if (tempBuffer.length <= 1) {
      buffers.push(tempBuffer);
      return buffers;
    }

    while (tempBuffer.length > 0) {
      const firstByte = tempBuffer.slice(0, 1);
      buffers.push(firstByte);
      tempBuffer = tempBuffer.slice(1);
    }

    return buffers;
  }

  public writeSerialData(data: string): void {
    const hexBuffer = Buffer.from(data, "hex");
    this.port.write(hexBuffer, (err) => {
      if (err) {
        console.error("Error on write: ", err.message);
      } else {
        console.log("Message written (hex): ", data);
      }
    });
  }

  public toggleBillAcceptor(disable: boolean): void {
    this.disableBill = disable;
    console.log("disableBill: ", this.disableBill);
    if (this.disableBill) {
      this.writeSerialData("5E");
      return;
    }
    this.writeSerialData("3E");
  }

  public close(): void {
    this.port.close((err) => {
      if (err) {
        console.error("Error closing port:", err.message);
      }
      console.log("Serial port closed");
    });
  }
}

// Provide exports for backward compatibility
export const billAcceptorService = BillAcceptorService.getInstance();
