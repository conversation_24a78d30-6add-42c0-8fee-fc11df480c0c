import * as lark from "@larksuiteoapi/node-sdk";

class LarkService {
  private client: lark.Client;

  constructor() {
    this.client = new lark.Client({
      appId: "cli_a7d0e5e14db9102f",
      appSecret: "jAMTeWMMyzwyLGOUyafRIgwcnpfg8iSd",
    });
  }

  public async send(variables: Record<string, any>) {
    if (!this.client.tokenManager) return;
    try {
      console.log("variables", variables);

      await this.client.im.message.createByCard({
        params: {
          receive_id_type: "chat_id",
        },
        data: {
          receive_id: "oc_6f905be7a84703b6c3d8e9ff54585f23",
          template_id: "ctp_AA4oR6gwo957",
          template_variable: variables,
        },
      });
    } catch (error) {
      console.error("[LarkService] Error sending message:", error);
    }
  }
}

export default new LarkService();
