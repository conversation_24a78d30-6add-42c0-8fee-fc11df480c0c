import { ipc<PERSON><PERSON>er } from "electron";

export const initStorePersistencePreload = async () => ({
  saveState: (state: any): Promise<boolean> => {
    return ipcRenderer.invoke("redux:save-state", state);
  },
  loadState: (): Promise<any | undefined> => {
    return ipcRenderer.invoke("redux:load-state");
  },
  clearState: (): Promise<boolean> => {
    return ipcRenderer.invoke("redux:clear-state");
  },
});
