import { ipc<PERSON><PERSON><PERSON> } from "electron";
import {
  IdleStateCallback,
  MergePhotosToFrameParams,
  ReuploadParameters,
} from "@/cores/types";

const registerSharedPreload = () => ({
  mergePhotosToFrame: (params: MergePhotosToFrameParams) =>
    ipcRenderer.invoke("merge-photos-to-frame", params),
  updateOrder: (params: any) => {
    return ipcRenderer.invoke("update-order", { params });
  },
  onSubscribeToIdleState: (callback: IdleStateCallback) => {
    ipcRenderer.on("idle-state", callback);

    return () => {
      ipcRenderer.removeListener("idle-state", callback);
    };
  },
  getTempPath: () => ipcRenderer.invoke("get-temp-path"),
  reuploadFiles: (params: ReuploadParameters) =>
    ipcRenderer.invoke("reupload-files", params),
  getInitialState: () => {
    try {
      console.log("[Preload] Getting initial state from main process");
      const state = ipcRenderer.sendSync("get-initial-state");
      console.log("[Preload] Received initial state:", state);

      // Kiểm tra xem state có phải là chuỗi JSON hợp lệ không
      if (typeof state === "string") {
        try {
          const parsedState = JSON.parse(state);
          console.log("[Preload] Successfully parsed state:", parsedState);
          return state;
        } catch (parseError) {
          console.error(
            "[Preload] Received invalid JSON from main process:",
            parseError
          );
        }
      }

      return state;
    } catch (error) {
      console.error("[Preload] Error getting initial state:", error);
      return JSON.stringify({});
    }
  },

  // Thêm hàm để gửi action trực tiếp đến main process
  dispatchToMain: (action: any) => {
    ipcRenderer.send("redux-action", action);
  },

  // Các hàm để lưu trữ Redux store
  reduxPersistence: {
    saveState: (state: any) => {
      console.log("[Preload] Saving Redux state to disk");
      return ipcRenderer.invoke("redux:save-state", state);
    },
    loadState: () => {
      console.log("[Preload] Loading Redux state from disk");
      return ipcRenderer.invoke("redux:load-state");
    },
    clearState: () => {
      console.log("[Preload] Clearing Redux state from disk");
      return ipcRenderer.invoke("redux:clear-state");
    },
  },
});

export { registerSharedPreload };
