import { BillDetectedCallback } from "@/cores/types";
import { ipc<PERSON><PERSON><PERSON> } from "electron";

const registerBillAcceptorPreload = () => ({
  startBillingPolling: (callback: BillDetectedCallback) => {
    ipcRenderer.on("received-cash", callback);
    ipcRenderer.invoke("billing-start-polling");

    return () => {
      console.log("Unregistering bill acceptor preload");
      
      ipcRenderer.removeListener("received-cash", callback);
      ipcRenderer.invoke("billing-stop-polling");
    };
  },
});

export { registerBillAcceptorPreload };
