import {
  LiveViewCallback,
  TakePictureCallback,
  TimelapseCompleteCallback,
  CameraNotConnectedCallback,
  StopTimelapseParameters,
  CamerasListCallback,
  CameraSwitchedCallback,
  CameraErrorCallback,
} from "@/cores/types";
import { ipcRenderer } from "electron";

const registerCameraPreload = () => ({
  onLiveView: (callback: LiveViewCallback) => {
    ipcRenderer.invoke("start-live-view");
    ipcRenderer.on("liveViewImage", callback);

    return () => {
      ipcRenderer.removeListener("liveViewImage", callback);
    };
  },
  onCameraNotConnected: (callback: CameraNotConnectedCallback) => {
    ipcRenderer.on("camera-not-connected", callback);

    return () => {
      ipcRenderer.removeListener("camera-not-connected", callback);
    };
  },
  onTakePicture: (callback: TakePictureCallback) => {
    ipcRenderer.on("takePicture", callback);

    return () => {
      ipcRenderer.removeListener("takePicture", callback);
    };
  },
  onRegisterCaptureWorkspace: (dir: string) =>
    ipcRenderer.invoke("register-capture-workspace", dir),
  capture: (dir: string): Promise<void> =>
    ipcRenderer.invoke("capture-photo", dir),
  setupCamera: (): Promise<void> => ipcRenderer.invoke("setup-camera"),
  startTimelapse: () => ipcRenderer.invoke("start-timelapse"),
  stopTimelapse: (params: StopTimelapseParameters) => {
    ipcRenderer.invoke("stop-timelapse", params);
    ipcRenderer.invoke("cleanup-camera");
  },
  disconnectRelay: (): Promise<void> => ipcRenderer.invoke("disconnect-relay"),
  restartLiveview: (): Promise<void> => ipcRenderer.invoke("restart-liveview"),
  turnOffCamera: (): Promise<void> => ipcRenderer.invoke("turn-off-camera"),
  onTimelapseCompleted: (callback: TimelapseCompleteCallback) => {
    ipcRenderer.on("timelapse-completed", callback);

    return () => {
      ipcRenderer.removeListener("timelapse-completed", callback);
    };
  },
  onCaptureError: (callback: CameraNotConnectedCallback) => {
    ipcRenderer.on("photo-capture-error", callback);

    return () => {
      ipcRenderer.removeListener("photo-capture-error", callback);
    };
  },

  // Camera switching methods
  getCameras: (): Promise<void> => ipcRenderer.invoke("get-cameras"),

  switchCamera: (cameraIndex: number): Promise<void> =>
    ipcRenderer.invoke("switch-camera", cameraIndex),

  onCamerasList: (callback: CamerasListCallback) => {
    ipcRenderer.on("camerasList", callback);

    return () => {
      ipcRenderer.removeListener("camerasList", callback);
    };
  },

  onCameraSwitched: (callback: CameraSwitchedCallback) => {
    ipcRenderer.on("cameraSwitched", callback);

    return () => {
      ipcRenderer.removeListener("cameraSwitched", callback);
    };
  },

  onCameraError: (callback: CameraErrorCallback) => {
    ipcRenderer.on("cameraError", callback);

    return () => {
      ipcRenderer.removeListener("cameraError", callback);
    };
  },
});

export { registerCameraPreload };
