import { ipc<PERSON><PERSON><PERSON> } from "electron";

const validChannels = [
  'network-status',
  'network-status-changed'
] as const;

type ValidChannel = typeof validChannels[number];

const registerNetworkPreload = () => ({
  checkNetwork: () => ipcRenderer.invoke("check-network"),
  onNetworkStatusChanged: (callback: (status: boolean) => void) => {
    const subscription = (_: any, status: boolean) => callback(status);
    ipcRenderer.on('network-status', subscription);
    // Return the cleanup function
    return () => ipcRenderer.removeListener('network-status', subscription);
  },
  sendNetworkStatusToMain: (channel: ValidChannel, ...args: any[]) => {
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, ...args);
    }
  }
});

export { registerNetworkPreload };
