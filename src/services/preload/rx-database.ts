import { IDownloadImageParams } from "@/cores/types";
import { ipc<PERSON><PERSON><PERSON> } from "electron";

const registerRxDatabasePreload = () => ({
	insertAppearence: (data: any) => ipcRenderer.invoke('insert-appearence', data),
	insertWaitingScreen: (data: any) => ipcRenderer.invoke('insert-waiting-screen', data),
	insertLayouts: (data: any) => ipcRenderer.invoke('insert-layouts', data),
	insertSizes: (data: any) => ipcRenderer.invoke('insert-sizes', data),
	insertStickers: (data: any) => ipcRenderer.invoke('insert-stickers', data),
	insertFrames: (data: any) => ipcRenderer.invoke('insert-frames', data),
	insertPrintSetting: (data: any) => ipcRenderer.invoke('insert-print-setting', data),
	downloadImage: (params: IDownloadImageParams) => ipcRenderer.invoke('download-image', params),
	retrivedRecords: () => ipcRenderer.invoke('retrived-records')
})

export { registerRxDatabasePreload };
