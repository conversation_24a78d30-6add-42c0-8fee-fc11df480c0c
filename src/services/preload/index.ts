import { registerCameraPreload } from "./camera";
import { registerBillAcceptorPreload } from "./bill-acceptor";
import { registerRxDatabasePreload } from "./rx-database";
import { registerNetworkPreload } from "./network";
import { initPrinterPreload } from "./printer";
import { registerSharedPreload } from "./shared";
import { registerRouterPreload } from "./router";
import { initLarkPreload } from "./lark";
import { initStorePersistencePreload } from "./store-persistence";

export {
  registerCameraPreload,
  registerBillAcceptorPreload,
  registerRxDatabasePreload,
  registerNetworkPreload,
  initPrinterPreload,
  registerSharedPreload,
  registerRouterPreload,
  initLarkPreload,
  initStorePersistencePreload,
};
