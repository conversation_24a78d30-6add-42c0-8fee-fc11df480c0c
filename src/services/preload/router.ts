import { RouterCallback } from "@/cores/types";
import { ipc<PERSON><PERSON><PERSON> } from "electron";

const registerRouterPreload = () => {
  return {
    primaryNavigate: (path: string) =>
      ipcRenderer.invoke("primary-navigate", path),
    secondaryNavigate: (path: string) =>
      ipcRenderer.invoke("secondary-navigate", path),
    onPrimaryNavigate: (callback: RouterCallback) => {
      ipcRenderer.on("on-primary-navigate", callback);

      return () => {
        ipcRenderer.removeListener("on-primary-navigate", callback);
      };
    },
    onSecondaryNavigate: (callback: RouterCallback) => {
      ipcRenderer.on("on-secondary-navigate", callback);

      return () => {
        ipcRenderer.removeListener("on-secondary-navigate", callback);
      };
    },
  };
};

export { registerRouterPreload };
