import { app } from "electron";
import path from "path";
import fs from "fs";

let logStream: fs.WriteStream | null = null;
let errorStream: fs.WriteStream | null = null;

const setupLogger = async () => {
  const logDir = path.join(app.getPath("userData"), "logs");
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const logFile = path.join(
    logDir,
    `app-${new Date().toISOString().split("T")[0]}.log`
  );
  const errorLogFile = path.join(
    logDir,
    `error-${new Date().toISOString().split("T")[0]}.log`
  );
  
  logStream = fs.createWriteStream(logFile, { flags: "a" });
  errorStream = fs.createWriteStream(errorLogFile, { flags: "a" });

  // <PERSON>ưu l<PERSON> console.log gốc
  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;

  const writeLog = (type: 'LOG' | 'ERROR', args: any[]) => {
    const stream = type === 'ERROR' ? errorStream : logStream;
    if (!stream || stream.destroyed) return;

    try {
      const timestamp = new Date().toISOString();
      const logMessage = args
        .map((arg) => (typeof arg === "object" ? JSON.stringify(arg) : arg))
        .join(" ");
      stream.write(`[${timestamp}] [${type}] ${logMessage}\n`);
    } catch (error) {
      originalConsoleError('Error writing to log:', error);
    }
  };

  // Override console.log
  console.log = (...args) => {
    // Ghi ra terminal
    originalConsoleLog.apply(console, args);
    // Ghi ra file
    writeLog('LOG', args);
  };

  // Override console.error
  console.error = (...args) => {
    // Ghi ra terminal
    originalConsoleError.apply(console, args);
    // Ghi ra file
    writeLog('ERROR', args);
  };

  // Đảm bảo đóng stream khi app tắt
  app.on("before-quit", () => {
    if (logStream && !logStream.destroyed) {
      logStream.end();
      logStream = null;
    }
    if (errorStream && !errorStream.destroyed) {
      errorStream.end();
    }
  });

  // Tạo file log mới mỗi ngày
  const scheduleNextDayLogFile = () => {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const timeUntilNextDay = tomorrow.getTime() - now.getTime();
    
    setTimeout(() => {
      if (logStream && !logStream.destroyed) {
        logStream.end();
        const newLogFile = path.join(
          logDir,
          `app-${tomorrow.toISOString().split("T")[0]}.log`
        );
        logStream = fs.createWriteStream(newLogFile, { flags: "a" });
      }
      if (errorStream && !errorStream.destroyed) {
        errorStream.end();
        const newErrorLogFile = path.join(
          logDir,
          `error-${tomorrow.toISOString().split("T")[0]}.log`
        );
        errorStream = fs.createWriteStream(newErrorLogFile, { flags: "a" });
      }
      scheduleNextDayLogFile(); // Schedule next day
    }, timeUntilNextDay);
  };

  scheduleNextDayLogFile();
};

export { setupLogger };
