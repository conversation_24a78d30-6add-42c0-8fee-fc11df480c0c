'use strict';
import { app, dialog, autoUpdater } from 'electron';
import os from 'os';

const version: string = app.getVersion();
const platform: string = `${os.platform()}_${os.arch()}`; // usually returns darwin_x64
const updaterFeedURL: string = `https://update.snapboxvietnam.com/update/${platform}/${version}`;

export function appUpdater(): void {
  autoUpdater.setFeedURL({ url: updaterFeedURL });

  // Log events
  autoUpdater.on('error', (err) => {
    console.log('Error in auto-updater: ', err);
    console.log('Update feed URL: ', updaterFeedURL);
  });
  
  autoUpdater.on('checking-for-update', () => {
    console.log('checking-for-update');
    console.log('Current version:', version);
    console.log('Platform:', platform);
  });
  autoUpdater.on('update-available', () => {
    console.log('update-available');
  });
  autoUpdater.on('update-not-available', () => console.log('update-not-available'));

  // Ask the user if update is available
  autoUpdater.on('update-downloaded', (info: any) => {
    console.log('update-downloaded', info);
    // Ask user to update the app
    dialog
      .showMessageBox({
        type: 'question',
        buttons: ['Cài đặt và khởi động lại', 'Để sau'],
        defaultId: 0,
        message: `Phiên bản mới`,
        detail: `Đã có phiên bản mới của ứng dụng đã được tải xuống`,
      })
      .then((response) => {
        if (response.response === 0) {
          setTimeout(() => autoUpdater.quitAndInstall(), 1);
        }
      });
  });

  // Init for updates
  autoUpdater.checkForUpdates();
}