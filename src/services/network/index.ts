import { BrowserWindow, ipcMain } from 'electron';
import dns from 'dns';

export class NetworkService {
  private mainWindow: BrowserWindow;
  private isOnline = true;
  private checkInterval: NodeJS.Timeout | null = null;

  constructor(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow;
    this.init();
  }

  private init() {
    // Kiểm tra network khi khởi động
    this.checkNetworkStatus();

    // Handle IPC calls
    ipcMain.handle('check-network', () => {
      return this.isOnline;
    });

    // Listen for online/offline events from renderer
    ipcMain.on('network-status-changed', (_, status: boolean) => {
      this.isOnline = status;
      this.broadcastStatus();
    });
  }

  private async checkNetworkStatus() {
    try {
      await new Promise((resolve, reject) => {
        dns.lookup('google.com', (err) => {
          if (err) {
            this.isOnline = false;
            reject(err);
          } else {
            this.isOnline = true;
            resolve(true);
          }
        });
      });
    } catch (error) {
      this.isOnline = false;
      console.error('Network check failed:', error);
    }
    
    this.broadcastStatus();
  }

  private broadcastStatus() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('network-status', this.isOnline);
    }
  }

  public startNetworkMonitoring() {
    // Clear any existing interval
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    // Check network status every 30 seconds
    this.checkInterval = setInterval(() => {
      this.checkNetworkStatus();
    }, 30000);
  }

  public stopNetworkMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }
}
