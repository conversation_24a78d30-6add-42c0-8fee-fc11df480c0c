// @ts-nocheck
import { SerialPort } from "serialport";
import IR<PERSON>y from "./IRelay";
import { exec } from "child_process";

// Constants
const DEFAULT_PORT = "COM6";
const DEFAULT_BAUD_RATE = 9600;
const COMMANDS = {
  TURN_ON: {
    CAMERA: Buffer.from("55560000000101AD", "hex"),
    TOP: Buffer.from("55560000000201AE", "hex"),
  },
  TURN_OFF: {
    CAMERA: Buffer.from("55560000000102AE", "hex"),
    TOP: Buffer.from("55560000000202AF", "hex"),
  },
};

const NEW_RELAY_COMMANDS = {
  TURN_ON: {
    CAMERA: Buffer.from("11", "hex"),
    CAMERA_SECONDARY: Buffer.from("41", "hex"),
    SIDE: Buffer.from("21", "hex"),
    TOP: Buffer.from("31", "hex"),
  },
  TURN_OFF: {
    CAMERA: Buffer.from("12", "hex"),
    CAMERA_SECONDARY: Buffer.from("42", "hex"),
    SIDE: Buffer.from("22", "hex"),
    TOP: Buffer.from("32", "hex"),
  },
};

const MAX_RECONNECT_ATTEMPTS = 3;
const RECONNECT_DELAY = 5000;
const COMMAND_TIMEOUT = 5000;
const CONNECTION_TIMEOUT = 5000;

class RelayService extends IRelay {
  private static instance: RelayService | null = null;
  private port: SerialPort | null = null;
  private reconnectAttempts = 0;
  private connectionPromise: Promise<void> | null = null;
  private abortController = new AbortController();
  private isOnline = false;
  private isConnected = false;
  private isNewRelay = false;
  private isMultipleCamera = false;
  private commands = COMMANDS;

  private constructor() {
    super();
    this.setupCleanupHandlers();
  }

  public static getInstance(): RelayService {
    if (!RelayService.instance) {
      RelayService.instance = new RelayService();
    }
    return RelayService.instance;
  }

  private setupCleanupHandlers(): void {
    const cleanupHandler = () => {
      this.cleanup().catch((error) => console.error("Cleanup error:", error));
    };

    process.on("exit", cleanupHandler);
    process.on("SIGINT", cleanupHandler);
    process.on("uncaughtException", cleanupHandler);
    process.on("unhandledRejection", cleanupHandler);
  }

  public async connect(
    portName = DEFAULT_PORT,
    baudRate = DEFAULT_BAUD_RATE,
    isNewRelay = false,
    isMultipleCamera = false
  ): Promise<void> {
    this.isNewRelay = isNewRelay;
    this.isMultipleCamera = isMultipleCamera;

    if (isNewRelay) {
      this.commands = NEW_RELAY_COMMANDS;
    }

    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    try {
      this.connectionPromise = this.attemptConnection(portName, baudRate);
      return await this.connectionPromise;
    } finally {
      this.connectionPromise = null;
    }
  }

  private async attemptConnection(
    portName: string,
    baudRate: number
  ): Promise<void> {
    const { signal } = this.abortController;

    return new Promise((resolve, reject) => {
      const cleanup = () => {
        clearTimeout(timer);
        port.removeAllListeners();
      };

      const port = new SerialPort({
        path: portName,
        baudRate,
        autoOpen: false,
        autoClose: true,
        endOnClose: true,
        dataBits: 8,
        parity: "none",
        stopBits: 1,
      });

      port.on("error", (error) => {
        console.error("Oh no error!");
        console.error(error);
      });

      const timer = setTimeout(() => {
        cleanup();
        reject(new Error("Connection timeout"));
      }, CONNECTION_TIMEOUT);

      const abortHandler = () => {
        cleanup();
        reject(new Error("Connection aborted"));
      };

      const openHandler = () => {
        cleanup();
        this.port = port;
        this.reconnectAttempts = 0;
        console.log(`Connected to ${portName}`);
        resolve();
      };

      const errorHandler = (err: Error) => {
        cleanup();
        reject(err);
      };

      signal.addEventListener("abort", abortHandler, { once: true });
      port.once("open", openHandler);
      port.once("error", errorHandler);

      try {
        port.open();
      } catch (error) {
        cleanup();
        reject(error);
      }
    });
  }

  private async handleConnectionError(
    error: Error,
    portName: string,
    baudRate: number
  ): Promise<boolean> {
    if (this.reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
      return false;
    }

    this.reconnectAttempts++;
    console.log(
      `Reconnecting (${this.reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})...`
    );

    await new Promise((resolve) => setTimeout(resolve, RECONNECT_DELAY));

    try {
      await this.connect(portName, baudRate);
      return true;
    } catch {
      return false;
    }
  }

  async sendBatchCommand(commands: Buffer[]): Promise<void> {
    for (const command of commands) {
      await this.sendCommand(command);
      await new Promise((resolve) => setTimeout(resolve, 200));
    }
  }

  public async turnOn(): Promise<void> {
    try {
      if (this.isOnline) {
        return;
      }

      await this.validateConnection();
      const commands = [
        this.commands.TURN_ON.CAMERA,
        this.commands.TURN_ON.TOP,
      ];

      if (this.isMultipleCamera) {
        commands.push(this.commands.TURN_ON.CAMERA_SECONDARY);
      }

      if (this.isNewRelay) {
        commands.push(this.commands.TURN_ON.SIDE);
      }

      await this.sendBatchCommand(commands);
    } catch (error) {
      console.error("Error turning on relay:", error);
    }
  }

  public async turnOnCamera(): Promise<void> {
    try {
      if (this.isOnline) {
        return;
      }

      await this.validateConnection();
      const commands = [this.commands.TURN_ON.CAMERA];

      if (this.isMultipleCamera) {
        commands.push(this.commands.TURN_ON.CAMERA_SECONDARY);
      }

      await this.sendBatchCommand(commands);
    } catch (error) {
      console.error("Error turning on relay:", error);
    }
  }

  public async turnOff(): Promise<void> {
    try {
      if (!this.isOnline) {
        return;
      }

      await this.validateConnection();
      const commands = [
        this.commands.TURN_OFF.CAMERA,
        this.commands.TURN_OFF.TOP,
      ];

      if (this.isMultipleCamera) {
        commands.push(this.commands.TURN_OFF.CAMERA_SECONDARY);
      }

      if (this.isNewRelay) {
        commands.push(this.commands.TURN_OFF.SIDE);
      }

      await this.sendBatchCommand(commands);
    } catch (error) {
      console.error("Error turning off relay:", error);
    }
  }

  public async turnOffCamera(): Promise<void> {
    try {
      if (!this.isOnline) {
        return;
      }

      await this.validateConnection();
      const commands = [this.commands.TURN_OFF.CAMERA];

      if (this.isMultipleCamera) {
        commands.push(this.commands.TURN_OFF.CAMERA_SECONDARY);
      }

      await this.sendBatchCommand(commands);
    } catch (error) {
      console.error("Error turning off relay:", error);
    }
  }

  private async validateConnection(): Promise<void> {
    if (!this.port?.isOpen) {
      throw new Error("Device not connected");
    }
  }

  private async sendCommand(
    command: Buffer,
    callback?: () => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.port?.isOpen) {
        return reject(new Error("Port not connected"));
      }

      this.port?.write(command, (error) => {
        if (error) {
          console.error("[relay] Error sending command:", error);
          return reject(error);
        }
        console.log("[relay] Command written successfully");
        callback?.();
      });

      this.port?.drain((drainError) => {
        if (drainError) {
          console.error("[relay] Error draining:", drainError);
          return reject(drainError);
        }

        if (command === this.commands.TURN_ON.CAMERA) {
          this.isOnline = true;
          console.log("[Relay] turned on camera");
        }

        if (command === this.commands.TURN_OFF.CAMERA) {
          this.isOnline = false;
          console.log("[Relay] turned off camera");
        }

        resolve();
        console.log("[relay] Command drained successfully");
      });
    });
  }

  public async disconnect(): Promise<void> {
    if (!this.isConnected) {
      console.log("Already disconnected");
      return;
    }

    try {
      await this.turnOff();
      this.abortController.abort();
      this.reconnectAttempts = MAX_RECONNECT_ATTEMPTS;

      if (!this.port) {
        console.log("Port not available");
        return;
      }

      await new Promise<void>((resolve, reject) => {
        this.port?.close((error) => {
          if (error) {
            console.error("Disconnect error:", error);
            reject(error);
            return;
          }
          this.isConnected = false;
          console.log("Disconnect completed");
          resolve();
        });
      });
    } catch (error) {
      console.error("Error during disconnect:", error);
      throw error;
    } finally {
      this.port = null;
    }
  }

  public async cleanup(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.disconnect();
      }
    } catch (error) {
      console.error("Cleanup error:", error);
    } finally {
      this.port = null;
      this.connectionPromise = null;
      this.reconnectAttempts = 0;
      this.abortController = new AbortController();
      this.isConnected = false;
    }
  }

  private async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number = 5000
  ): Promise<T> {
    const timeout = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Operation timeout")), timeoutMs)
    );

    return Promise.race([operation(), timeout]);
  }
}

export const relayService = RelayService.getInstance();
