<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <style>
      body {
        font-family: Arial, sans-serif;
        background: white;
      }
      .container {
        background: white;
        padding: 20px;
      }
      h2 {
        margin-top: 0;
        color: #333;
      }
      input {
        width: 100%;
        padding: 8px;
        margin: 10px 0;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
      .buttons {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
      }
      button {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      .confirm {
        background: #4caf50;
        color: white;
      }
      .cancel {
        background: #f44336;
        color: white;
      }
      .error-message {
        color: #f44336;
        margin: 10px 0;
        font-size: 14px;
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>Xác thực mật khẩu</h2>
      <p><PERSON><PERSON> lòng nhập mật khẩu để tiếp tục</p>
      <input type="password" id="password" placeholder="Nhập mật khẩu" />
      <div id="error" class="error-message"></div>
      <div class="buttons">
        <button class="cancel" onclick="window.api.cancelPassword()">
          Hủy
        </button>
        <button class="confirm" onclick="handleSubmit()">Xác nhận</button>
      </div>
    </div>

    <script>
      // Handle Enter key
      document
        .getElementById("password")
        .addEventListener("keypress", function (e) {
          if (e.key === "Enter") {
            handleSubmit();
          }
        });

      // Handle submit
      function handleSubmit() {
        const password = document.getElementById("password").value;
        window.api.submitPassword(password);
      }

      // Handle error message
      window.api.onError((message) => {
        const errorElement = document.getElementById("error");
        errorElement.textContent = message;
        errorElement.style.display = "block";
        // Clear error after 3 seconds
        setTimeout(() => {
          errorElement.style.display = "none";
        }, 3000);
      });
    </script>
  </body>
</html>
