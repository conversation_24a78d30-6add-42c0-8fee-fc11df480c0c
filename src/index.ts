import {
  app,
  BrowserWindow,
  clipboard,
  Data,
  ipcMain,
  Menu,
  MenuItemConstructorOptions,
  nativeImage,
  net,
  powerMonitor,
  protocol,
  screen,
} from "electron";
import Store from "electron-store";
import fs from "fs";
import path from "path";
import {
  disconnectRelay,
  initIpc,
  initMainWindow,
  initSecondaryWindow,
  releaseNativeResources,
  initIpcWithoutWindow,
} from "./services/ipc";
import { setupLogger } from "./services/logger";
import { NetworkService } from "./services/network";
import { appUpdater } from "./services/auto-upadater";
import { exec, spawn } from "child_process";
import { getConfig } from "@/cores/utils/config";
import reduxStore from "@/redux/store.main";
import { clearReduxState } from "@/redux/store.persistence";

const IDLE_LIMIT = 10 * 60;
const config = getConfig();

let networkService: NetworkService;
let loopInterval: any;
let aiChildProcess: any;

const snapboxAI = path.join(
  "C:",
  "Users",
  "M710q",
  "snapbox",
  "snapbox-ai.exe"
);

// Add password dialog function
const showPasswordDialog = async (
  parentWindow: BrowserWindow
): Promise<boolean> => {
  return new Promise((resolve) => {
    const passwordWindow = new BrowserWindow({
      width: 600,
      height: 320,
      resizable: false,
      minimizable: false,
      maximizable: false,
      parent: parentWindow,
      modal: true,
      title: "Snapbox Vietnam",
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: true,
        preload: path.join(__dirname, "preload-password.js"),
      },
    });

    // Load HTML file
    passwordWindow.loadFile(path.join(__dirname, "password-dialog.html"));

    // Handle password result
    ipcMain.once("password-submit", (event, password) => {
      if (password === "26012024") {
        passwordWindow.close();
        resolve(true);
      } else {
        event.reply("password-error", "Mật khẩu không đúng!");
      }
    });

    ipcMain.once("password-cancel", () => {
      passwordWindow.close();
      resolve(false);
    });

    passwordWindow.on("closed", () => {
      resolve(false);
    });
  });
};

const startSnapboxAI = () => {
  aiChildProcess = spawn(snapboxAI, [], {
    windowsHide: true,
  });

  aiChildProcess.stdout.on("data", (data: any) => {
    console.log("Snapbox AI Output:", data.toString());
  });

  aiChildProcess.stderr.on("data", (data: any) => {
    console.error("Snapbox AI Error:", data.toString());
  });

  aiChildProcess.on("close", (code: any) => {
    console.log(`Snapbox AI process exited with code ${code}`);
  });
};

const startHFP = () => {
  exec(
    'powershell -Command "Start-Process \\"C:\\Users\\<USER>\\snapbox\\hfp.ahk\\""',
    {
      windowsHide: true,
    },
    (error: any, stdout: any, stderr: any) => {
      if (error) {
        console.error("Error executing HotFolderPrint:", error);
      } else {
        console.log("HotFolderPrint executed successfully");
      }
    }
  );
};

// Create preload script for password dialog
const createPreloadScript = () => {
  const preloadPath = path.join(__dirname, "preload-password.js");
  const preloadContent = `
    const { contextBridge, ipcRenderer } = require('electron');

    contextBridge.exposeInMainWorld('api', {
      submitPassword: (password) => {
        ipcRenderer.send('password-submit', password);
      },
      cancelPassword: () => {
        ipcRenderer.send('password-cancel');
      },
      onError: (callback) => {
        ipcRenderer.on('password-error', (_, message) => {
          callback(message);
        });
      }
    });
  `;

  fs.writeFileSync(preloadPath, preloadContent);
};

const template: MenuItemConstructorOptions[] = [
  {
    label: "Quản trị",
    submenu: [
      {
        label: `Phiên bản ${app.getVersion()}`,
        enabled: false,
      },
      {
        label: "Về màn hình chính",
        click: async () => {
          try {
            const mainWindow = BrowserWindow.getFocusedWindow();
            if (mainWindow) {
              BrowserWindow.getAllWindows().forEach(async (window) => {
                window.webContents.send("idle-state");
              });

              setTimeout(async () => {
                await disconnectRelay();
              }, 1000);
            }
          } catch (error) {
            console.error("[View] Error disconnecting relay:", error);
          }
        },
      },
      {
        label: "Cập nhật phiên bản mới",
        click: async () => {
          const mainWindow = BrowserWindow.getFocusedWindow();
          if (mainWindow) {
            appUpdater();
          }
        },
      },
      { role: "toggleDevTools", label: "Kiểm tra phần tử" },
      {
        label: "Đăng xuất",
        click: async () => {
          try {
            const mainWindow = BrowserWindow.getFocusedWindow();
            if (mainWindow && (await showPasswordDialog(mainWindow))) {
              // Clear all data from electron-store
              store.clear();

              // Clear Redux state
              clearReduxState();

              // Reset all Redux slices
              reduxStore.dispatch({ type: "auth/reset" });
              reduxStore.dispatch({ type: "app/reset" });
              reduxStore.dispatch({ type: "order/reset" });
              reduxStore.dispatch({ type: "waitingScreen/reset" });

              // Reload all windows
              BrowserWindow.getAllWindows().forEach((window) => {
                window.reload();
              });
            }
          } catch (error) {
            console.error("[Logout] Error clearing data:", error);
          }
        },
      },
      { role: "quit", label: "Thoát ứng dụng" },
    ],
  },
];

// Add this after template definition
const menu = Menu.buildFromTemplate(template);
Menu.setApplicationMenu(menu);

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require("electron-squirrel-startup")) {
  app.quit();
}

const store: any = new Store({
  projectName: "snapbox",
});

// Ensure we're in the main process before registering protocols
if (app) {
  const gotTheLock = app.requestSingleInstanceLock();

  if (!gotTheLock) {
    app.quit();
  }

  protocol.registerSchemesAsPrivileged([
    {
      scheme: "app",
      privileges: {
        secure: true,
        supportFetchAPI: true,
        bypassCSP: true,
      },
    },
    {
      scheme: "video",
      privileges: {
        secure: false,
        supportFetchAPI: true,
        bypassCSP: true,
        stream: true,
      },
    },
  ]);

  // Xử lý protocol riêng biệt
  const handleProtocols = async () => {
    protocol.handle("app", async (request) => {
      const pathToMedia = decodeURIComponent(new URL(request.url).pathname);
      const normalizedPath = pathToMedia.replace(/\\/g, "/");
      const filePath = path.normalize(normalizedPath);

      try {
        return await net.fetch(`file://${filePath}`);
      } catch (error) {
        console.error(
          `Error handling app protocol for path ${filePath}:`,
          error
        );
        throw error;
      }
    });

    protocol.handle("video", async (request) => {
      const url = new URL(request.url);
      let pathToMedia = decodeURIComponent(url.pathname);

      if (process.platform === "win32") {
        pathToMedia = pathToMedia
          .replace(/^\/+/, "")
          .replace(/^([a-z])(:)/, "$1:");
      }

      const MAX_RETRIES = 3;
      let retryCount = 0;

      while (retryCount < MAX_RETRIES) {
        try {
          // Check if file exists and is accessible
          if (!fs.existsSync(pathToMedia)) {
            throw new Error(`Video file not found: ${pathToMedia}`);
          }

          const stats = fs.statSync(pathToMedia);
          if (stats.size === 0) {
            throw new Error(`Video file is empty: ${pathToMedia}`);
          }

          const fileStream = fs.createReadStream(pathToMedia);

          // Handle stream errors
          fileStream.on("error", (error) => {
            console.error(`Stream error for ${pathToMedia}:`, error);
            throw error;
          });

          return new Response(fileStream as any, {
            status: 200,
            headers: {
              "Content-Type": "video/webm",
              "Content-Length": stats.size.toString(),
            },
          });
        } catch (error) {
          console.error(
            `Error handling video protocol for path ${pathToMedia} (attempt ${retryCount + 1}/${MAX_RETRIES}):`,
            error
          );

          retryCount++;
          if (retryCount === MAX_RETRIES) {
            throw error;
          }

          // Wait before retrying
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
    });
  };

  ipcMain.on("get-initial-state", (event) => {
    try {
      const state = reduxStore.getState() ?? {};
      // Chuyển đổi state thành chuỗi JSON
      const stateJson = JSON.stringify(state);
      // Trả về state cho renderer process
      event.returnValue = stateJson;

      // Log để kiểm tra
      console.log("[Main Process] Initial state sent to renderer");
    } catch (error) {
      console.error("[Main Process] Error sending initial state:", error);
      event.returnValue = JSON.stringify({});
    }
  });

  // Thêm listener để kiểm tra các action từ renderer
  ipcMain.on("redux-action", (event, action) => {
    if (process.env.NODE_ENV === "development") {
      console.log(
        "[Main Process] Received redux action from renderer:",
        action
      );
    }
  });

  app.disableHardwareAcceleration();
  app.whenReady().then(async () => {
    try {
      let secondaryWindow: BrowserWindow | null = null;
      const displays = screen.getAllDisplays();
      process.env.IS_OVERHEAD_MACHINE =
        displays.length > 1 && !config.enableMultipleCameras ? "true" : "false";
      process.env.IS_MULTIPLE_CAMERA = config.enableMultipleCameras
        ? "true"
        : "false";
      process.env.IS_ENABLE_REMOVE_BACKGROUND = config.isEnableRemoveBackground
        ? "true"
        : "false";
      // Thêm đoạn code này trước khi gọi PrinterServiceManager.cleanupOrphanedProcesses()
      process.env.PM2_HOME = path.join(app.getPath("userData"), ".pm2");
      // Đảm bảo protocol handlers được thiết lập trước
      await handleProtocols();

      try {
        // Delete all files in temp folder
        const tempDir = path.join(app.getPath("userData"), "temp");
        if (fs.existsSync(tempDir)) {
          fs.readdirSync(tempDir).forEach((file) => {
            fs.unlinkSync(path.join(tempDir, file));
          });
        }
      } catch (error) {
        console.error("Error deleting temp files:", error);
      }

      if (config.isEnableRemoveBackground) {
        startSnapboxAI();
      }

      startHFP();

      await initIpcWithoutWindow();

      // Sau đó mới tạo window và khởi tạo các service khác
      const mainWindow = await createPrimaryWindow();
      networkService = new NetworkService(mainWindow);

      if (
        process.env.IS_OVERHEAD_MACHINE === "true" ||
        process.env.IS_MULTIPLE_CAMERA === "true"
      ) {
        secondaryWindow = await createSecondaryWindow();
      }

      await initIpc(mainWindow, secondaryWindow);
      await setupLogger();

      app.commandLine.appendSwitch("enable-gpu");
      app.commandLine.appendSwitch("no-crashpad");
      app.commandLine.appendSwitch("disable-crash-reporter");
      app.commandLine.appendSwitch("disable-features", "Crashpad");

      loopInterval = setInterval(async () => {
        const idleTime = powerMonitor.getSystemIdleTime();
        if (idleTime >= IDLE_LIMIT) {
          mainWindow.webContents.send("idle-state");
          secondaryWindow?.webContents?.send("idle-state");
          await disconnectRelay();
        }
      }, 1000);

      // Store handlers
      ipcMain.on("electron-store-get", async (event, val) => {
        event.returnValue = store.get(val);
      });

      ipcMain.on("electron-store-set", async (_, key, val) => {
        store.set(key, val);
      });

      ipcMain.on("electron-store-delete", async (_, key) => {
        try {
          store.delete(key);
        } catch (error) {
          console.log("🚀 ~ ipcMain.on ~ error:", error);
        }
      });

      ipcMain.on("clipboard", async (_, val: Data & { img?: string }) => {
        if (val.img) {
          clipboard.writeImage(
            nativeImage.createFromBuffer(Buffer.from(val.img, "base64"))
          );
        } else {
          clipboard.write(val);
        }
      });

      if (app.dock) {
        const image = nativeImage.createFromPath(
          app.getAppPath() + "/images/white-logo.png"
        );
        app.dock.setIcon(image);
      }

      createPreloadScript();
    } catch (error) {
      console.error("Error during app initialization:", error);
    }
  });

  // Quit when all windows are closed, except on macOS. There, it's common
  // for applications and their menu bar to stay active until the user quits
  // explicitly with Cmd + Q.
  app.on("window-all-closed", async () => {
    if (process.platform !== "darwin") {
      app.quit();
    }

    if (networkService) {
      console.log("Stopping network monitoring.");
      networkService.stopNetworkMonitoring();
      clearInterval(loopInterval);
    }

    aiChildProcess?.kill();
  });

  app.on("before-quit", async (event) => {
    try {
      console.log("App is quitting. Cleaning up resources.");
      event.preventDefault();
      clearInterval(loopInterval);

      await releaseNativeResources();
      app.exit(1);
    } catch (error) {
      console.error("Error during app quitting:", error);
      app.exit(1);
    }
  });

  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createPrimaryWindow();
      createSecondaryWindow();
    }
  });
}

const createPrimaryWindow = async (): Promise<BrowserWindow> => {
  try {
    const displays = screen.getAllDisplays();
    console.log("displays", displays);
    let primaryDisplay = displays.find(
      (display) => display.label === config.primaryScreen
    );
    if (!primaryDisplay) {
      primaryDisplay = displays[0];
    }
    const wdw = await initMainWindow(primaryDisplay);
    // Add context menu
    wdw.webContents.on("context-menu", (_, _props) => {
      const contextMenu = Menu.buildFromTemplate(template);

      contextMenu.popup({
        window: wdw,
        x: 0, // Đặt vị trí x = 0 để menu hiện ở bên trái
        y: 0, // Đặt vị trí y = 0 để menu hiện ở trên cùng
      });
    });

    wdw.webContents.on("did-finish-load", async () => {
      console.log("did-finish-load event triggered");
      networkService.startNetworkMonitoring();
    });

    wdw.webContents.on("did-navigate-in-page", (event, url) => {
      wdw.webContents.sendInputEvent({ type: "mouseMove", x: 0, y: 0 });
    });

    wdw.on("close", () => {
      wdw.destroy();
    });

    return wdw;
  } catch (error) {
    console.error("Error creating window:", error);
    throw error;
  }
};

const createSecondaryWindow = async (): Promise<BrowserWindow> => {
  try {
    const displays = screen.getAllDisplays();
    let secondaryDisplay = displays.find(
      (display) => display.label === config.overheadScreen
    );
    if (!secondaryDisplay) {
      secondaryDisplay = displays[1];
    }

    const wdw = await initSecondaryWindow(secondaryDisplay);
    wdw.webContents.on("did-finish-load", async () => {
      console.log("did-finish-load event triggered");
      networkService.startNetworkMonitoring();
    });

    wdw.webContents.on("did-navigate-in-page", (event, url) => {
      wdw.webContents.sendInputEvent({ type: "mouseMove", x: 0, y: 0 });
    });

    wdw.on("close", () => {
      wdw.destroy();
    });

    return wdw;
  } catch (error) {
    console.error("Error creating window:", error);
    throw error;
  }
};

// Add this function to log memory usage
function logMemoryUsage() {
  const memoryUsage = process.memoryUsage();
  const toMB = (bytes: any) => (bytes / 1024 / 1024).toFixed(2);

  console.log(
    `Memory Usage: RSS=${toMB(memoryUsage.rss)} MB, ` +
      `HeapTotal=${toMB(memoryUsage.heapTotal)} MB, ` +
      `HeapUsed=${toMB(memoryUsage.heapUsed)} MB`
  );
}

// Call this function periodically to monitor memory usage
if (process.env.NODE_ENV === "development") {
  setInterval(logMemoryUsage, 60000); // Log every 60 seconds
}

process.on("exit", async () => {
  await releaseNativeResources();

  aiChildProcess?.kill();
});
process.on("SIGINT", async () => {
  await releaseNativeResources();

  aiChildProcess?.kill();
});

process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);

  aiChildProcess?.kill();
});

process.on("unhandledRejection", (error) => {
  console.error("Unhandled Rejection:", error);
});
