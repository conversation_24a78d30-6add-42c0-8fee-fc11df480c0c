{"v": "5.7.6", "fr": 30, "ip": 0, "op": 150, "w": 800, "h": 800, "nm": "Digital Camera", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Digital Camera", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 394, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [37.72, 29, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 1, "s": [77.739, 77.739, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 2, "s": [165.944, 165.944, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 3, "s": [257.937, 257.937, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 4, "s": [348.237, 348.237, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.177, 0.177, 0]}, "t": 5, "s": [432.589, 432.589, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.849, 0.849, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.181, 0.181, 0]}, "t": 6, "s": [507.928, 507.928, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.853, 0.853, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.186, 0.186, 0]}, "t": 7, "s": [572.285, 572.285, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.858, 0.858, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.192, 0.192, 0]}, "t": 8, "s": [624.663, 624.663, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.865, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.201, 0.201, 0]}, "t": 9, "s": [664.882, 664.882, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.877, 0.877, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.217, 0.217, 0]}, "t": 10, "s": [693.421, 693.421, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.911, 0.911, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.259, 0.259, 0]}, "t": 11, "s": [711.254, 711.254, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.73, 1.73, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [1.294, 1.294, 0]}, "t": 12, "s": [719.701, 719.701, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.765, 0.765, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.075, 0.075, 0]}, "t": 13, "s": [720.282, 720.282, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.808, 0.808, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.129, 0.129, 0]}, "t": 14, "s": [714.606, 714.606, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.147, 0.147, 0]}, "t": 15, "s": [704.264, 704.264, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 16, "s": [690.755, 690.755, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 17, "s": [675.428, 675.428, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 18, "s": [659.442, 659.442, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 19, "s": [643.75, 643.75, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.177, 0.177, 0]}, "t": 20, "s": [629.092, 629.092, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.849, 0.849, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.181, 0.181, 0]}, "t": 21, "s": [616, 616, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.853, 0.853, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.186, 0.186, 0]}, "t": 22, "s": [604.816, 604.816, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.858, 0.858, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.192, 0.192, 0]}, "t": 23, "s": [595.714, 595.714, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.865, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.201, 0.201, 0]}, "t": 24, "s": [588.725, 588.725, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.877, 0.877, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.217, 0.217, 0]}, "t": 25, "s": [583.766, 583.766, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.911, 0.911, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.259, 0.259, 0]}, "t": 26, "s": [580.667, 580.667, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.73, 1.73, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [1.294, 1.294, 0]}, "t": 27, "s": [579.199, 579.199, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.765, 0.765, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.075, 0.075, 0]}, "t": 28, "s": [579.098, 579.098, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.808, 0.808, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.129, 0.129, 0]}, "t": 29, "s": [580.084, 580.084, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.147, 0.147, 0]}, "t": 30, "s": [581.882, 581.882, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 31, "s": [584.229, 584.229, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 32, "s": [586.893, 586.893, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 33, "s": [589.671, 589.671, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 34, "s": [592.397, 592.397, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.177, 0.177, 0]}, "t": 35, "s": [594.945, 594.945, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.849, 0.849, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.181, 0.181, 0]}, "t": 36, "s": [597.22, 597.22, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.853, 0.853, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.186, 0.186, 0]}, "t": 37, "s": [599.163, 599.163, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.858, 0.858, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.192, 0.192, 0]}, "t": 38, "s": [600.745, 600.745, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.865, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.201, 0.201, 0]}, "t": 39, "s": [601.959, 601.959, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.877, 0.877, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.217, 0.217, 0]}, "t": 40, "s": [602.821, 602.821, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.911, 0.911, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.259, 0.259, 0]}, "t": 41, "s": [603.36, 603.36, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.73, 1.73, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [1.294, 1.294, 0]}, "t": 42, "s": [603.615, 603.615, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.765, 0.765, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.075, 0.075, 0]}, "t": 43, "s": [603.632, 603.632, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.808, 0.808, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.129, 0.129, 0]}, "t": 44, "s": [603.461, 603.461, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.147, 0.147, 0]}, "t": 45, "s": [603.149, 603.149, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 46, "s": [602.741, 602.741, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.816, 0.816, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 47, "s": [602.278, 602.278, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.848, 0.848, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.152, 0.152, 0]}, "t": 48, "s": [601.795, 601.795, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.852, 0.852, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.185, 0.185, 0]}, "t": 49, "s": [601.211, 601.211, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.857, 0.857, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.191, 0.191, 0]}, "t": 50, "s": [600.732, 600.732, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.863, 0.863, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.199, 0.199, 0]}, "t": 51, "s": [600.362, 600.362, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.871, 0.871, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.212, 0.212, 0]}, "t": 52, "s": [600.097, 600.097, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.887, 0.887, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.235, 0.235, 0]}, "t": 53, "s": [599.925, 599.925, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.94, 0.94, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.315, 0.315, 0]}, "t": 54, "s": [599.83, 599.83, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.593, 0.593, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-0.21, -0.21, 0]}, "t": 55, "s": [599.796, 599.796, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.802, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.105, 0.105, 0]}, "t": 56, "s": [599.805, 599.805, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.828, 0.828, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 57, "s": [599.843, 599.843, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.841, 0.841, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 58, "s": [599.895, 599.895, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.175, 0.175, 0]}, "t": 59, "s": [599.95, 599.95, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.004, 0.004, 0]}, "t": 60, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 61, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 63, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 64, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 65, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 66, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 67, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 68, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 69, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 70, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 71, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 72, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 73, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 74, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 75, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 76, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 77, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 78, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 79, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 80, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 81, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 82, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 83, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 84, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 85, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 86, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 87, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 88, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.004, 1.004, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 89, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.825, 0.825, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 90, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.159, 0.159, 0]}, "t": 91, "s": [599.949, 599.949, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.858, 0.858, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 92, "s": [599.893, 599.893, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.901, 0.901, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.201, 0.201, 0]}, "t": 93, "s": [599.841, 599.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.389, 1.389, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.523, 0.523, 0]}, "t": 94, "s": [599.804, 599.804, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.698, 0.698, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.069, 0.069, 0]}, "t": 95, "s": [599.797, 599.797, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.767, 0.767, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.115, 0.115, 0]}, "t": 96, "s": [599.836, 599.836, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.79, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.13, 0.13, 0]}, "t": 97, "s": [599.941, 599.941, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.801, 0.801, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.138, 0.138, 0]}, "t": 98, "s": [600.127, 600.127, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.809, 0.809, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 99, "s": [600.412, 600.412, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.821, 0.821, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.148, 0.148, 0]}, "t": 100, "s": [600.806, 600.806, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.846, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.156, 0.156, 0]}, "t": 101, "s": [601.312, 601.312, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.182, 0.182, 0]}, "t": 102, "s": [601.893, 601.893, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.172, 0.172, 0]}, "t": 103, "s": [602.383, 602.383, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.857, 0.857, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.181, 0.181, 0]}, "t": 104, "s": [602.844, 602.844, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.88, 0.88, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.199, 0.199, 0]}, "t": 105, "s": [603.239, 603.239, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.973, 0.973, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.272, 0.272, 0]}, "t": 106, "s": [603.523, 603.523, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.576, 0.576, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-0.04, -0.04, 0]}, "t": 107, "s": [603.649, 603.649, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.759, 0.759, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.104, 0.104, 0]}, "t": 108, "s": [603.564, 603.564, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.789, 0.789, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.127, 0.127, 0]}, "t": 109, "s": [603.219, 603.219, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.802, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.138, 0.138, 0]}, "t": 110, "s": [602.567, 602.567, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.81, 0.81, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 111, "s": [601.569, 601.569, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.816, 0.816, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.149, 0.149, 0]}, "t": 112, "s": [600.198, 600.198, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.821, 0.821, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.152, 0.152, 0]}, "t": 113, "s": [598.447, 598.447, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.825, 0.825, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.156, 0.156, 0]}, "t": 114, "s": [596.332, 596.332, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.159, 0.159, 0]}, "t": 115, "s": [593.894, 593.894, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 116, "s": [591.211, 591.211, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 117, "s": [588.397, 588.397, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.848, 0.848, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.174, 0.174, 0]}, "t": 118, "s": [585.603, 585.603, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.861, 0.861, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.184, 0.184, 0]}, "t": 119, "s": [583.022, 583.022, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.891, 0.891, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.208, 0.208, 0]}, "t": 120, "s": [580.883, 580.883, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.072, 1.072, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.35, 0.35, 0]}, "t": 121, "s": [579.448, 579.448, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.677, 0.677, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.039, 0.039, 0]}, "t": 122, "s": [579, 579, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.77, 0.77, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.112, 0.112, 0]}, "t": 123, "s": [579.834, 579.834, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.793, 0.793, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.131, 0.131, 0]}, "t": 124, "s": [582.238, 582.238, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.805, 0.805, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.14, 0.14, 0]}, "t": 125, "s": [586.471, 586.471, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.812, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.145, 0.145, 0]}, "t": 126, "s": [592.741, 592.741, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.817, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.15, 0.15, 0]}, "t": 127, "s": [601.174, 601.174, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.153, 0.153, 0]}, "t": 128, "s": [611.788, 611.788, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.826, 0.826, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 129, "s": [624.457, 624.457, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 130, "s": [638.887, 638.887, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 131, "s": [654.585, 654.585, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.841, 0.841, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 132, "s": [670.843, 670.843, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.85, 0.85, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.176, 0.176, 0]}, "t": 133, "s": [686.726, 686.726, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.866, 0.866, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.188, 0.188, 0]}, "t": 134, "s": [701.074, 701.074, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.906, 0.906, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.22, 0.22, 0]}, "t": 135, "s": [712.516, 712.516, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.562, 1.562, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.737, 0.737, 0]}, "t": 136, "s": [719.513, 719.513, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.72, 0.72, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.073, 0.073, 0]}, "t": 137, "s": [720.404, 720.404, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.778, 0.778, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.119, 0.119, 0]}, "t": 138, "s": [713.497, 713.497, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.797, 0.797, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.133, 0.133, 0]}, "t": 139, "s": [697.164, 697.164, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.807, 0.807, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.141, 0.141, 0]}, "t": 140, "s": [669.971, 669.971, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.813, 0.813, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.146, 0.146, 0]}, "t": 141, "s": [630.824, 630.824, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.818, 0.818, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.15, 0.15, 0]}, "t": 142, "s": [579.136, 579.136, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.823, 0.823, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.154, 0.154, 0]}, "t": 143, "s": [514.998, 514.998, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.827, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 144, "s": [439.358, 439.358, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.161, 0.161, 0]}, "t": 145, "s": [354.185, 354.185, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 146, "s": [262.611, 262.611, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.843, 0.843, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 147, "s": [169.034, 169.034, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.178, 0.178, 0]}, "t": 148, "s": [79.167, 79.167, 100]}, {"t": 149, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "AC IN [YRH] Controls", "np": 4, "mn": "Pseudo/MHAC PrCtrl YRH 4", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Number of bounces", "mn": "Pseudo/MHAC PrCtrl YRH 4-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "Scale", "mn": "Pseudo/MHAC PrCtrl YRH 4-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": 5, "nm": "AC OUT [YRH] 2 Controls", "np": 4, "mn": "Pseudo/MHAC PrCtrl YRH 4", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Number of bounces", "mn": "Pseudo/MHAC PrCtrl YRH 4-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "Scale", "mn": "Pseudo/MHAC PrCtrl YRH 4-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -3.866], [3.866, 0], [0, 3.866], [-3.866, 0]], "o": [[0, 3.866], [-3.866, 0], [0, -3.866], [3.866, 0]], "v": [[7, 0], [0, 7], [-7, 0], [0, -7]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.149019613862, 0.196078434587, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 2", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.149019613862, 0.196078434587, 0.219607844949, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [3]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [1]}, {"t": 127, "s": [3]}], "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.72, 31.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.72, 31.5], "ix": 2}, "a": {"a": 0, "k": [37.72, 31.5], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 30, "s": [30, 30]}, {"i": {"x": [0.833, 0.833], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 40, "s": [100, 100]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0, 0]}, "t": 117, "s": [100, 100]}, {"t": 127, "s": [30, 30]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "O", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[21.22, 13.5], [25.22, 13.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.149000010771, 0.195999998205, 0.219999994016, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.67, "y": 1}, "o": {"x": 0.75, "y": 0}, "t": 30, "s": [0, 0], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.67, "y": 0.67}, "o": {"x": 0.75, "y": 0.75}, "t": 33, "s": [0, 1], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.67, "y": 1}, "o": {"x": 0.75, "y": 0}, "t": 35, "s": [0, 1], "to": [0, 0], "ti": [0, 0]}, {"t": 40, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[34.72, 11.958], [40.72, 11.958]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.553, 0], [0, 0], [0, 0.552], [0, 0], [-0.552, 0], [0, 0], [0, -0.552], [0, 0]], "o": [[0, 0], [-0.552, 0], [0, 0], [0, -0.552], [0, 0], [0.553, 0], [0, 0], [0, 0.552]], "v": [[1.25, 1.5], [-1.25, 1.5], [-2.25, 0.5], [-2.25, -0.5], [-1.25, -1.5], [1.25, -1.5], [2.25, -0.5], [2.25, 0.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [55.47, 20.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.523], [5.523, 0], [0, 5.523], [-5.522, 0]], "o": [[0, 5.523], [-5.522, 0], [0, -5.523], [5.523, 0]], "v": [[10, 0], [0, 10], [-10, 0], [0, -10]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.149000010771, 0.195999998205, 0.219999994016, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.72, 31.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.863, 0], [0, 0], [0.457, -0.731], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-0.862, 0], [0, 0], [0, 0], [0, 0], [-0.457, -0.731]], "v": [[5.614, -2], [-5.614, -2], [-7.734, -0.825], [-9.5, 2], [9.5, 2], [7.734, -0.825]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.149019613862, 0.196078449488, 0.21960785985, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.149000010771, 0.195999998205, 0.219999994016, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.72, 11.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 3, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[7.42, 0.088], [0, 0], [0, 0], [0, 0], [-2.761, 0], [0, 0], [0, 2.761], [0, 0], [-0.083, -0.083]], "o": [[-7, -0.083], [0, 0], [0, 0], [0, 2.761], [0, 0], [2.762, 0], [0, 0], [0, 0], [0, 0.083]], "v": [[-0.167, -16.833], [-10.5, -11], [-24, -11], [-24, 6], [-19, 11], [19, 11], [24, 6], [24, -11], [10.5, -11]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.149000010771, 0.195999998205, 0.219999994016, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.72, 36.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 3, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.762, 0], [0, 0], [0, 2.761], [0, 0], [-2.761, 0], [0, 0], [0, -2.762], [0, 0]], "o": [[0, 0], [-2.761, 0], [0, 0], [0, -2.762], [0, 0], [2.762, 0], [0, 0], [0, 2.761]], "v": [[19, 16], [-19, 16], [-24, 11], [-24, -11], [-19, -16], [19, -16], [24, -11], [24, 11]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [], "o": [], "v": [], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.149000010771, 0.195999998205, 0.219999994016, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.149019613862, 0.196078434587, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.72, 31.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 4, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.72, 28.5], "ix": 2}, "a": {"a": 0, "k": [37.72, 28.5], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Camera", "np": 8, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Small/BG-01 Outlines", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [47]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 141, "s": [47]}, {"t": 149, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [45]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.178]}, "t": 9, "s": [37.524]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.179]}, "t": 10, "s": [30.925]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.179]}, "t": 11, "s": [25.149]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 12, "s": [20.141]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 13, "s": [15.847]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 14, "s": [12.211]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.185]}, "t": 15, "s": [9.178]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.187]}, "t": 16, "s": [6.695]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.19]}, "t": 17, "s": [4.705]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.194]}, "t": 18, "s": [3.155]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.2]}, "t": 19, "s": [1.989]}, {"i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.207]}, "t": 20, "s": [1.153]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.22]}, "t": 21, "s": [0.592]}, {"i": {"x": [0.833], "y": [0.886]}, "o": {"x": [0.167], "y": [0.245]}, "t": 22, "s": [0.251]}, {"i": {"x": [0.833], "y": [0.904]}, "o": {"x": [0.167], "y": [0.308]}, "t": 23, "s": [0.075]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.65]}, "t": 24, "s": [0.01]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.001]}, "t": 25, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 83, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 85, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 86, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 91, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 92, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 93, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 94, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 111, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 112, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 113, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 114, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 115, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 116, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 117, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 121, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 122, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 123, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 124, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 125, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 126, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 127, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.333]}, "o": {"x": [0.167], "y": [0.083]}, "t": 129, "s": [0]}, {"i": {"x": [0.833], "y": [0.69]}, "o": {"x": [0.167], "y": [0.095]}, "t": 130, "s": [0.006]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.114]}, "t": 131, "s": [0.045]}, {"i": {"x": [0.833], "y": [0.779]}, "o": {"x": [0.167], "y": [0.126]}, "t": 132, "s": [0.152]}, {"i": {"x": [0.833], "y": [0.792]}, "o": {"x": [0.167], "y": [0.134]}, "t": 133, "s": [0.36]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.139]}, "t": 134, "s": [0.703]}, {"i": {"x": [0.833], "y": [0.806]}, "o": {"x": [0.167], "y": [0.143]}, "t": 135, "s": [1.215]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.146]}, "t": 136, "s": [1.929]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.148]}, "t": 137, "s": [2.88]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.15]}, "t": 138, "s": [4.101]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 139, "s": [5.625]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 140, "s": [7.487]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 141, "s": [9.72]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.155]}, "t": 142, "s": [12.358]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.156]}, "t": 143, "s": [15.435]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.156]}, "t": 144, "s": [18.984]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.157]}, "t": 145, "s": [23.04]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 146, "s": [27.636]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.158]}, "t": 147, "s": [32.805]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.158]}, "t": 148, "s": [38.582]}, {"t": 149, "s": [45]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [585.185, 400, 0], "to": [-5.128, 0, 0], "ti": [9.654, 0, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 9, "s": [554.418, 400, 0], "to": [-9.654, 0, 0], "ti": [8.487, 0, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.179}, "t": 10, "s": [527.263, 400, 0], "to": [-8.487, 0, 0], "ti": [7.396, 0, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 11, "s": [503.494, 400, 0], "to": [-7.396, 0, 0], "ti": [6.38, 0, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 12, "s": [482.886, 400, 0], "to": [-6.38, 0, 0], "ti": [5.439, 0, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 13, "s": [465.213, 400, 0], "to": [-5.439, 0, 0], "ti": [4.574, 0, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.183}, "t": 14, "s": [450.249, 400, 0], "to": [-4.574, 0, 0], "ti": [3.783, 0, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 15, "s": [437.77, 400, 0], "to": [-3.783, 0, 0], "ti": [3.068, 0, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.187}, "t": 16, "s": [427.55, 400, 0], "to": [-3.068, 0, 0], "ti": [2.428, 0, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 17, "s": [419.362, 400, 0], "to": [-2.428, 0, 0], "ti": [1.863, 0, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.194}, "t": 18, "s": [412.983, 400, 0], "to": [-1.863, 0, 0], "ti": [1.373, 0, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.2}, "t": 19, "s": [408.185, 400, 0], "to": [-1.373, 0, 0], "ti": [0.958, 0, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.207}, "t": 20, "s": [404.745, 400, 0], "to": [-0.958, 0, 0], "ti": [0.619, 0, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.22}, "t": 21, "s": [402.435, 400, 0], "to": [-0.619, 0, 0], "ti": [0.355, 0, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.245}, "t": 22, "s": [401.032, 400, 0], "to": [-0.355, 0, 0], "ti": [0.165, 0, 0]}, {"i": {"x": 0.833, "y": 0.904}, "o": {"x": 0.167, "y": 0.308}, "t": 23, "s": [400.308, 400, 0], "to": [-0.165, 0, 0], "ti": [0.051, 0, 0]}, {"i": {"x": 0.833, "y": 0.904}, "o": {"x": 0.167, "y": 0.65}, "t": 24, "s": [400.039, 400, 0], "to": [-0.051, 0, 0], "ti": [0.007, 0, 0]}, {"i": {"x": 0.833, "y": 0.922}, "o": {"x": 0.167, "y": 0.607}, "t": 25, "s": [400, 400, 0], "to": [-0.007, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.338}, "o": {"x": 0.167, "y": 0.083}, "t": 129, "s": [400, 400, 0], "to": [0.002, 0, 0], "ti": [-0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.69}, "o": {"x": 0.167, "y": 0.095}, "t": 130, "s": [400.01, 400, 0], "to": [0.014, 0, 0], "ti": [-0.045, 0, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.114}, "t": 131, "s": [400.083, 400, 0], "to": [0.045, 0, 0], "ti": [-0.097, 0, 0]}, {"i": {"x": 0.833, "y": 0.779}, "o": {"x": 0.167, "y": 0.126}, "t": 132, "s": [400.28, 400, 0], "to": [0.097, 0, 0], "ti": [-0.169, 0, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.134}, "t": 133, "s": [400.664, 400, 0], "to": [0.169, 0, 0], "ti": [-0.263, 0, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.139}, "t": 134, "s": [401.296, 400, 0], "to": [0.263, 0, 0], "ti": [-0.377, 0, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.143}, "t": 135, "s": [402.24, 400, 0], "to": [0.377, 0, 0], "ti": [-0.512, 0, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 136, "s": [403.557, 400, 0], "to": [0.512, 0, 0], "ti": [-0.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.148}, "t": 137, "s": [405.31, 400, 0], "to": [0.667, 0, 0], "ti": [-0.843, 0, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.15}, "t": 138, "s": [407.56, 400, 0], "to": [0.843, 0, 0], "ti": [-1.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 139, "s": [410.37, 400, 0], "to": [1.04, 0, 0], "ti": [-1.258, 0, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 140, "s": [413.803, 400, 0], "to": [1.258, 0, 0], "ti": [-1.497, 0, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 141, "s": [417.92, 400, 0], "to": [1.497, 0, 0], "ti": [-1.756, 0, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.155}, "t": 142, "s": [422.784, 400, 0], "to": [1.756, 0, 0], "ti": [-2.036, 0, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.156}, "t": 143, "s": [428.456, 400, 0], "to": [2.036, 0, 0], "ti": [-2.337, 0, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.156}, "t": 144, "s": [435, 400, 0], "to": [2.337, 0, 0], "ti": [-2.658, 0, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.157}, "t": 145, "s": [442.477, 400, 0], "to": [2.658, 0, 0], "ti": [-3, 0, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 146, "s": [450.95, 400, 0], "to": [3, 0, 0], "ti": [-3.363, 0, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 147, "s": [460.48, 400, 0], "to": [3.363, 0, 0], "ti": [-3.747, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.158}, "t": 148, "s": [471.13, 400, 0], "to": [3.747, 0, 0], "ti": [-1.972, 0, 0]}, {"t": 149, "s": [482.963, 400, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [144.5, 124.5, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 8, "s": [100, 100, 100]}, {"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 25, "s": [114, 114, 100]}, {"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 129, "s": [114, 114, 100]}, {"t": 149, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "AC IN [3G8] Controls", "np": 4, "mn": "Pseudo/MHAC PrCtrl 3G8 4", "ix": 1, "en": 1, "ef": [{"ty": 3, "nm": "Position", "mn": "Pseudo/MHAC PrCtrl 3G8 4-0001", "ix": 1, "v": {"a": 0, "k": [185.185, 0], "ix": 1}}, {"ty": 0, "nm": "<PERSON><PERSON>", "mn": "Pseudo/MHAC PrCtrl 3G8 4-0002", "ix": 2, "v": {"a": 0, "k": 45, "ix": 2}}]}, {"ty": 5, "nm": "AC OUT [3G8] 2 Controls", "np": 4, "mn": "Pseudo/MHAC PrCtrl 3G8 4", "ix": 2, "en": 1, "ef": [{"ty": 3, "nm": "Position", "mn": "Pseudo/MHAC PrCtrl 3G8 4-0001", "ix": 1, "v": {"a": 0, "k": [82.963, 0], "ix": 1}}, {"ty": 0, "nm": "<PERSON><PERSON>", "mn": "Pseudo/MHAC PrCtrl 3G8 4-0002", "ix": 2, "v": {"a": 0, "k": 45, "ix": 2}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.25, 23.101], [9.432, 16.248], [6.902, 19.057], [5.105, 10.405], [5.599, -61.853], [1.39, -4.025], [5.055, -1.609], [8.476, -1.504], [6.94, -3.54], [8.341, -12.997], [-18.936, -25.215], [-17.282, -7.041], [-35.691, -0.199], [-24.647, 14.974]], "o": [[3.356, -18.25], [-9.784, -16.857], [-3.946, -10.897], [-24.118, -49.164], [-0.384, 4.24], [-1.732, 5.014], [-8.151, 2.594], [-7.673, 1.361], [-13.777, 7.026], [-16.661, 25.96], [11.206, 14.921], [33.054, 13.465], [28.839, 0.161], [21.79, -13.239]], "v": [[142.752, 47.604], [134.148, -8.129], [102.489, -56.324], [91.907, -89.395], [-36.74, -75.406], [-38.357, -62.776], [-49.341, -52.057], [-75.311, -50.23], [-97.341, -42.797], [-131.225, -11.857], [-130.402, 72.798], [-84.869, 104.163], [19.651, 124.225], [102.922, 105.248]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.738146841526, 0.771196961403, 0.913725495338, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [144.164, 124.229], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 8, "op": 150, "st": 8, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Midium/BG-01 Outlines", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [47]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137, "s": [47]}, {"t": 145, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [-35]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.178]}, "t": 5, "s": [-29.185]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.179]}, "t": 6, "s": [-24.053]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.179]}, "t": 7, "s": [-19.56]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 8, "s": [-15.665]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 9, "s": [-12.325]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 10, "s": [-9.497]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.185]}, "t": 11, "s": [-7.139]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.187]}, "t": 12, "s": [-5.207]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.19]}, "t": 13, "s": [-3.659]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.194]}, "t": 14, "s": [-2.454]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.2]}, "t": 15, "s": [-1.547]}, {"i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.207]}, "t": 16, "s": [-0.897]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.22]}, "t": 17, "s": [-0.46]}, {"i": {"x": [0.833], "y": [0.886]}, "o": {"x": [0.167], "y": [0.245]}, "t": 18, "s": [-0.195]}, {"i": {"x": [0.833], "y": [0.904]}, "o": {"x": [0.167], "y": [0.308]}, "t": 19, "s": [-0.058]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.65]}, "t": 20, "s": [-0.007]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.001]}, "t": 21, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 83, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 85, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 86, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 91, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 92, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 93, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 94, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 111, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 112, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 113, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 114, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 115, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 116, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 117, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 121, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 122, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 123, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 124, "s": [0]}, {"i": {"x": [0.833], "y": [0.333]}, "o": {"x": [0.167], "y": [0.083]}, "t": 125, "s": [0]}, {"i": {"x": [0.833], "y": [0.69]}, "o": {"x": [0.167], "y": [0.095]}, "t": 126, "s": [-0.004]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.114]}, "t": 127, "s": [-0.035]}, {"i": {"x": [0.833], "y": [0.779]}, "o": {"x": [0.167], "y": [0.126]}, "t": 128, "s": [-0.118]}, {"i": {"x": [0.833], "y": [0.792]}, "o": {"x": [0.167], "y": [0.134]}, "t": 129, "s": [-0.28]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.139]}, "t": 130, "s": [-0.547]}, {"i": {"x": [0.833], "y": [0.806]}, "o": {"x": [0.167], "y": [0.143]}, "t": 131, "s": [-0.945]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.146]}, "t": 132, "s": [-1.501]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.148]}, "t": 133, "s": [-2.24]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.15]}, "t": 134, "s": [-3.189]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 135, "s": [-4.375]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 136, "s": [-5.823]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 137, "s": [-7.56]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.155]}, "t": 138, "s": [-9.612]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.156]}, "t": 139, "s": [-12.005]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.156]}, "t": 140, "s": [-14.766]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.157]}, "t": 141, "s": [-17.92]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 142, "s": [-21.494]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.158]}, "t": 143, "s": [-25.515]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.158]}, "t": 144, "s": [-30.008]}, {"t": 145, "s": [-35]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [289.63, 476.296, 0], "to": [3.056, -2.113, 0], "ti": [-5.754, 3.977, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 5, "s": [307.967, 463.62, 0], "to": [5.754, -3.977, 0], "ti": [-5.058, 3.497, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.179}, "t": 6, "s": [324.151, 452.432, 0], "to": [5.058, -3.497, 0], "ti": [-4.408, 3.047, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 7, "s": [338.317, 442.64, 0], "to": [4.408, -3.047, 0], "ti": [-3.803, 2.629, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 8, "s": [350.6, 434.149, 0], "to": [3.803, -2.629, 0], "ti": [-3.242, 2.241, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 9, "s": [361.133, 426.868, 0], "to": [3.242, -2.241, 0], "ti": [-2.726, 1.884, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.183}, "t": 10, "s": [370.051, 420.703, 0], "to": [2.726, -1.884, 0], "ti": [-2.255, 1.559, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 11, "s": [377.489, 415.561, 0], "to": [2.255, -1.559, 0], "ti": [-1.829, 1.264, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.187}, "t": 12, "s": [383.58, 411.35, 0], "to": [1.829, -1.264, 0], "ti": [-1.447, 1, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 13, "s": [388.46, 407.977, 0], "to": [1.447, -1, 0], "ti": [-1.11, 0.767, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.194}, "t": 14, "s": [392.262, 405.349, 0], "to": [1.11, -0.767, 0], "ti": [-0.818, 0.566, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.2}, "t": 15, "s": [395.121, 403.372, 0], "to": [0.818, -0.566, 0], "ti": [-0.571, 0.395, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.207}, "t": 16, "s": [397.172, 401.955, 0], "to": [0.571, -0.395, 0], "ti": [-0.369, 0.255, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.22}, "t": 17, "s": [398.549, 401.003, 0], "to": [0.369, -0.255, 0], "ti": [-0.211, 0.146, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.245}, "t": 18, "s": [399.385, 400.425, 0], "to": [0.211, -0.146, 0], "ti": [-0.099, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.904}, "o": {"x": 0.167, "y": 0.308}, "t": 19, "s": [399.816, 400.127, 0], "to": [0.099, -0.068, 0], "ti": [-0.031, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.905}, "o": {"x": 0.167, "y": 0.649}, "t": 20, "s": [399.976, 400.016, 0], "to": [0.031, -0.021, 0], "ti": [-0.004, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.921}, "o": {"x": 0.167, "y": 0.616}, "t": 21, "s": [400, 400, 0], "to": [0.004, -0.003, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.337}, "o": {"x": 0.167, "y": 0.083}, "t": 125, "s": [400, 400, 0], "to": [-0.001, 0.001, 0], "ti": [0.006, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.69}, "o": {"x": 0.167, "y": 0.095}, "t": 126, "s": [399.995, 400.004, 0], "to": [-0.006, 0.005, 0], "ti": [0.02, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.114}, "t": 127, "s": [399.964, 400.029, 0], "to": [-0.02, 0.016, 0], "ti": [0.042, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.779}, "o": {"x": 0.167, "y": 0.126}, "t": 128, "s": [399.877, 400.098, 0], "to": [-0.042, 0.034, 0], "ti": [0.074, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.134}, "t": 129, "s": [399.71, 400.231, 0], "to": [-0.074, 0.059, 0], "ti": [0.115, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.139}, "t": 130, "s": [399.433, 400.451, 0], "to": [-0.115, 0.091, 0], "ti": [0.165, -0.131, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.143}, "t": 131, "s": [399.02, 400.78, 0], "to": [-0.165, 0.131, 0], "ti": [0.224, -0.178, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 132, "s": [398.444, 401.239, 0], "to": [-0.224, 0.178, 0], "ti": [0.292, -0.232, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.148}, "t": 133, "s": [397.677, 401.849, 0], "to": [-0.292, 0.232, 0], "ti": [0.369, -0.294, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.15}, "t": 134, "s": [396.692, 402.632, 0], "to": [-0.369, 0.294, 0], "ti": [0.455, -0.362, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 135, "s": [395.463, 403.611, 0], "to": [-0.455, 0.362, 0], "ti": [0.55, -0.438, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 136, "s": [393.961, 404.806, 0], "to": [-0.55, 0.438, 0], "ti": [0.655, -0.521, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 137, "s": [392.16, 406.24, 0], "to": [-0.655, 0.521, 0], "ti": [0.768, -0.611, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.155}, "t": 138, "s": [390.032, 407.934, 0], "to": [-0.768, 0.611, 0], "ti": [0.891, -0.709, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.156}, "t": 139, "s": [387.55, 409.909, 0], "to": [-0.891, 0.709, 0], "ti": [1.022, -0.814, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.156}, "t": 140, "s": [384.687, 412.188, 0], "to": [-1.022, 0.814, 0], "ti": [1.163, -0.926, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.157}, "t": 141, "s": [381.416, 414.791, 0], "to": [-1.163, 0.926, 0], "ti": [1.313, -1.045, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 142, "s": [377.71, 417.741, 0], "to": [-1.313, 1.045, 0], "ti": [1.472, -1.171, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 143, "s": [373.54, 421.06, 0], "to": [-1.472, 1.171, 0], "ti": [1.639, -1.305, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.158}, "t": 144, "s": [368.88, 424.769, 0], "to": [-1.639, 1.305, 0], "ti": [0.863, -0.687, 0]}, {"t": 145, "s": [363.704, 428.889, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [219.5, 190.5, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 4, "s": [90, 90, 100]}, {"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 21, "s": [114, 114, 100]}, {"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 125, "s": [114, 114, 100]}, {"t": 145, "s": [90, 90, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "AC IN [I2Z] Controls", "np": 4, "mn": "Pseudo/MHAC PrCtrl I2Z 4", "ix": 1, "en": 1, "ef": [{"ty": 3, "nm": "Position", "mn": "Pseudo/MHAC PrCtrl I2Z 4-0001", "ix": 1, "v": {"a": 0, "k": [-110.37, 76.296], "ix": 1}}, {"ty": 0, "nm": "<PERSON><PERSON>", "mn": "Pseudo/MHAC PrCtrl I2Z 4-0002", "ix": 2, "v": {"a": 0, "k": -35, "ix": 2}}]}, {"ty": 5, "nm": "AC OUT [I2Z] 2 Controls", "np": 4, "mn": "Pseudo/MHAC PrCtrl I2Z 4", "ix": 2, "en": 1, "ef": [{"ty": 3, "nm": "Position", "mn": "Pseudo/MHAC PrCtrl I2Z 4-0001", "ix": 1, "v": {"a": 0, "k": [-36.296, 28.889], "ix": 1}}, {"ty": 0, "nm": "<PERSON><PERSON>", "mn": "Pseudo/MHAC PrCtrl I2Z 4-0002", "ix": 2, "v": {"a": 0, "k": -35, "ix": 2}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.226, 35.663], [16.587, 23.349], [13.137, 27.967], [9.202, 15.083], [-0.136, -94.679], [1.548, -6.304], [7.45, -3.149], [12.657, -3.467], [10.041, -6.343], [10.848, -20.894], [-32.267, -35.634], [-27.218, -8.275], [-54.209, 4.681], [-35.324, 26.174]], "o": [[2.548, -28.172], [-17.207, -24.222], [-7.512, -15.991], [-43.478, -71.266], [0.009, 6.49], [-1.929, 7.854], [-12.011, 5.075], [-11.458, 3.137], [-19.932, 12.59], [-21.668, 41.736], [19.095, 21.087], [52.059, 15.827], [43.802, -3.782], [31.23, -23.138]], "v": [[218.903, 54.989], [198.063, -28.416], [143.272, -97.157], [122.592, -145.883], [-70.749, -106.684], [-71.439, -87.286], [-86.618, -69.48], [-125.787, -63.081], [-158.193, -48.721], [-205.31, 2.978], [-192.241, 131.375], [-118.74, 172.63], [42.728, 188.493], [166.49, 148.056]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.738146841526, 0.771196961403, 0.913725495338, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [219.423, 190.216], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 4, "op": 146, "st": 4, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Big/BG-01 Outlines", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [18]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132, "s": [18]}, {"t": 140, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [-90]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.178]}, "t": 1, "s": [-75.047]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.179]}, "t": 2, "s": [-61.85]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.179]}, "t": 3, "s": [-50.298]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 4, "s": [-40.283]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 5, "s": [-31.693]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 6, "s": [-24.421]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.185]}, "t": 7, "s": [-18.356]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.187]}, "t": 8, "s": [-13.389]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.19]}, "t": 9, "s": [-9.41]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.194]}, "t": 10, "s": [-6.31]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.2]}, "t": 11, "s": [-3.978]}, {"i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.207]}, "t": 12, "s": [-2.306]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.22]}, "t": 13, "s": [-1.184]}, {"i": {"x": [0.833], "y": [0.886]}, "o": {"x": [0.167], "y": [0.245]}, "t": 14, "s": [-0.501]}, {"i": {"x": [0.833], "y": [0.904]}, "o": {"x": [0.167], "y": [0.308]}, "t": 15, "s": [-0.15]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.65]}, "t": 16, "s": [-0.019]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.002]}, "t": 17, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 21, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 83, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 85, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 86, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 91, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 92, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 93, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 94, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 111, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 112, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 113, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 114, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 115, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 116, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 117, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [1.001]}, "o": {"x": [0.167], "y": [0]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.333]}, "o": {"x": [0.167], "y": [0.083]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [0.69]}, "o": {"x": [0.167], "y": [0.095]}, "t": 121, "s": [-0.011]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.114]}, "t": 122, "s": [-0.09]}, {"i": {"x": [0.833], "y": [0.779]}, "o": {"x": [0.167], "y": [0.126]}, "t": 123, "s": [-0.304]}, {"i": {"x": [0.833], "y": [0.792]}, "o": {"x": [0.167], "y": [0.134]}, "t": 124, "s": [-0.72]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.139]}, "t": 125, "s": [-1.406]}, {"i": {"x": [0.833], "y": [0.806]}, "o": {"x": [0.167], "y": [0.143]}, "t": 126, "s": [-2.43]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.146]}, "t": 127, "s": [-3.859]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.148]}, "t": 128, "s": [-5.76]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.15]}, "t": 129, "s": [-8.201]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 130, "s": [-11.25]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 131, "s": [-14.974]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 132, "s": [-19.44]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.155]}, "t": 133, "s": [-24.716]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.156]}, "t": 134, "s": [-30.87]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.156]}, "t": 135, "s": [-37.969]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.157]}, "t": 136, "s": [-46.08]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 137, "s": [-55.271]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.158]}, "t": 138, "s": [-65.61]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.158]}, "t": 139, "s": [-77.164]}, {"t": 140, "s": [-90]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [400, 585.185, 0], "to": [0, -5.128, 0], "ti": [0, 9.654, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 1, "s": [400, 554.418, 0], "to": [0, -9.654, 0], "ti": [0, 8.487, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.179}, "t": 2, "s": [400, 527.263, 0], "to": [0, -8.487, 0], "ti": [0, 7.396, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 3, "s": [400, 503.494, 0], "to": [0, -7.396, 0], "ti": [0, 6.38, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 4, "s": [400, 482.886, 0], "to": [0, -6.38, 0], "ti": [0, 5.439, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 5, "s": [400, 465.213, 0], "to": [0, -5.439, 0], "ti": [0, 4.574, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.183}, "t": 6, "s": [400, 450.249, 0], "to": [0, -4.574, 0], "ti": [0, 3.783, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 7, "s": [400, 437.77, 0], "to": [0, -3.783, 0], "ti": [0, 3.068, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.187}, "t": 8, "s": [400, 427.55, 0], "to": [0, -3.068, 0], "ti": [0, 2.428, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 9, "s": [400, 419.362, 0], "to": [0, -2.428, 0], "ti": [0, 1.863, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.194}, "t": 10, "s": [400, 412.983, 0], "to": [0, -1.863, 0], "ti": [0, 1.373, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.2}, "t": 11, "s": [400, 408.185, 0], "to": [0, -1.373, 0], "ti": [0, 0.958, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.207}, "t": 12, "s": [400, 404.745, 0], "to": [0, -0.958, 0], "ti": [0, 0.619, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.22}, "t": 13, "s": [400, 402.435, 0], "to": [0, -0.619, 0], "ti": [0, 0.355, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.245}, "t": 14, "s": [400, 401.032, 0], "to": [0, -0.355, 0], "ti": [0, 0.165, 0]}, {"i": {"x": 0.833, "y": 0.904}, "o": {"x": 0.167, "y": 0.308}, "t": 15, "s": [400, 400.308, 0], "to": [0, -0.165, 0], "ti": [0, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.904}, "o": {"x": 0.167, "y": 0.65}, "t": 16, "s": [400, 400.039, 0], "to": [0, -0.051, 0], "ti": [0, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.922}, "o": {"x": 0.167, "y": 0.607}, "t": 17, "s": [400, 400, 0], "to": [0, -0.007, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.337}, "o": {"x": 0.167, "y": 0.083}, "t": 120, "s": [400, 400, 0], "to": [0, 0.004, 0], "ti": [0, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.69}, "o": {"x": 0.167, "y": 0.095}, "t": 121, "s": [400, 400.023, 0], "to": [0, 0.031, 0], "ti": [0, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.114}, "t": 122, "s": [400, 400.185, 0], "to": [0, 0.1, 0], "ti": [0, -0.216, 0]}, {"i": {"x": 0.833, "y": 0.779}, "o": {"x": 0.167, "y": 0.126}, "t": 123, "s": [400, 400.625, 0], "to": [0, 0.216, 0], "ti": [0, -0.378, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.134}, "t": 124, "s": [400, 401.481, 0], "to": [0, 0.378, 0], "ti": [0, -0.586, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.139}, "t": 125, "s": [400, 402.894, 0], "to": [0, 0.586, 0], "ti": [0, -0.841, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.143}, "t": 126, "s": [400, 405, 0], "to": [0, 0.841, 0], "ti": [0, -1.142, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 127, "s": [400, 407.94, 0], "to": [0, 1.142, 0], "ti": [0, -1.489, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.148}, "t": 128, "s": [400, 411.852, 0], "to": [0, 1.489, 0], "ti": [0, -1.883, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.15}, "t": 129, "s": [400, 416.875, 0], "to": [0, 1.883, 0], "ti": [0, -2.323, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 130, "s": [400, 423.148, 0], "to": [0, 2.323, 0], "ti": [0, -2.809, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 131, "s": [400, 430.81, 0], "to": [0, 2.809, 0], "ti": [0, -3.341, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 132, "s": [400, 440, 0], "to": [0, 3.341, 0], "ti": [0, -3.92, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.155}, "t": 133, "s": [400, 450.856, 0], "to": [0, 3.92, 0], "ti": [0, -4.545, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.156}, "t": 134, "s": [400, 463.519, 0], "to": [0, 4.545, 0], "ti": [0, -5.216, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.156}, "t": 135, "s": [400, 478.125, 0], "to": [0, 5.216, 0], "ti": [0, -5.934, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.157}, "t": 136, "s": [400, 494.815, 0], "to": [0, 5.934, 0], "ti": [0, -6.698, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 137, "s": [400, 513.727, 0], "to": [0, 6.698, 0], "ti": [0, -7.508, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 138, "s": [400, 535, 0], "to": [0, 7.508, 0], "ti": [0, -8.364, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.158}, "t": 139, "s": [400, 558.773, 0], "to": [0, 8.364, 0], "ti": [0, -4.402, 0]}, {"t": 140, "s": [400, 585.185, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [236.5, 214, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.843, 0.843, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.844, 0.844, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.178, 0.178, 0]}, "t": 1, "s": [18.94, 18.94, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.844, 0.844, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.179, 0.179, 0]}, "t": 2, "s": [35.657, 35.657, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.179, 0.179, 0]}, "t": 3, "s": [50.289, 50.289, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.846, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.181, 0.181, 0]}, "t": 4, "s": [62.975, 62.975, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.847, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.182, 0.182, 0]}, "t": 5, "s": [73.855, 73.855, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.848, 0.848, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.183, 0.183, 0]}, "t": 6, "s": [83.066, 83.066, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.85, 0.85, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.185, 0.185, 0]}, "t": 7, "s": [90.749, 90.749, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.852, 0.852, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.187, 0.187, 0]}, "t": 8, "s": [97.04, 97.04, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.854, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.19, 0.19, 0]}, "t": 9, "s": [102.081, 102.081, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.857, 0.857, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.194, 0.194, 0]}, "t": 10, "s": [106.008, 106.008, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.861, 0.861, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.2, 0.2, 0]}, "t": 11, "s": [108.961, 108.961, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.866, 0.866, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.207, 0.207, 0]}, "t": 12, "s": [111.079, 111.079, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.874, 0.874, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.22, 0.22, 0]}, "t": 13, "s": [112.501, 112.501, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.886, 0.886, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.245, 0.245, 0]}, "t": 14, "s": [113.365, 113.365, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.904, 0.904, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.308, 0.308, 0]}, "t": 15, "s": [113.81, 113.81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.65, 0.65, 0]}, "t": 16, "s": [113.976, 113.976, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [19134.81, 19134.813, 0]}, "t": 17, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 18, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 19, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 20, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 21, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 22, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 25, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 26, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 27, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 28, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 29, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 30, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 31, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 32, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 33, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 34, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 35, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 36, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 37, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 38, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 39, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 40, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 41, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 42, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 43, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 44, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 45, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 46, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 47, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 48, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 49, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 50, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 52, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 53, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 54, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 55, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 56, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 57, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 58, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 59, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 60, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 61, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 63, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 64, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 65, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 66, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 67, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 68, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 69, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 70, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 71, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 72, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 73, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 74, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 75, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 76, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 77, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 78, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 79, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 80, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 81, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 82, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 83, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 84, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 85, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 86, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 87, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 88, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 89, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 90, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 91, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 92, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 93, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 94, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 95, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 96, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 97, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 98, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 99, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 100, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 101, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 102, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 103, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 104, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 105, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 106, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 107, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 108, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 109, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 110, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 111, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 112, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 113, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 114, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 115, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 116, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 117, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 118, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.001, 1.001, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 119, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.333, 0.333, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 120, "s": [114, 114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.69, 0.69, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.095, 0.095, 0]}, "t": 121, "s": [113.986, 113.986, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.754, 0.754, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.114, 0.114, 0]}, "t": 122, "s": [113.886, 113.886, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.779, 0.779, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.126, 0.126, 0]}, "t": 123, "s": [113.615, 113.615, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.792, 0.792, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.134, 0.134, 0]}, "t": 124, "s": [113.088, 113.088, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.8, 0.8, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.139, 0.139, 0]}, "t": 125, "s": [112.219, 112.219, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.806, 0.806, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.143, 0.143, 0]}, "t": 126, "s": [110.922, 110.922, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.81, 0.81, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.146, 0.146, 0]}, "t": 127, "s": [109.112, 109.112, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.813, 0.813, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.148, 0.148, 0]}, "t": 128, "s": [106.704, 106.704, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.815, 0.815, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.15, 0.15, 0]}, "t": 129, "s": [103.612, 103.612, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.817, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.152, 0.152, 0]}, "t": 130, "s": [99.75, 99.75, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.818, 0.818, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.153, 0.153, 0]}, "t": 131, "s": [95.033, 95.033, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.819, 0.819, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.154, 0.154, 0]}, "t": 132, "s": [89.376, 89.376, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.821, 0.821, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.155, 0.155, 0]}, "t": 133, "s": [82.693, 82.693, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.821, 0.821, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.156, 0.156, 0]}, "t": 134, "s": [74.898, 74.898, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.156, 0.156, 0]}, "t": 135, "s": [65.906, 65.906, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.823, 0.823, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 136, "s": [55.632, 55.632, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.824, 0.824, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 137, "s": [43.99, 43.99, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.824, 0.824, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.158, 0.158, 0]}, "t": 138, "s": [30.894, 30.894, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.158, 0.158, 0]}, "t": 139, "s": [16.259, 16.259, 100]}, {"t": 140, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "AC IN [A6J] Controls", "np": 5, "mn": "Pseudo/MHAC PrCtrl A6J 4", "ix": 1, "en": 1, "ef": [{"ty": 3, "nm": "Position", "mn": "Pseudo/MHAC PrCtrl A6J 4-0001", "ix": 1, "v": {"a": 0, "k": [0, 185.185], "ix": 1}}, {"ty": 0, "nm": "<PERSON><PERSON>", "mn": "Pseudo/MHAC PrCtrl A6J 4-0002", "ix": 2, "v": {"a": 0, "k": -90, "ix": 2}}, {"ty": 0, "nm": "Scale", "mn": "Pseudo/MHAC PrCtrl A6J 4-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}, {"ty": 5, "nm": "AC OUT [A6J] 2 Controls", "np": 5, "mn": "Pseudo/MHAC PrCtrl A6J 4", "ix": 2, "en": 1, "ef": [{"ty": 3, "nm": "Position", "mn": "Pseudo/MHAC PrCtrl A6J 4-0001", "ix": 1, "v": {"a": 0, "k": [0, 185.185], "ix": 1}}, {"ty": 0, "nm": "<PERSON><PERSON>", "mn": "Pseudo/MHAC PrCtrl A6J 4-0002", "ix": 2, "v": {"a": 0, "k": -90, "ix": 2}}, {"ty": 0, "nm": "Scale", "mn": "Pseudo/MHAC PrCtrl A6J 4-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-25.937, 31.1], [0.411, 32.386], [-5.767, 34.465], [-1.141, 19.949], [60.768, -88.161], [5.494, -4.868], [8.956, 1.863], [14.004, 4.917], [13.42, 0.557], [23.529, -12.46], [-7.097, -53.901], [-19.997, -25.205], [-53.438, -30.512], [-49.695, 1.628]], "o": [[20.489, -24.568], [-0.428, -33.601], [3.297, -19.707], [5.392, -94.258], [-4.166, 6.044], [-6.846, 6.065], [-14.437, -3.003], [-12.677, -4.45], [-26.64, -1.108], [-47, 24.888], [4.2, 31.898], [38.247, 48.205], [43.178, 24.653], [43.933, -1.438]], "v": [[196.434, 160.721], [230.692, 69.731], [223.935, -29.455], [236.037, -88.083], [30.972, -175.971], [17.854, -158.37], [-7.718, -151.569], [-48.271, -170.809], [-87.651, -178.293], [-164.733, -160.505], [-235.157, -32.66], [-193.318, 52.992], [-53.316, 171.599], [87.819, 213.584]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.738146841526, 0.771196961403, 0.913725495338, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [236.18, 213.682], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 141, "st": 0, "bm": 0}], "markers": []}