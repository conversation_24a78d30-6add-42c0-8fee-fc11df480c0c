@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes flash {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  15% {
    opacity: 0.9;
    transform: scale(1.1);
  }
  30% {
    opacity: 0.3;
    transform: scale(1);
  }
  45% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  60% {
    opacity: 0.2;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1);
  }
}

.flash-effect {
  opacity: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.8) 30%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  mix-blend-mode: screen;
  backdrop-filter: blur(2px);
  transition: all 0.3s ease;
}

.flash-effect.active {
  animation: flash 700ms cubic-bezier(0.4, 0, 0.2, 1);
}

.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {  
  display: none;  /* Chrome, Safari and Opera */
}

@font-face {
  font-family: 'SVN Gilroy';
  src: url('./fonts/SVN-<PERSON>\ Regular.otf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'SVN Gilroy';
  src: url('./fonts/SVN-Gilroy\ Medium.otf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'SVN Gilroy';
  src: url('./fonts/SVN-Gilroy\ SemiBold.otf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'SVN Gilroy';
  src: url('./fonts/SVN-Gilroy\ Bold.otf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

body {
  font-family: 'SVN Gilroy', sans-serif;
}