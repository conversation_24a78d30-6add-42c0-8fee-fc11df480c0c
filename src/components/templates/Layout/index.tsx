import SLogo from "@/components/atoms/SLogo";
import SText from "@/components/atoms/SText";
import { cn } from "@/cores/helpers";
import { useTheme } from "@/hooks/useTheme";
import React from "react";

type LayoutProps = {
  children: React.ReactNode;
  title?: string;
  description?: string;
  showLogo?: boolean;
  className?: string;
};

const Layout = ({
  children,
  title,
  description,
  showLogo = true,
  className,
}: LayoutProps) => {
  const theme = useTheme();

  return (
    <div
      className={cn(
        "w-screen h-screen flex flex-col justify-between items-center pb-10 pt-[72px]",
        className
      )}
      style={{
        backgroundColor: theme.background_color,
      }}
    >
      <div className="flex flex-col justify-center items-center gap-8">
        {showLogo && (
          <div className="w-[250px] h-[110px]">
            <SLogo />
          </div>
        )}
        <div className="flex flex-col items-center">
          {title && (
            <SText type="main" className="font-semibold text-[70px]">
              {title}
            </SText>
          )}
          {description && (
            <SText type="main" className="text-[30px] text-center">
              {description}
            </SText>
          )}
        </div>
      </div>
      <div className="w-full h-full z-50 flex flex-col">{children}</div>
    </div>
  );
};

export default React.memo(Layout);
