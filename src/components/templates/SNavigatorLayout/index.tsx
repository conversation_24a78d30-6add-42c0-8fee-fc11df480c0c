import SIconButton from "@/components/molecules/SIconButton";
import Layout from "../Layout";
import { cn } from "@/cores/helpers";

type SNavigatorLayoutProps = {
  title: string;
  description: string;
  children?: React.ReactNode;
  onGoBack: () => void;
  onSubmit: () => void;
  disabled?: boolean;
  className?: string;
};

const SNavigatorLayout = ({
  title,
  description,
  children,
  onGoBack,
  onSubmit,
  disabled,
  className,
}: SNavigatorLayoutProps) => {
  return (
    <Layout showLogo={false} title={title} description={description}>
      <div className="flex flex-col h-full">{children}</div>

      <div
        className={cn([
          "flex flex-row justify-between w-full px-[100px]",
          className,
        ])}
      >
        <SIconButton
          LeftIcon={"SArrowLeft"}
          className="w-[250px] h-[65px] bg-primary"
          left={{
            color: "primary_text_color",
            size: 27,
          }}
          text="Back"
          onClick={onGoBack}
        />

        <SIconButton
          RightIcon={"SArrowRight"}
          text="Next"
          className="w-[250px] h-[65px] bg-primary"
          right={{
            color: "primary_text_color",
            size: 27,
          }}
          onClick={onSubmit}
          disabled={disabled}
        />
      </div>
    </Layout>
  );
};

export default SNavigatorLayout;
