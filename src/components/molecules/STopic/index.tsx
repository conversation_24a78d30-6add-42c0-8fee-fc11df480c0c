import { cn } from "@/cores/helpers";
import STopicThumb from "../STopicThumb";

type TSTopic = {
  items: any[];
  height?: string;
  className?: string;
  onClick: (item: any) => void;
};

const STopic = ({ items, height, className, onClick }: TSTopic) => (
  <div className={cn("flex gap-[48px]", height, className)}>
    {items.map((item) => (
      <STopicThumb key={item.id} item={item} onClick={onClick} />
    ))}
  </div>
);

export default STopic;
