import React from "react";
import { cn } from "@/cores/helpers";
import { CarouselItem } from "./types";
import { SButton, SImage } from "@/components/atoms";
import SIcon from "@/components/atoms/SIcon";

interface SCarouselViewProps {
  visibleItems: CarouselItem[];
  currentIndex: number;
  items: CarouselItem[];
  maxVisibleItems: number;
  className?: string;
  itemClassName?: string;
  onPrev: () => void;
  onNext: () => void;
}

export const SCarouselView: React.FC<SCarouselViewProps> = ({
  visibleItems,
  currentIndex,
  items,
  maxVisibleItems,
  className,
  itemClassName,
  onPrev,
  onNext,
}) => {
  return (
    <div
      className={cn(
        "relative flex flex-1 items-center justify-center w-full py-5",
        className
      )}
    >
      <SButton
        className="bg-primary_color w-[120px] h-[120px] rounded-full items-center justify-center flex"
        onClick={onPrev}
        noDefaultStyle
      >
        <SIcon
          name="ChevronLeftIcon"
          weight="bold"
          color="primary_text_color"
          size={100}
        />
      </SButton>

      <div className="flex flex-1 flex-col justify-center items-center">
        <div className="flex items-center justify-center gap-3 flex-1">
          {visibleItems.map((item: any, index: number) => {
            const isCenter = index === Math.floor(maxVisibleItems / 2);

            return (
              <div
                key={`${item.id}-${index}-${new Date().getTime()}`}
                className={cn(
                  "transition-all duration-300 select-none flex flex-1 flex-col items-center justify-center",
                  isCenter
                    ? [
                        "w-[530px] h-[577px] scale-100 opacity-100 p-10 z-10 border-2 border-primary_color rounded-lg overflow-hidden",
                      ]
                    : ["w-[350px] h-[350px]", "scale-75 opacity-40"],
                  itemClassName
                )}
              >
                <SImage className="flex-1 object-contain" src={item.imageUrl} />

                {isCenter && (
                  <p className="text-[20px] text-center font-400 text-black max-w-2xl mt-4">
                    {items[currentIndex]?.description}
                  </p>
                )}
              </div>
            );
          })}
        </div>
      </div>

      <SButton
        className="bg-primary_color w-[120px] h-[120px] rounded-full items-center justify-center flex"
        onClick={onNext}
        noDefaultStyle
      >
        <SIcon
          name="ChevronRightIcon"
          weight="bold"
          color="primary_text_color"
          size={100}
        />
      </SButton>
    </div>
  );
};
