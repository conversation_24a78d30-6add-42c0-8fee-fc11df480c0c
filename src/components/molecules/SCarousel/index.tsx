import React, { useState, useEffect, useCallback } from "react";
import { SCarouselView } from "./view";
import { CarouselItem, SCarouselProps } from "./types";

const SCarousel: React.FC<SCarouselProps> = ({
  items,
  maxVisibleItems = 5,
  onSelect,
  className,
  itemClassName,
  defaultIndex = 0,
}) => {
  const [currentIndex, setCurrentIndex] = useState(defaultIndex);
  const [visibleItems, setVisibleItems] = useState<CarouselItem[]>([]);

  const updateVisibleItems = useCallback(() => {
    if (items.length === 0) return;

    const halfVisible = Math.floor(maxVisibleItems / 2);
    const newVisibleItems = [];

    for (let i = 0; i < maxVisibleItems; i++) {
      const index =
        (currentIndex - halfVisible + i + items.length) % items.length;
      newVisibleItems.push(items[index]);
    }

    setVisibleItems(newVisibleItems);
  }, [currentIndex, items, maxVisibleItems]);

  useEffect(() => {
    updateVisibleItems();
  }, [updateVisibleItems]);

  const handlePrev = () => {
    const newIndex = (currentIndex - 1 + items.length) % items.length;
    setCurrentIndex(newIndex);
    onSelect?.(items[newIndex], newIndex);
  };

  const handleNext = () => {
    const newIndex = (currentIndex + 1) % items.length;
    setCurrentIndex(newIndex);
    onSelect?.(items[newIndex], newIndex);
  };

  if (items.length === 0) {
    return null;
  }

  return (
    <SCarouselView
      visibleItems={visibleItems}
      currentIndex={currentIndex}
      items={items}
      maxVisibleItems={maxVisibleItems}
      className={className}
      itemClassName={itemClassName}
      onPrev={handlePrev}
      onNext={handleNext}
    />
  );
};

export default SCarousel;
