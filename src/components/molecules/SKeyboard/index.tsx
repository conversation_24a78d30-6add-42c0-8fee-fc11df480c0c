import { SButton } from "@/components/atoms";
import SIcon from "@/components/atoms/SIcon";
import SText from "@/components/atoms/SText";
import { KEYBOARD_NUMBERS } from "@/cores/constants";
import { cn } from "@/cores/helpers";

type SKeyboardProps = {
  disabled?: boolean;
  setVoucher: (voucher: string) => void;
  voucher: string;
  onFinish: () => void;
};

type KeyboardNumberProps = {
  keyboardNumber: string;
  setVoucher: (voucher: string) => void;
  voucher: string;
  onFinish: () => void;
  disabled?: boolean;
};

const KeyboardNumber = ({
  keyboardNumber,
  setVoucher,
  voucher,
  onFinish,
  disabled = false,
}: KeyboardNumberProps) => {
  return (
    <SButton
      noBackground
      effect={keyboardNumber === "C" ? "light" : "dark"}
      borderWidth={2}
      key={keyboardNumber}
      disabled={disabled}
      onClick={() => {
        switch (keyboardNumber) {
          case "R":
            setVoucher(voucher.slice(0, -1));
            break;
          case "C":
            onFinish();
            break;
          default:
            setVoucher(voucher + keyboardNumber);
            break;
        }
      }}
      className={cn([
        "w-[90px] h-[90px] border-2 !border-primary_text_color mb-4 rounded-full flex items-center justify-center",
        keyboardNumber === "C" && "bg-primary_color border-none",
      ])}
    >
      {keyboardNumber === "C" ? (
        <SIcon
          weight="bold"
          name="SArrowUDownLeft"
          color="primary_text_color"
          size={34}
        />
      ) : keyboardNumber === "R" ? (
        <SIcon
          size={40}
          name="BackspaceIcon"
          weight="fill"
          color="primary_text_color"
        />
      ) : (
        <SText type="main" className="text-[50px] font-semibold">
          {keyboardNumber}
        </SText>
      )}
    </SButton>
  );
};

const SKeyboard = ({
  setVoucher,
  voucher,
  onFinish,
  disabled = false,
}: SKeyboardProps) => {
  return (
    <div className="flex flex-col items-center flex-1">
      {KEYBOARD_NUMBERS.map((row, index) => (
        <div key={index} className="flex flex-row gap-4">
          {row.map((keyboardNumber) => (
            <KeyboardNumber
              key={keyboardNumber}
              keyboardNumber={keyboardNumber.toString()}
              setVoucher={setVoucher}
              voucher={voucher}
              onFinish={onFinish}
              disabled={disabled}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

export default SKeyboard;
