import { SButton } from "@/components/atoms";
import SImage from "@/components/atoms/SImage";
import { cn } from "@/cores/helpers";

type TSTopicThumb = {
  item: any;
  className?: string;
  onClick: (item: any) => void;
};

const STopicThumb = ({ item, className, onClick }: TSTopicThumb) => {
  return (
    <SButton
      onClick={() => onClick(item)}
      noBackground
      noBorder
      className={cn("flex-1 p-0 relative", className)}
    >
      <SImage
        src={item.imageUrl}
        className="w-full h-full object-cover rounded-xl"
      />
    </SButton>
  );
};

export default STopicThumb;
