import { SButton } from "@/components/atoms";
import SText from "@/components/atoms/SText";
import { cn } from "@/cores/helpers";
import { useTheme } from "@/hooks/useTheme";
import { useMemo } from "react";

type SPriceSelectorProps = {
  size: any;
  selectedPrice: any;
  setSelectedPrice: any;
};

const SPriceSelector = ({
  size,
  selectedPrice,
  setSelectedPrice,
}: SPriceSelectorProps) => {
  const isActivated = useMemo(
    () => selectedPrice?.id === size.id,
    [selectedPrice, size]
  );

  return (
    <div
      className={cn([
        "flex flex-col items-center w-[300px] rounded-2xl h-[400px] overflow-hidden justify-center",
        isActivated
          ? "bg-primary_color"
          : "border-2 border-secondary_text_color",
      ])}
      key={size.id}
    >
      <SButton
        noDefaultStyle
        effect={isActivated ? "light" : "dark"}
        onClick={() => setSelectedPrice(size)}
        className="rounded-lg flex w-full h-full flex-col items-center justify-center"
      >
        <SText className="text-[150px] font-bold" type="main">
          {size.vi_title}
        </SText>
        <SText className="text-[64px]" type="main">
          ảnh
        </SText>
        <SText className="text-[24px] mt-2" type="sub">
          {size.en_title}
        </SText>
      </SButton>
      <div
        className={cn([
          "w-full h-[85px] rounded-t-2xl flex items-center justify-center",
          isActivated ? "bg-secondary_color" : "bg-primary_color",
        ])}
      >
        <SText
          type={isActivated ? "sub2" : "main"}
          className="text-[38px] font-bold"
        >
          {size.price?.toLocaleString("vi-VN", {
            style: "currency",
            currency: "VND",
          })}
        </SText>
      </div>
    </div>
  );
};

export default SPriceSelector;
