import { SButton } from "@/components/atoms";
import SIcon from "@/components/atoms/SIcon";
import Slider from "react-slick";
import { useRef } from "react";

interface FrameSliderProps {
  currentIndex?: number;
  onSlideChange?: (index: number) => void;
  onStickerChange?: (data: any) => void;
  stickers?: any[];
  isPrinting?: boolean;
}

const StickerSlider: React.FC<FrameSliderProps> = ({
  currentIndex,
  onSlideChange,
  onStickerChange,
  stickers = [],
  isPrinting,
}) => {
  const ref = useRef<any>(null);
  const settings = {
    dots: false,
    className: "center w-full",
    infinite: false,
    speed: 300,
    slidesToShow: 1,
    slidesToScroll: 1,
    centerPadding: "0px",
    centerMode: true,
    autoplay: false,
    waitForAnimate: true,
    focusOnSelect: true,
    afterChange: (index: number) => {
      onSlideChange?.(index);
    },
  };

  const stickersSlide = Math.ceil(stickers?.length / 5);

  return (
    <div className="flex flex-row gap-4 flex-1 w-full justify-center items-center">
      <SButton
        className="bg-primary_color w-[60px] h-[60px] rounded-full items-center justify-center flex"
        onClick={() => ref.current?.slickPrev()}
        noDefaultStyle
      >
        <SIcon
          name="ChevronLeftIcon"
          weight="bold"
          color="primary_text_color"
          size={40}
        />
      </SButton>

      <div className="slider-container flex flex-row flex-1 max-w-[500px]">
        {/* @ts-ignore */}
        <Slider ref={ref} {...settings}>
          {Array.from({ length: stickersSlide })
            .map((_: unknown, index: number) => ({
              id: index + 1,
            }))
            .map((slide, index) => (
              <div key={`slide-item-${slide?.id}-${index}`}>
                <div className="grid grid-cols-5 gap-5 w-fit">
                  {[...stickers]
                    ?.slice(currentIndex * 5, (currentIndex + 1) * 5)
                    ?.map((sticker: any) => (
                      <button
                        key={`sticker-item-${sticker?.id}`}
                        className="cursor-pointer"
                        disabled={isPrinting}
                        onClick={() => onStickerChange?.(sticker)}
                      >
                        <img src={sticker?.image} className="w-20 h-20" />
                      </button>
                    ))}
                </div>
              </div>
            ))}
        </Slider>
      </div>

      <SButton
        className="bg-primary_color w-[60px] h-[60px] rounded-full items-center justify-center flex"
        onClick={() => ref.current?.slickNext()}
        noDefaultStyle
      >
        <SIcon
          name="ChevronRightIcon"
          weight="bold"
          color="primary_text_color"
          size={40}
        />
      </SButton>
    </div>
  );
};

export default StickerSlider;
