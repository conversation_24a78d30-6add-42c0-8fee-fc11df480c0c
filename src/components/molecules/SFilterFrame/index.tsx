import useFrameDimensions from "@/hooks/useFrameDimension";
import { useOrder } from "@/hooks/useOrder";
import StickerItem from "@/primary-screens/filter-sticker/components/StickerItem";
import CropImageItem from "@/primary-screens/select-image/components/CropImageItem";
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
} from "react";

const SFilterFrame = forwardRef(
  (
    {
      frame,
      frameSize,
      photos,
      photoMaps,
      filters,
      onDeleteSticker,
      onStickerChange,
      getFilterStyle,
      currentIndex,
      setCurrentIndex,
    }: any,
    ref
  ) => {
    const mockRef = useRef(null);
    const boundRef = useRef(null);
    const { draftOrder } = useOrder();
    const frameDimensions = useFrameDimensions({
      ref: boundRef,
      frame,
      frameSize,
    });

    const aspectRatioWidth = frameDimensions?.width / frameSize?.width || 1;
    const aspectRatioHeight = frameDimensions?.height / frameSize?.height || 1;

    const dateFrameItems = useMemo(
      () =>
        frame?.frameItems?.filter(
          (record: any) => record?.itemType === "Date"
        ) ?? [],
      [frame?.frameItems]
    );

    const qrFrameItems = useMemo(
      () =>
        frame?.frameItems?.filter(
          (record: any) => record?.itemType === "QrCode"
        ) ?? [],
      [frame?.frameItems]
    );

    useImperativeHandle(ref, () => ({
      getFrameDimensions: () => frameDimensions,
    }));

    const renderFrameItems = useCallback(() => {
      return frame.frameItems?.map((frameItem: any, index: number) => {
        const photoIndex = photoMaps?.get(frameItem.id);
        const url = photos?.[photoIndex];
        const isMixed = frameItem.position % 2 === 0;

        if (photoIndex !== undefined && photoIndex !== -1 && url) {
          return (
            <CropImageItem
              key={`frame-item-${frameItem.id}-${index}`}
              image={url}
              {...frameItem}
              aspectRatioWidth={frameDimensions?.width / frameSize?.width}
              aspectRatioHeight={frameDimensions?.height / frameSize?.height}
              background={filters.background}
              style={getFilterStyle(isMixed)}
            />
          );
        }
        return null;
      });
    }, [frame?.frameItems, photoMaps, photos, frameDimensions, getFilterStyle]);

    return (
      <div
        className="w-full h-[630px] flex justify-center items-center"
        ref={boundRef}
      >
        {frameDimensions && (
          <div
            className="flex w-fit items-center justify-center relative"
            ref={mockRef}
          >
            {frame?.imageUrl && (
              <img
                src={frame?.imageUrl}
                alt="frame"
                style={{
                  width: frameDimensions.width,
                  height: frameDimensions.height,
                  objectFit: "cover",
                }}
              />
            )}

            {renderFrameItems()}

            {filters?.date_image &&
              dateFrameItems?.map((dfr: any, index: number) => (
                <CropImageItem
                  key={`date-frame-item-${dfr.id}-${index}`}
                  date
                  {...(dfr ?? {
                    x_coordinate: 0,
                    y_coordinate: 0,
                  })}
                  aspectRatioWidth={aspectRatioWidth}
                  aspectRatioHeight={aspectRatioHeight}
                />
              ))}

            {filters?.qr_type &&
              draftOrder?.domain &&
              qrFrameItems?.map((qrf: any, index: number) => (
                <CropImageItem
                  key={`qr-frame-item-${qrf.id}-${index}`}
                  qrUrl={draftOrder?.domain}
                  qr
                  {...(qrf ?? {
                    x_coordinate: 0,
                    y_coordinate: 0,
                  })}
                  aspectRatioWidth={aspectRatioWidth}
                  aspectRatioHeight={aspectRatioHeight}
                />
              ))}

            {filters?.stickers?.map((sticker: any, index: number) => (
              <StickerItem
                key={`sticker-${sticker.id}-${index}`}
                data={sticker}
                onDeleteSticker={() => onDeleteSticker(index)}
                onStickerChange={onStickerChange}
                isActive={currentIndex === index}
                onStickerClick={() => setCurrentIndex(index)}
                currentIndex={index}
              />
            ))}
          </div>
        )}
      </div>
    );
  }
);

export default SFilterFrame;
