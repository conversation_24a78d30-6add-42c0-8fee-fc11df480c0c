
interface SWaitingImageProps {
  image: any;
}

const SWaitingImage = ({ image }: SWaitingImageProps) => {
  return (
    <div className="w-full h-full flex justify-center items-center">
      <div className="w-full h-full">
        <img
          className="w-full h-full object-contain"
          src={image.url}
          alt="frame"
        />
      </div>
    </div>
  );
};

export default SWaitingImage;
