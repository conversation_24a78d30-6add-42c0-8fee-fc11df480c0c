import { useTheme } from "@/hooks/useTheme";

const STheme = () => {
  const theme = useTheme();

  return (
    <style>
      {`
          .bg-primary_color { background-color: ${theme.primary_color}; }
          .bg-background_color { background-color: ${theme.background_color}; }
          .bg-secondary_color { background-color: ${theme.secondary_color}; }
          .bg-primary_text_color { background-color: ${theme.primary_text_color}; }
          .bg-secondary_text_color { background-color: ${theme.secondary_text_color}; }
          .bg-secondary_text_color_2 { background-color: ${theme.secondary_text_color_2}; }
          .bg-primary_color_10 { background-color: ${theme.primary_color}; opacity: 0.1; }

          .text-primary_color { color: ${theme.primary_color}; }
          .text-background_color { color: ${theme.background_color}; }
          .text-secondary_color { color: ${theme.secondary_color}; }
          .text-primary_text_color { color: ${theme.primary_text_color}; }
          .text-secondary_text_color { color: ${theme.secondary_text_color}; }
          .text-secondary_text_color_2 { color: ${theme.secondary_text_color_2}; }

          .border-primary_color { border-color: ${theme.primary_color}; }
          .border-background_color { border-color: ${theme.background_color}; }
          .border-secondary_color { border-color: ${theme.secondary_color}; }
          .border-primary_text_color { border-color: ${theme.primary_text_color}; }
          .border-secondary_text_color { border-color: ${theme.secondary_text_color}; }
          .border-secondary_text_color_2 { border-color: ${theme.secondary_text_color_2}; }
        `}
    </style>
  );
};

export default STheme;
