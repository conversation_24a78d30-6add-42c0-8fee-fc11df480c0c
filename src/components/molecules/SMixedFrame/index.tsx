import useFrameDimensions from "@/hooks/useFrameDimension";
import CropImageItem from "@/primary-screens/select-image/components/CropImageItem";
import { useRef, memo, useMemo } from "react";

interface FrameItem {
  id: string;
  x_coordinate: number;
  y_coordinate: number;
  // ... thêm các thuộc t<PERSON>h khác của frameItem
}

interface Frame {
  imageUrl: string;
  frameItems?: FrameItem[];
}

interface FrameSize {
  width: number;
  height: number;
}

interface SMixedFrameProps {
  frame: Frame;
  frameSize: FrameSize;
  photos: any[];
  photoMaps: Map<string, number>;
  className?: string;
  handleRemoveImage: (photoIndex: number) => void;
}

const SMixedFrame = memo(
  ({
    frame,
    frameSize,
    photos,
    photoMaps,
    className = "",
    handleRemoveImage,
  }: SMixedFrameProps) => {
    const mockRef = useRef<HTMLDivElement>(null);
    const boundRef = useRef<HTMLDivElement>(null);

    const frameDimensions = useFrameDimensions({
      ref: boundRef,
      frame,
      frameSize,
    });

    const imageStyle = useMemo(
      () => ({
        width: frameDimensions?.width,
        height: frameDimensions?.height,
        objectFit: "cover" as const,
      }),
      [frameDimensions?.width, frameDimensions?.height]
    );

    const aspectRatios = useMemo(
      () => ({
        width: frameDimensions?.width
          ? frameDimensions.width / frameSize.width
          : 0,
        height: frameDimensions?.height
          ? frameDimensions.height / frameSize.height
          : 0,
      }),
      [frameDimensions, frameSize]
    );

    if (!frame?.imageUrl) return null;

    return (
      <div
        className={`flex-1 flex max-h-[630px] justify-center ${className}`.trim()}
        ref={boundRef}
      >
        <div className="flex w-fit justify-center relative" ref={mockRef}>
          {frameDimensions && (
            <img
              src={frame.imageUrl}
              alt={`Frame ${frame.imageUrl.split("/").pop()}`}
              style={imageStyle}
              loading="lazy"
              decoding="async"
            />
          )}

          {frame.frameItems?.map((frameItem, index) => {
            const photoIndex = photoMaps?.get(frameItem.id);
            if (photoIndex === undefined || photoIndex === -1) return null;

            return (
              <CropImageItem
                key={`crop-image-item-${frameItem.id}-${index}`}
                image={photos[photoIndex]}
                x_coordinate={frameItem.x_coordinate}
                y_coordinate={frameItem.y_coordinate}
                {...frameItem}
                aspectRatioWidth={aspectRatios.width}
                aspectRatioHeight={aspectRatios.height}
                onRemoveImage={() => handleRemoveImage(photoIndex)}
              />
            );
          })}
        </div>
      </div>
    );
  }
);

SMixedFrame.displayName = "SMixedFrame";

export default SMixedFrame;
