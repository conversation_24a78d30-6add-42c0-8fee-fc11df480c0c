import React, { useState, useEffect, useRef } from "react";
import DEFAULT_WAITING_SCREEN_IMAGE from "@/assets/default-waiting-screen.gif";

interface SlideItem {
  type: "video" | "image";
  url: string;
}

interface SAutoSlideProps {
  items: SlideItem[];
  interval?: number; // Thời gian chuy<PERSON>n slide (tính bằng ms)
}

const SAutoSlide: React.FC<SAutoSlideProps> = ({ items, interval = 5000 }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const videoRef = useRef<HTMLVideoElement>(null);

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % items.length);
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (items[currentIndex]?.type === "video" && videoRef.current) {
      videoRef.current.play();
      videoRef.current.onended = () => {
        goToNext();
      };
    } else {
      timer = setTimeout(goToNext, interval);
    }

    return () => {
      if (timer) clearTimeout(timer);
      if (videoRef.current) {
        videoRef.current.onended = null;
      }
    };
  }, [currentIndex, interval, items]);

  if (!items?.length)
    return (
      <div className="relative w-screen h-screen overflow-hidden">
        <img
          src={DEFAULT_WAITING_SCREEN_IMAGE}
          alt={`slide-`}
          className="w-full h-full object-fit"
        />
      </div>
    );

  return (
    <div className="relative w-screen h-screen overflow-hidden">
      {items.map((item, index) => (
        <div
          key={index}
          className={`absolute top-0 left-0 w-full h-full transition-opacity duration-500 ease-in-out
            ${currentIndex === index ? "opacity-100 z-10" : "opacity-0 z-0"}`}
        >
          {item.type === "video" ? (
            <video
              ref={currentIndex === index ? videoRef : null}
              src={item.url}
              className="w-full h-full object-cover"
              muted
              playsInline
            />
          ) : (
            <img
              src={item.url}
              alt={`slide-${index}`}
              className="w-full h-full object-fit"
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default SAutoSlide;
