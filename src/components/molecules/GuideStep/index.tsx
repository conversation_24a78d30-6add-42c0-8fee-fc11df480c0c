import React from "react";
import SAutoSlide from "../SAutoSlide";

type Props = {
  slides?: any[];
  onClick: () => void;
};

const GuideStep = ({ slides, onClick }: Props) => {
  const handleClick = () => {
    onClick();
  };

  return (
    <div
      onClick={handleClick}
      className="bg-white w-screen h-screen flex flex-col max-h-screen overflow-hidden cursor-pointer relative"
    >
      <SAutoSlide items={slides} />
    </div>
  );
};

export default React.memo(GuideStep);
