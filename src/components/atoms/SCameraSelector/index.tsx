import { useOrder } from "@/hooks/useOrder";
import SIcon from "../SIcon";
import SText from "../SText";

type Props = {
  onSwitchCamera?: () => void;
};

const SCameraSelector = ({ onSwitchCamera }: Props) => {
  const { activeCameraIndex } = useOrder();

  return (
    <div className="flex flex-col absolute top-10 right-10 gap-4">
      <button
        onClick={() =>
          window.api.switchCamera(0).catch((error) => {
            console.error(
              `[useLiveView] Error switching to camera at index 0:`,
              error
            );

            onSwitchCamera?.();
          })
        }
        className={`w-[100px] h-[100px] rounded-lg flex flex-col items-center justify-center ${
          activeCameraIndex === 0
            ? "bg-primary_color"
            : "border-2 border-primary_text_color"
        }`}
      >
        <div className="relative flex flex-col items-center justify-center">
          <SIcon name="SCamera" size={55} color="primary_text_color" />
          <div className="absolute top-[20%] right-[45%] flex justify-center items-center">
            <SText type="main" className="text-[25px] font-bold">
              1
            </SText>
          </div>
        </div>
        <SText type="main" className="text-[18px] font-bold">
          Cam 01
        </SText>
      </button>

      <button
        onClick={() =>
          window.api.switchCamera(1).catch((error) => {
            console.error(
              `[useLiveView] Error switching to camera at index 0:`,
              error
            );

            onSwitchCamera?.();
          })
        }
        className={`w-[100px] h-[100px] rounded-lg flex flex-col items-center justify-center ${
          activeCameraIndex === 1
            ? "bg-primary_color"
            : "border-2 border-primary_text_color"
        }`}
      >
        <div className="relative">
          <SIcon name="SCamera" size={55} color="primary_text_color" />
          <div className="absolute top-[20%] right-[45%] flex justify-center items-center">
            <SText type="main" className="text-[25px] font-bold">
              2
            </SText>
          </div>
        </div>
        <SText type="main" className="text-[18px] font-bold">
          Cam 02
        </SText>
      </button>
    </div>
  );
};

export default SCameraSelector;
