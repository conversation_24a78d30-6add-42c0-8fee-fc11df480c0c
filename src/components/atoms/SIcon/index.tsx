import * as OutlineIcon from "@heroicons/react/24/outline";
import * as SolidIcon from "@heroicons/react/24/solid";
import { useMemo } from "react";

import { TSColorName } from "@/cores/theme/colors";
import SCaretLeft from "./svgs/SCaretLeft";
import SCaretRight from "./svgs/SCaretRight";
import SArrowUDownLeft from "./svgs/SArrowUDownLeft";
import SArrowLeft from "./svgs/SArrowLeft";
import SMoney from "./svgs/SMoney";
import SPrinter from "./svgs/SPrinter";
import SArrowRight from "./svgs/SArrowRight";
import SAutoMode from "./svgs/SAutoMode";
import SRemoteMode from "./svgs/SRemoteMode";
import { useTheme } from "@/hooks/useTheme";
import SCheck from "./svgs/SCheck";
import SClear from "./svgs/SClear";
import SReturn from "./svgs/SReturn";
import SCoundown from "./svgs/SCoundown";
import STwoViews from "./svgs/STwoViews";
import SCamera from "./svgs/SCamera";
import SNoBackground from "./svgs/SNoBackground";

export type SLibIconName = keyof typeof OutlineIcon | keyof typeof SolidIcon;

const SCIcons = {
  SCaretLeft,
  SCaretRight,
  SArrowUDownLeft,
  SMoney,
  SPrinter,
  SArrowLeft,
  SArrowRight,
  SAutoMode,
  SRemoteMode,
  SCheck,
  SClear,
  SReturn,
  SCoundown,
  STwoViews,
  SCamera,
  SNoBackground,
};

const SCNames = Object.keys(SCIcons);

export type SIconName = SLibIconName | keyof typeof SCIcons;

type Props = {
  name: SIconName;
  weight?: "bold" | "regular" | "thin" | "light" | "fill";
  className?: string;
  size?: number;
  color?: TSColorName | "white";
};

const SIcon = ({
  name,
  size = 24,
  color,
  weight = "regular",
  className,
}: Props) => {
  const theme = useTheme();

  const iconColor = useMemo(
    () => (color ? theme?.[color] : theme?.primary_color),
    [color]
  ) as string;

  const commonProps = useMemo(() => {
    return {
      width: size,
      height: size,
      color: iconColor,
    };
  }, [size, iconColor]);

  if (SCNames.includes(name)) {
    const SvgComp = SCIcons[name as keyof typeof SCIcons];

    return <SvgComp size={size} color={iconColor} />;
  }

  const SolidIconComp: any = useMemo(
    () => SolidIcon[name as SLibIconName] ?? SolidIcon.QuestionMarkCircleIcon,
    [name]
  );

  const OutlineIconComp: any = useMemo(
    () =>
      OutlineIcon[name as SLibIconName] ?? OutlineIcon.QuestionMarkCircleIcon,
    [name]
  );

  return (
    <>
      {weight === "fill" ? (
        <SolidIconComp {...commonProps} className={className} />
      ) : (
        <OutlineIconComp {...commonProps} className={className} />
      )}
    </>
  );
};

export default SIcon;
