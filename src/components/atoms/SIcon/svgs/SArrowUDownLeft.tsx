import { useMemo } from "react";

const SArrowUDownLeft = ({ size = 15, color = "#fff" }) => {
  const width = useMemo(() => Math.floor((39 * size) / 34), [size]);

  return (
    <svg
      width={width}
      height={size}
      viewBox={`0 0 39 34`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.46973 2H25.4697C28.6523 2 31.7046 3.26433 33.9551 5.51477C36.2055 7.76521 37.4697 10.8174 37.4697 14C37.4697 17.1826 36.2055 20.2349 33.9551 22.4853C31.7046 24.7357 28.6523 26 25.4697 26H11.7698"
        stroke={color}
        strokeWidth="3"
        strokeMiterlimit="10"
      />
      <path
        d="M13.9002 18.49L0.950195 25.97L13.9002 33.45V18.49Z"
        fill={color}
      />
    </svg>
  );
};

export default SArrowUDownLeft;
