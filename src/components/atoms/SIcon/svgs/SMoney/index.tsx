const SMoney = ({ size = 80, color = "#F385A1" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 80 80"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        opacity="0.2"
        d="M5 21.6281V59.1281C33.6375 45.1375 46.3625 72.3625 75 58.3719V20.8719C46.3625 34.8625 33.6375 7.6375 5 21.6281ZM40 47.5C38.5166 47.5 37.0666 47.0601 35.8332 46.236C34.5999 45.4119 33.6386 44.2406 33.0709 42.8701C32.5032 41.4997 32.3547 39.9917 32.6441 38.5368C32.9335 37.082 33.6478 35.7456 34.6967 34.6967C35.7456 33.6478 37.082 32.9335 38.5368 32.6441C39.9917 32.3547 41.4997 32.5032 42.8701 33.0709C44.2406 33.6386 45.4119 34.5999 46.236 35.8332C47.0601 37.0666 47.5 38.5166 47.5 40C47.5 41.9891 46.7098 43.8968 45.3033 45.3033C43.8968 46.7098 41.9891 47.5 40 47.5Z"
        fill={color}
      />
      <path
        d="M76.325 18.75C75.9651 18.5254 75.5539 18.396 75.1302 18.3742C74.7065 18.3523 74.2843 18.4386 73.9031 18.625C60.4875 25.1875 50.9062 22.1125 40.7781 18.8688C30.1531 15.4656 19.1375 11.95 3.91875 19.3813C3.49353 19.5852 3.13468 19.9053 2.88364 20.3046C2.63259 20.7038 2.49959 21.1659 2.5 21.6375V59.1156C2.49994 59.5398 2.60781 59.957 2.81345 60.328C3.01909 60.699 3.31575 61.0116 3.6755 61.2363C4.03525 61.461 4.44626 61.5905 4.86986 61.6126C5.29346 61.6347 5.71571 61.5486 6.09688 61.3625C19.5125 54.8 29.0938 57.875 39.2375 61.1188C45.25 63.0406 51.375 65 58.3 65C63.6406 65 69.4656 63.8375 76.0844 60.6063C76.5048 60.401 76.8591 60.0819 77.1072 59.6853C77.3552 59.2886 77.487 58.8303 77.4875 58.3625V20.8844C77.4913 20.4591 77.3865 20.0399 77.1831 19.6664C76.9797 19.2929 76.6843 18.9775 76.325 18.75ZM72.5 56.7719C59.8125 62.45 50.5469 59.4875 40.7625 56.3594C34.75 54.4375 28.625 52.4781 21.7 52.4781C16.8347 52.5018 12.0189 53.4564 7.5125 55.2906V23.2281C20.2 17.55 29.4656 20.5125 39.25 23.6406C49.0344 26.7688 59.1063 30 72.5 24.7156V56.7719ZM40 30C38.0222 30 36.0888 30.5865 34.4443 31.6853C32.7998 32.7841 31.5181 34.3459 30.7612 36.1732C30.0043 38.0004 29.8063 40.0111 30.1921 41.9509C30.578 43.8907 31.5304 45.6725 32.9289 47.0711C34.3275 48.4696 36.1093 49.422 38.0491 49.8079C39.9889 50.1937 41.9996 49.9957 43.8268 49.2388C45.6541 48.4819 47.2159 47.2002 48.3147 45.5557C49.4135 43.9112 50 41.9778 50 40C50 37.3478 48.9464 34.8043 47.0711 32.9289C45.1957 31.0536 42.6522 30 40 30ZM40 45C39.0111 45 38.0444 44.7068 37.2221 44.1574C36.3999 43.6079 35.759 42.8271 35.3806 41.9134C35.0022 40.9998 34.9031 39.9945 35.0961 39.0246C35.289 38.0547 35.7652 37.1637 36.4645 36.4645C37.1637 35.7652 38.0546 35.289 39.0245 35.0961C39.9945 34.9032 40.9998 35.0022 41.9134 35.3806C42.827 35.759 43.6079 36.3999 44.1573 37.2222C44.7068 38.0444 45 39.0111 45 40C45 41.3261 44.4732 42.5979 43.5355 43.5355C42.5979 44.4732 41.3261 45 40 45ZM17.5 30V45C17.5 45.663 17.2366 46.2989 16.7678 46.7678C16.2989 47.2366 15.663 47.5 15 47.5C14.337 47.5 13.7011 47.2366 13.2322 46.7678C12.7634 46.2989 12.5 45.663 12.5 45V30C12.5 29.337 12.7634 28.7011 13.2322 28.2322C13.7011 27.7634 14.337 27.5 15 27.5C15.663 27.5 16.2989 27.7634 16.7678 28.2322C17.2366 28.7011 17.5 29.337 17.5 30ZM62.5 50V35C62.5 34.337 62.7634 33.7011 63.2322 33.2322C63.7011 32.7634 64.337 32.5 65 32.5C65.663 32.5 66.2989 32.7634 66.7678 33.2322C67.2366 33.7011 67.5 34.337 67.5 35V50C67.5 50.663 67.2366 51.2989 66.7678 51.7678C66.2989 52.2366 65.663 52.5 65 52.5C64.337 52.5 63.7011 52.2366 63.2322 51.7678C62.7634 51.2989 62.5 50.663 62.5 50Z"
        fill={color}
      />
    </svg>
  );
};

export default SMoney;
