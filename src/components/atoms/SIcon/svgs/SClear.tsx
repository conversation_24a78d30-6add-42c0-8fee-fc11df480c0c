const SClear = ({ size = 15 }: any) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox={`0 0 ${size} ${size}`}
      fill="none"
    >
      <g clipPath="url(#clip0_28_13632)">
        <path
          d="M15 30C23.2843 30 30 23.2843 30 15C30 6.71573 23.2843 0 15 0C6.71573 0 0 6.71573 0 15C0 23.2843 6.71573 30 15 30Z"
          fill="url(#paint0_linear_28_13632)"
        />
        <path
          d="M16.3306 14.9133L20.0641 21.1648H17.3377L14.9761 17.2055L12.6144 21.1648H9.90544L13.6216 14.9307L10.0965 9.00915H12.8054L14.9761 12.6558L17.1467 9.00915H19.8557L16.3306 14.9133Z"
          fill="white"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_28_13632"
          x1="0"
          y1={size / 2}
          x2={size}
          y2={size / 2}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F37021" />
          <stop offset="1" stopColor="#ED1C24" />
        </linearGradient>
        <clipPath id="clip0_28_13632">
          <rect width={size} height={size} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default SClear;
