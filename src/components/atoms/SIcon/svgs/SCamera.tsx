const SCamera = ({ size = 15, color = "#231F20" }) => {
  const height = size * 1.1;

  return (
    <svg
      width={size}
      height={height}
      viewBox={`0 0 ${size} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M43.1107 4.48982C42.3955 3.8228 41.5531 3.30714 40.6338 2.97377C39.7144 2.6404 38.7372 2.4962 37.7607 2.54983H34.2607C34.1287 2.54903 33.9998 2.50984 33.8897 2.43703C33.7796 2.36423 33.6931 2.26096 33.6407 2.13982C33.4293 1.67232 33.0878 1.27562 32.6568 0.997185C32.2259 0.718752 31.7237 0.570378 31.2107 0.569824H14.7907C14.2777 0.570378 13.7755 0.718752 13.3446 0.997185C12.9136 1.27562 12.5721 1.67232 12.3607 2.13982C12.3083 2.26096 12.2217 2.36423 12.1117 2.43703C12.0016 2.50984 11.8727 2.54903 11.7407 2.54983H8.2507C7.27327 2.50167 6.29601 2.64838 5.37582 2.98144C4.45562 3.31449 3.61089 3.82723 2.89071 4.48982C2.25633 5.07151 1.75065 5.7795 1.40621 6.56827C1.06177 7.35704 0.886191 8.20914 0.89071 9.06982V40.1998C0.890367 41.0555 1.06801 41.9018 1.41232 42.6851C1.75663 43.4684 2.26007 44.1715 2.89071 44.7498C3.60172 45.4235 4.44074 45.9476 5.358 46.2912C6.27525 46.6347 7.25205 46.7907 8.23068 46.7498H37.7407C38.7199 46.7959 39.6982 46.6423 40.6162 46.2985C41.5342 45.9547 42.3726 45.4278 43.0807 44.7498C43.7065 44.1784 44.2074 43.4836 44.5516 42.7091C44.8958 41.9347 45.0759 41.0973 45.0807 40.2498V9.01983C45.0882 8.16884 44.9167 7.32578 44.5774 6.54536C44.238 5.76495 43.7383 5.06461 43.1107 4.48982ZM39.6807 39.5798C39.6982 39.9813 39.6245 40.3815 39.4652 40.7504C39.3058 41.1193 39.065 41.4473 38.7607 41.7098C38.0909 42.2567 37.2449 42.5411 36.3807 42.5098H9.64071C8.77968 42.5404 7.93704 42.256 7.27071 41.7098C6.96978 41.4555 6.73063 41.1361 6.57125 40.7758C6.41187 40.4154 6.33639 40.0236 6.35067 39.6298V9.97983C6.34654 9.57872 6.42625 9.18117 6.58468 8.81266C6.74311 8.44415 6.97679 8.1128 7.27071 7.83983C7.92886 7.27809 8.77642 6.98841 9.64071 7.02982H13.8907C14.1022 7.0291 14.3091 6.9674 14.4864 6.85212C14.6638 6.73683 14.8042 6.57286 14.8907 6.37983L15.0007 6.13982C15.1576 5.7855 15.4141 5.48441 15.739 5.27315C16.0639 5.06189 16.4431 4.94956 16.8307 4.94983H29.1707C29.5583 4.94956 29.9375 5.06189 30.2624 5.27315C30.5873 5.48441 30.8437 5.7855 31.0007 6.13982L31.1107 6.37983C31.1972 6.57286 31.3376 6.73683 31.515 6.85212C31.6923 6.9674 31.8991 7.0291 32.1107 7.02982H36.3807C37.2485 6.9848 38.1005 7.27476 38.7607 7.83983C39.0624 8.10659 39.3014 8.4368 39.4604 8.80678C39.6195 9.17676 39.6947 9.57735 39.6807 9.97983V39.5798Z"
        fill={color}
      />
    </svg>
  );
};

export default SCamera;
