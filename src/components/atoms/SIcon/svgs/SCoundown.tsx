import { useMemo } from "react";

const SCoundown = ({ size = 338, color = "#231F20" }) => {
  const width = useMemo(() => Math.round((size * 341) / 338), [size]);

  return (
    <svg
      width={width}
      height={size}
      viewBox={`0 0 ${width} ${size}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M253.501 311.71C218.623 332.363 177.389 339.529 137.588 331.855C97.7868 324.18 62.1739 302.196 37.4758 270.055C12.7778 237.914 0.70443 197.842 3.53638 157.407C6.36833 116.972 23.9094 78.973 52.8464 50.5886C81.7835 22.2042 120.113 5.39899 160.595 3.34717C201.078 1.29536 240.91 14.139 272.569 39.4522C304.227 64.7653 325.52 100.796 332.426 140.737C339.332 180.679 331.372 221.767 310.051 256.24"
        stroke={color}
        strokeWidth="6"
        strokeMiterlimit="10"
      />
      <path
        d="M298.57 226.55L304.43 225.22L310.94 254.08L339.25 245.48L340.99 251.23L306.51 261.69L298.57 226.55Z"
        fill={color}
      />
    </svg>
  );
};

export default SCoundown;
