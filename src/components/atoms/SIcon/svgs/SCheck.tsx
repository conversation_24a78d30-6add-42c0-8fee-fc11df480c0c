const SCheck = ({ size = 32 }: any) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox={`0 0 ${size} ${size}`}
      fill="none"
    >
      <path
        d="M15.9102 30.78C24.1944 30.78 30.9102 24.0643 30.9102 15.78C30.9102 7.49576 24.1944 0.780029 15.9102 0.780029C7.62589 0.780029 0.910156 7.49576 0.910156 15.78C0.910156 24.0643 7.62589 30.78 15.9102 30.78Z"
        fill="url(#paint0_linear_97_2)"
      />
      <path
        d="M15.9102 30.78C24.1944 30.78 30.9102 24.0643 30.9102 15.78C30.9102 7.49576 24.1944 0.780029 15.9102 0.780029C7.62589 0.780029 0.910156 7.49576 0.910156 15.78C0.910156 24.0643 7.62589 30.78 15.9102 30.78Z"
        fill="url(#paint1_linear_97_2)"
        stroke="#00AB4F"
        strokeMiterlimit="10"
      />
      <path
        d="M16.8517 22.9448H14.681L12.5625 19.4891H10.7912V17.3359H13.8475L15.584 20.34L19.3696 9.05266H21.6618L16.8517 22.9448Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_97_2"
          x1="0.910156"
          y1="15.78"
          x2="30.9102"
          y2="15.78"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#70BF54" />
          <stop offset="1" stopColor="#00AE4D" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_97_2"
          x1="0.910156"
          y1="15.78"
          x2="30.9102"
          y2="15.78"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#70BF54" />
          <stop offset="1" stopColor="#00AE4D" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default SCheck;
