const SRemoteMode = ({ size = 15, color = "#231F20" }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size + 50}
      height={size}
      viewBox={`0 0 ${size + 50} ${size}`}
      fill="none"
    >
      <path
        d="M191.38 24.69C190.316 23.7613 188.93 23.2889 187.52 23.375C186.111 23.4611 184.792 24.0988 183.85 25.15L154.26 58.33C152.493 60.2948 150.024 61.4862 147.386 61.6471C144.748 61.808 142.153 60.9255 140.16 59.19C139.175 58.3337 138.37 57.2902 137.791 56.1201C137.213 54.95 136.873 53.6767 136.791 52.3741C136.71 51.0715 136.887 49.7656 137.315 48.5323C137.742 47.299 138.41 46.1629 139.28 45.19L148.73 34.59C150.521 32.5807 151.897 30.2361 152.776 27.6918C153.656 25.1475 154.022 22.454 153.853 19.7673C153.685 17.0806 152.986 14.4539 151.795 12.0393C150.605 9.62463 148.948 7.46998 146.92 5.7C142.799 2.10969 137.431 0.283998 131.975 0.617023C126.52 0.950048 121.414 3.41507 117.76 7.48L98.1699 29.48L91.1699 23.31C89.2451 21.6287 86.7355 20.7731 84.1845 20.9284C81.6336 21.0837 79.2464 22.2375 77.5399 24.14L2.4498 108.34C1.61085 109.279 0.966827 110.376 0.555025 111.566C0.143222 112.756 -0.0280794 114.016 0.0509963 115.273C0.130072 116.53 0.457879 117.758 1.0156 118.887C1.57331 120.016 2.34972 121.023 3.29978 121.85L25.3799 141.18C27.3052 142.864 29.8169 143.722 32.3703 143.567C34.9237 143.411 37.313 142.255 39.0199 140.35L114.11 56.16C114.949 55.2208 115.593 54.1243 116.005 52.9343C116.416 51.7442 116.588 50.4842 116.509 49.2273C116.43 47.9705 116.102 46.7419 115.544 45.6128C114.986 44.4837 114.21 43.4766 113.26 42.65L106.26 36.48L125.85 14.48C127.616 12.5129 130.085 11.3198 132.723 11.1589C135.362 10.998 137.958 11.882 139.95 13.62C140.935 14.4763 141.74 15.5198 142.318 16.6899C142.896 17.86 143.236 19.1333 143.318 20.4359C143.4 21.7385 143.222 23.0444 142.795 24.2777C142.368 25.511 141.7 26.6471 140.83 27.62L131.38 38.22C129.588 40.2293 128.213 42.5739 127.334 45.1182C126.454 47.6625 126.088 50.356 126.256 53.0427C126.425 55.7294 127.124 58.3561 128.314 60.7707C129.504 63.1854 131.161 65.34 133.19 67.11C137.311 70.7003 142.679 72.526 148.134 72.193C153.59 71.86 158.696 69.3949 162.35 65.33L191.86 32.15C192.323 31.6308 192.678 31.0248 192.904 30.3673C193.131 29.7097 193.225 29.0137 193.18 28.3196C193.135 27.6256 192.953 26.9473 192.644 26.3242C192.335 25.7011 191.905 25.1456 191.38 24.69ZM96.2498 66.76C93.047 70.3371 88.5629 72.5071 83.7704 72.7991C78.9778 73.0912 74.2634 71.4818 70.6499 68.32C68.8667 66.7681 67.4095 64.8774 66.363 62.7577C65.3165 60.6381 64.7015 58.3317 64.5536 55.9724C64.4056 53.6131 64.7277 51.2479 65.5013 49.0141C66.2749 46.7804 67.4844 44.7225 69.0598 42.96C72.2654 39.3875 76.7489 37.2209 81.5399 36.9289C86.3309 36.637 91.0442 38.2432 94.6599 41.4C96.4419 42.9527 97.8979 44.8437 98.9437 46.9633C99.9894 49.0829 100.604 51.3889 100.752 53.7479C100.9 56.1068 100.578 58.4716 99.8053 60.7052C99.0324 62.9389 97.8239 64.9969 96.2498 66.76Z"
        fill={color}
      />
    </svg>
  );
};

export default SRemoteMode;
