const SCaretRight = ({ size = 15, color = "#231F20" }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size * 2}
      height={size}
      viewBox={`0 0 ${size * 2} ${size}`}
      fill="none"
    >
      <path
        d="M53.4397 13.5C51.7786 13.5013 50.1336 13.1749 48.5988 12.5396C47.064 11.9042 45.6696 10.9722 44.4955 9.79718C43.3214 8.62213 42.3905 7.22702 41.7564 5.69171C41.1222 4.15641 40.7972 2.51109 40.7998 0.849976"
        stroke={color}
        strokeMiterlimit="10"
      />
      <path
        d="M40.7998 26.14C40.7998 22.7876 42.1315 19.5726 44.502 17.2021C46.8724 14.8317 50.0874 13.5 53.4397 13.5"
        stroke={color}
        strokeMiterlimit="10"
      />
      <path d="M53.4402 13.5H0.410156" stroke={color} strokeMiterlimit="10" />
    </svg>
  );
};

export default SCaretRight;
