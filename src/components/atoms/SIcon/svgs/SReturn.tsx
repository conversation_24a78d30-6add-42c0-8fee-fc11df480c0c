import { useMemo } from "react";

const SReturn = ({ size = 15, color = "#fff" }: any) => {
  const width = useMemo(() => Math.round((size * 55) / 28), [size]);

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={size}
      viewBox={`0 0 ${width} ${size}`}
      fill="none"
    >
      <path
        d="M0.619141 8.97998C2.90623 8.97998 5.0998 9.88781 6.71796 11.5041C8.33611 13.1204 9.2465 15.3129 9.24915 17.6"
        stroke={color}
        strokeMiterlimit="10"
      />
      <path
        d="M9.24915 0.349976C9.24915 2.63879 8.33994 4.83389 6.7215 6.45233C5.10306 8.07077 2.90796 8.97998 0.619141 8.97998"
        stroke={color}
        strokeMiterlimit="10"
      />
      <path
        d="M0.619141 8.97998H15.0791"
        stroke={color}
        strokeMiterlimit="10"
      />
      <path
        d="M15.0792 8.97998H44.9991C47.4577 8.97998 49.8156 9.95661 51.5541 11.6951C53.2925 13.4335 54.2692 15.7914 54.2692 18.25C54.2665 20.7068 53.2887 23.0621 51.5505 24.7984C49.8124 26.5347 47.456 27.51 44.9991 27.5099H0.619141"
        stroke={color}
        strokeMiterlimit="10"
      />
    </svg>
  );
};

export default SReturn;
