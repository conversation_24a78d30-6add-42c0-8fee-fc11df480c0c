import { useMemo } from "react";

const SAutoMode = ({ size = 15, color = "#231F20" }) => {
  const width = useMemo(() => Math.round((size * 15) / 14), [size]);

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={size}
      viewBox={`0 0 ${width} ${size}`}
      fill="none"
    >
      <path
        d="M129.05 21.95C116.28 9.22698 99.359 1.52356 81.3783 0.247488C63.3976 -1.02859 45.558 4.20788 31.1197 15L25.1197 8.99999C24.3536 8.23216 23.3869 7.69541 22.3301 7.45107C21.2733 7.20674 20.1691 7.26468 19.1437 7.61831C18.1183 7.97193 17.213 8.60695 16.5315 9.45077C15.85 10.2946 15.4196 11.3132 15.2897 12.39L11.9797 39.78C11.8749 40.6496 11.9691 41.5316 12.2552 42.3594C12.5413 43.1873 13.0118 43.9392 13.6311 44.5586C14.2504 45.1779 15.0024 45.6484 15.8303 45.9345C16.6581 46.2206 17.5401 46.3148 18.4097 46.21L45.7597 42.82C46.8354 42.6902 47.8529 42.2607 48.6962 41.5804C49.5395 40.9001 50.1746 39.9965 50.529 38.9727C50.8835 37.9489 50.9431 36.846 50.701 35.79C50.4589 34.7339 49.9248 33.7671 49.1597 33L43.3997 27.25C54.477 19.6842 67.8376 16.1994 81.1991 17.3911C94.5605 18.5828 107.094 24.377 116.657 33.7837C126.221 43.1905 132.221 55.6261 133.634 68.9661C135.046 82.306 131.783 95.7224 124.401 106.923C117.019 118.124 105.977 126.414 93.1617 130.378C80.3461 134.341 66.5519 133.731 54.1359 128.653C41.7198 123.574 31.4524 114.342 25.0876 102.534C18.7229 90.7253 16.6559 77.0733 19.2397 63.91C19.3049 63.5996 19.3003 63.2786 19.2263 62.9702C19.1522 62.6618 19.0105 62.3737 18.8114 62.1268C18.6123 61.8799 18.3609 61.6803 18.0753 61.5425C17.7896 61.4047 17.4768 61.3321 17.1597 61.33H4.07965C3.56598 61.328 3.06846 61.509 2.67626 61.8407C2.28407 62.1725 2.02292 62.6331 1.93969 63.14C-0.257822 76.7196 1.32584 90.642 6.51709 103.381C11.7083 116.12 20.3067 127.184 31.3697 135.36C58.3097 155.47 96.3697 154.78 122.59 133.81C130.867 127.227 137.662 118.972 142.53 109.583C147.398 100.194 150.229 89.8831 150.839 79.3249C151.449 68.7667 149.823 58.1986 146.068 48.3118C142.314 38.4249 136.514 29.4419 129.05 21.95Z"
        fill={color}
      />
      <path
        d="M76 27.82C75.0717 27.82 74.1815 28.1887 73.5251 28.8451C72.8687 29.5015 72.5 30.3917 72.5 31.32V75.11C72.5 75.18 72.5 75.24 72.5 75.31C72.5 75.38 72.5 75.55 72.5 75.68C72.5 75.81 72.5 75.89 72.57 76C72.64 76.11 72.64 76.22 72.68 76.33C72.7182 76.4402 72.765 76.5472 72.82 76.65C72.86 76.7484 72.9103 76.8422 72.97 76.93C73.0376 77.0373 73.111 77.1408 73.19 77.24L73.3 77.4L94.17 101.46C94.7787 102.162 95.6411 102.593 96.5675 102.658C97.4939 102.724 98.4085 102.419 99.11 101.81C99.8115 101.201 100.242 100.339 100.308 99.4125C100.374 98.486 100.069 97.5715 99.46 96.87L79.46 73.8V31.27C79.447 30.3573 79.078 29.4858 78.4316 28.8413C77.7853 28.1968 76.9127 27.8303 76 27.82Z"
        fill={color}
      />
    </svg>
  );
};

export default SAutoMode;
