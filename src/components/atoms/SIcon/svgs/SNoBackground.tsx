const SNoBackground = ({ size = 15, color = "#231F20" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox={`0 0 ${size} ${size}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.8694 4.33995L23.8694 2.10997C24.0484 1.90615 24.1415 1.64097 24.1294 1.36998C24.1114 1.09844 23.9896 0.844206 23.7893 0.659957C23.6906 0.567197 23.5742 0.495431 23.447 0.44902C23.3198 0.402608 23.1845 0.382498 23.0493 0.389938C22.9148 0.394357 22.7827 0.425886 22.6607 0.482589C22.5387 0.539292 22.4294 0.620019 22.3393 0.719955L20.3393 2.94994C17.8714 1.07088 14.7887 0.185099 11.6997 0.467269C8.61065 0.749439 5.73961 2.17907 3.65293 4.47417C1.56625 6.76927 0.415535 9.76311 0.427832 12.865C0.440129 15.9668 1.61452 18.9515 3.71934 21.23L1.71934 23.4599C1.62602 23.5609 1.55457 23.6801 1.50937 23.81C1.46024 23.9375 1.43639 24.0733 1.43931 24.2099C1.44766 24.3451 1.48157 24.4775 1.53928 24.6C1.59575 24.7253 1.67744 24.8376 1.77927 24.93C1.88222 25.0206 2.00092 25.0918 2.12937 25.1399C2.2589 25.1801 2.39367 25.2003 2.52927 25.1999C2.66542 25.1903 2.79816 25.1528 2.91929 25.09C3.04461 25.0335 3.15703 24.9518 3.24937 24.85L5.24937 22.62C7.71734 24.499 10.7999 25.3848 13.8889 25.1026C16.9779 24.8205 19.8491 23.3909 21.9358 21.0957C24.0224 18.8006 25.1732 15.8068 25.1609 12.7049C25.1486 9.60307 23.9742 6.61843 21.8694 4.33995ZM2.45933 12.78C2.46159 10.8664 2.99483 8.99112 3.99961 7.36265C5.00439 5.73419 6.44128 4.41644 8.15049 3.55614C9.85969 2.69583 11.7741 2.32669 13.6806 2.48979C15.5872 2.65289 17.4111 3.34185 18.9493 4.47996L5.12937 19.6899C3.40989 17.7995 2.45773 15.3354 2.45933 12.78ZM12.7993 23.12C10.5878 23.1301 8.43172 22.429 6.64927 21.12L20.4693 5.89995C21.8078 7.38336 22.6873 9.22307 23.0013 11.1962C23.3154 13.1694 23.0505 15.1912 22.2387 17.0169C21.427 18.8425 20.1032 20.3936 18.4278 21.4822C16.7524 22.5707 14.7973 23.15 12.7993 23.1499V23.12Z"
        fill={color}
      />
    </svg>
  );
};

export default SNoBackground;
