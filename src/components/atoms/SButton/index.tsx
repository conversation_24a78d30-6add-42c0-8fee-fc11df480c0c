import { cn } from "@/cores/helpers";
import React, { useMemo } from "react";
// @ts-ignore
import Ripple from "material-ripple-effects";
import { useTheme } from "@/hooks/useTheme";

type SButtonProps = {
  children: React.ReactNode;
  className?: string;
  noDefaultStyle?: boolean;
  effect?: "light" | "dark";
  secondary?: boolean;
  onClick?: () => void;
  disabled?: boolean;
  noBorder?: boolean;
  noBackground?: boolean;
  style?: React.CSSProperties;
  borderWidth?: number;
};

const SButton = ({
  children,
  className,
  noDefaultStyle,
  effect = "light",
  onClick,
  secondary = false,
  disabled = false,
  style = {},
  noBorder = false,
  noBackground = false,
  borderWidth = 4,
}: SButtonProps) => {
  const rippleEffect = new Ripple();
  const theme = useTheme();

  const borderStyle = useMemo(() => {
    if (noBorder) return {};

    return {
      borderColor: secondary ? theme.secondary_color : theme.primary_color,
      borderWidth,
    };
  }, [noBorder, theme, borderWidth]);

  const backgroundStyle = useMemo(() => {
    if (noBackground) return {};

    return {
      backgroundColor: secondary ? theme.secondary_color : theme.primary_color,
    };
  }, [noBackground, secondary, theme]);

  return (
    <button
      className={cn([
        noDefaultStyle
          ? ""
          : "px-4 py-3 shadow-none hover:shadow-none rounded-lg",
        disabled ? "opacity-50 cursor-not-allowed" : "",
        className,
      ])}
      style={
        noDefaultStyle
          ? style
          : {
              ...backgroundStyle,
              ...borderStyle,
            }
      }
      onClick={onClick}
      onMouseDown={(e) => {
        rippleEffect.create(e, effect);
      }}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default SButton;
