import mockFilterImg from "@/assets/capture/filter.jpeg";
import { returnFilterStyling } from "@/cores/helpers";
import { useTheme } from "@/hooks/useTheme";
import { FC } from "react";

type Props = {
  label: string;
  value: string;
  onFilterChange?: (value: string) => void;
  isActive?: boolean;
  isPrinting?: boolean;
};

const StickerItem: FC<Props> = ({ label, value, onFilterChange, isActive, isPrinting }) => {
  const theme = useTheme();

  return (
    <div className="flex flex-col gap-2 items-center">
      <button
        className={`rounded-full border-[4px] border-solid cursor-pointer`}
        style={{
          borderColor: isActive ? theme.primary_color : "transparent",
        }}
        disabled={isPrinting}
        onClick={() => onFilterChange?.(value)}
      >
        <div className="relative overflow-hidden">
          <img
            src={mockFilterImg}
            className="object-cover w-[84px] h-[84px] rounded-full"
            style={value === "mix" ? {} : returnFilterStyling(value)}
          />
          {value === "mix" && (
            <div className="bg-neutral-200 h-full w-1/2 absolute top-0 left-[42px] transform scale-x-[-1] mix-blend-color rounded-l-full" />
          )}
        </div>
      </button>
      <span className="text-xs font-medium">{label}</span>
    </div>
  );
};

export default StickerItem;
