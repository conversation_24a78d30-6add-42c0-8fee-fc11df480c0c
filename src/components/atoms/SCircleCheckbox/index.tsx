import { FC } from "react";
import SIcon from "../SIcon";
import SText from "../SText";

type Props = {
  label: string;
  description: string;
  isActive?: boolean;
  value: any;
  onChange?: (value: any) => void;
};

const SCircleCheckbox: FC<Props> = ({
  isActive,
  label,
  description,
  value,
  onChange,
}) => {
  return (
    <div
      className="flex justify-center items-center gap-3 cursor-pointer rounded-full w-[250px] h-[65px] border-2 border-primary_text_color"
      onClick={() => onChange?.(value)}
    >
      {isActive ? (
        <div className="w-[30px] h-[30px] rounded-full bg-primary_color flex justify-center items-center">
          <SIcon
            name="CheckIcon"
            weight="fill"
            color="primary_text_color"
            size={20}
          />
        </div>
      ) : (
        <div className="w-[30px] h-[30px] rounded-full border-2 border-primary_color" />
      )}
      <div className="flex flex-col gap-1">
        <SText type="main" className="text-[20px]">
          {label}
        </SText>
        <SText type="sub" className="text-[12px]">
          {description}
        </SText>
      </div>
    </div>
  );
};

export default SCircleCheckbox;
