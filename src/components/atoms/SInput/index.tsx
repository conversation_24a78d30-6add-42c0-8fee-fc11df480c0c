import { cn } from "@/cores/helpers";
import { useTheme } from "@/hooks/useTheme";
import { forwardRef, InputHTMLAttributes } from "react";

interface SInputProps extends InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  secondary?: boolean;
}

const SInput = forwardRef<HTMLInputElement, SInputProps>(
  ({ error, secondary, className, ...others }, ref) => {
    const theme = useTheme();

    const hexToRgba = (hex: string, opacity: number) => {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    };

    return (
      <div className="flex flex-col w-full gap-2">
        <input
          ref={ref}
          type="text"
          autoFocus
          style={{
            color: secondary ? theme.secondary_color : theme.primary_color,
            borderWidth: 2,
            borderColor: secondary
              ? theme.secondary_color
              : theme.primary_text_color,
            backgroundColor: secondary
              ? hexToRgba(theme.secondary_color, 0.3)
              : hexToRgba(theme.primary_color, 0.3),
            WebkitTextFillColor: theme.secondary_text_color,
          }}
          className={cn(
            `w-full h-[73px] rounded-lg p-4 text-2xl placeholder:opacity-40 placeholder:text-2xl placeholder:font-normal`,
            className
          )}
          {...others}
        />
        {error && <p className="text-red-500 text-sm">{error}</p>}
      </div>
    );
  }
);

export default SInput;
