import { cn } from "@/cores/helpers";
import { useState } from "react";

type Props = {
  src: string;
  className?: string;
  wrapperClassName?: string;
};

const SImage = ({ src, className, wrapperClassName }: Props) => {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <div className={cn("w-full h-full relative", wrapperClassName)}>
      {isLoading && (
        <div className="absolute top-0 left-0 w-full h-full bg-gray-200 flex items-center justify-center rounded-xl animate-pulse">
          <div className="w-4 h-4 border-2 border-green border-t-transparent rounded-full animate-spin" />
        </div>
      )}

      <img
        src={src}
        className={cn(["w-full h-full object-cover rounded-xl", className])}
        alt="snapbox"
        onLoad={() => setIsLoading(false)}
        loading="lazy"
        decoding="async"
      />
    </div>
  );
};

export default SImage;
