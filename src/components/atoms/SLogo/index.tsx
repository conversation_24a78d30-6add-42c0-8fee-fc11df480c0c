import { cn } from "@/cores/helpers";
import { useTheme } from "@/hooks/useTheme";

type SLogoProps = {
  className?: string;
};

const SLogo = ({ className }: SLogoProps) => {
  const theme = useTheme();

  if (!theme.logo) return <></>;

  return (
    <img
      src={theme.logo}
      alt="logo"
      className={cn(["w-full h-full object-contain", className])}
    />
  );
};

export default SLogo;
