import { cn } from "@/cores/helpers";
import { useTheme } from "@/hooks/useTheme";
import { useMemo } from "react";

type Props = {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  type?: "primary" | "secondary" | "main" | "sub" | "sub2";
};

const SText = ({ children, className, style, type = "primary" }: Props) => {
  const theme = useTheme();

  const textColor = useMemo(() => {
    if (type === "secondary") return theme.secondary_color;
    if (type === "main") return theme.primary_text_color;
    if (type === "sub") return theme.secondary_text_color;
    if (type === "sub2") return theme.secondary_text_color_2;
    return theme.primary_color;
  }, [type, theme]);

  return (
    <span
      style={{
        color: textColor,
        lineHeight: 1.2,
        ...style,
      }}
      className={className}
    >
      {children}
    </span>
  );
};

export default SText;
