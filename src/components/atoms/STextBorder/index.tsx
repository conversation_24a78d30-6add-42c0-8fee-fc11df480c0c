import { cn } from "@/cores/helpers";
import { HeartIcon } from "@heroicons/react/24/solid";
import React from "react";

interface STextBorderProps {
  children: React.ReactNode;
  className?: string;
}

const STextBorder = ({ children, className }: STextBorderProps) => {
  return (
    <div
      className={cn([
        "border-2 border-pink-100 rounded-[20px] p-4 relative",
        className,
      ])}
    >
      <HeartIcon className="absolute -top-[20px] right-[1/2] size-10 text-pink-100" />

      {children}
    </div>
  );
};

export default STextBorder;
