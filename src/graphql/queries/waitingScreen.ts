import { gql } from "@apollo/client";

export const CLIENT_APP_WAITING_SCREEN = gql`
  query clientAppWaitingScreen {
    clientAppGetAppearanceSetting {
      id
      logo
      havePayScreen
      background
      primary_color
      secondary_color
      background_color
      primary_text_color
      secondary_text_color
      secondary_text_color_2
    }

    clientAppGetBackgroundImages {
      images {
        id
        fileName
        fileUrl
      }
    }

    clientAppWaitingScreen {
      id
      images {
        id
        url
      }
    }

    clientAppGetLayouts {
      id
      name
      clientId
      status
      formats {
        id
        name
        imageCount
        status
        layoutItems {
          id
          imageUrl
          topicId
          position
          topic {
            id
            name
            extraFee
            isActive
          }
        }
      }
    }
    clientAppGetSettingSizes {
      id
      settingSizeType
      smallSizePrice2
      smallSizePrice4
      smallSizePrice6
      smallSizePrice8
      smallSizePrice10
      largeSizePrice2
      largeSizePrice3
      largeSizePrice4
      largeSizePrice5
      largeSizePrice6
    }

    clientAppGetFrames {
      id
      clientId
      description
      imageUrl
      frameSize
      numberImage
      numberPicture
      topicId
      orientation
      dateColor
      frameItems {
        id
        frameId
        itemType
        itemId
        parentId
        x_coordinate
        y_coordinate
        position
        width
        height
        angle
      }
    }

    clientAppGetStickers {
      id
      clientId
      image
    }

    clientAppPrintSetting {
      id
      adjustColor
      printRetry
      border
      sharpness
      overcoatFinish
      printerQuality
      yResolution
      adjGammaR
      adjGammaG
      adjGammaB
      adjBrightnessR
      adjBrightnessG
      adjBrightnessB
      adjContrastR
      adjContrastG
      adjContrastB
      adjChroma
      icmMethod
    }
  }
`;
