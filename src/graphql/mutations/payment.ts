import { gql } from "@apollo/client";

export const CLIENT_APP_ORDER_PAYMENT_ONLINE = gql`
  mutation clientAppCreateOrderPayOnline(
    $promotionCode: String
    $settingSizeId: String!
    $topicId: String!
    $settingSizeKey: String!
    $frameId: String!
  ) {
    clientAppCreateOrderPayOnline(
      input: {
        promotionCode: $promotionCode
        settingSizeId: $settingSizeId
        topicId: $topicId
        settingSizeKey: $settingSizeKey
        frameId: $frameId
      }
    ) {
      qrCode
      qrCodeUrl
      skipPayment
      refCode
    }
  }
`;

export const CLIENT_APP_ORDER_PAYMENT_CASH = gql`
  mutation clientAppCreateOrderPayWithCash(
    $settingSizeId: String!
    $settingSizeKey: String!
    $frameId: String!
    $topicId: String!
    $receivedAmount: Float!
    $promotionCode: String
    $denominations: String
  ) {
    clientAppCreateOrderPayWithCash(
      input: {
        settingSizeId: $settingSizeId
        settingSizeKey: $settingSizeKey
        frameId: $frameId
        topicId: $topicId
        receivedAmount: $receivedAmount
        promotionCode: $promotionCode
        denominations: $denominations
      }
    ) {
      orderId
      domain
    }
  }
`;
