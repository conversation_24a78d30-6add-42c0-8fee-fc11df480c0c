import { gql } from "@apollo/client";

export const CLIENT_APP_UPDATE_ORDER = gql`
  mutation clientAppCreateImage($input: CreateImageInput!, $file: Upload!) {
    clientAppCreateImage(input: $input, file: $file) {
      message
      captureMode
      domain
    }
  }
`;

export const CLIENT_APP_FINISH_PRINT = gql`
  mutation {
    clientAppPrintImage {
      message
    }
  }
`;

export const CLIENT_APP_UPDATE_TIMELAPSE_VIDEO = gql`
  mutation clientAppCreateTimeLapse(
    $input: CreateImageInput!
    $file: Upload!
  ) {
    clientAppCreateTimeLapse(input: $input, file: $file) {
      isSuccess
    }
  }
`;
