import SLogo from "@/components/atoms/SLogo";
import SText from "@/components/atoms/SText";
import Layout from "@/components/templates/Layout";
const CountdownSuccess = () => {
  return (
    <Layout showLogo={false}>
      <div className="flex w-full h-full flex-col justify-center items-center">
        <div className="px-10 pt-[122px] pb-[83px] bg-primary_color mt-10 gap-3 rounded-2xl flex flex-col items-center justify-center">
          <SText type="main" className="text-[40px] font-bold text-center">
            ĐANG TRONG QUÁ TRÌNH CHỤP ẢNH BẰNG CAMERA PHÍA TRÊN
          </SText>
          <SText type="main" className="text-[40px] font-bold">
            VUI LÒNG KHÔNG CHẠM VÀO MÀN HÌNH
          </SText>
          <SText type="sub" className="text-[24px]">
            Please look at the camera above. Shooting starts in 10 secs
          </SText>
          <SText type="sub" className="text-[24px]">
            Please do not touch the screen
          </SText>
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="w-[250px] h-[110px]">
          <SLogo />
        </div>
      </div>
    </Layout>
  );
};

export default CountdownSuccess;
