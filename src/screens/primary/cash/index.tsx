import CashIcon from "@/assets/cash.png";
import SText from "@/components/atoms/SText";
import SIconButton from "@/components/molecules/SIconButton";
import Layout from "@/components/templates/Layout";
import { isMultipleCamera, isOverheadMachine } from "@/cores/constants";
import { ECaptureMode } from "@/cores/enums";
import { ROUTES, SECONDARY_ROUTES } from "@/cores/routes";
import { CLIENT_APP_ORDER_PAYMENT_CASH } from "@/graphql/mutations/payment";
import { useOrder } from "@/hooks/useOrder";
import useSMutation from "@/hooks/useSMutation";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { setCaptureMode } from "@/redux/slices/orderSlice";
import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";

const Cash = () => {
  const navigate = useNavigate();
  const {
    draftOrder,
    frame,
    layoutItem,
    promotionCode,
    setDraftOrder,
    setPromotionCode,
  } = useOrder();
  const denominationRef = useRef<any>({});
  const [receivedPrice, setReceivedPrice] = useState(0);
  const { data } = useWaitingScreen();

  const { cashMutate } = useSMutation({
    gql: CLIENT_APP_ORDER_PAYMENT_CASH,
    options: {
      key: "cash",
      isLoadingAuto: false,
      onCompleted: (data) => {
        if (data?.clientAppCreateOrderPayWithCash?.orderId) {
          window.api.onRegisterCaptureWorkspace(
            `orders/${data?.clientAppCreateOrderPayWithCash?.orderId}`
          );

          setDraftOrder({
            id: data?.clientAppCreateOrderPayWithCash?.orderId,
            domain: data?.clientAppCreateOrderPayWithCash?.domain,
          });

          if (isOverheadMachine) {
            setCaptureMode(ECaptureMode.AUTO);
            window.api.secondaryNavigate(SECONDARY_ROUTES.COUNT_DOWN);
            navigate(ROUTES.COUNT_DOWN);

            return;
          }

          navigate(ROUTES.CAPTURE_MODE);
        }
      },
      onError: (error) => console.log(error),
    },
  });

  const settingSize = useMemo(
    () => data?.clientAppGetSettingSizes?.[0] ?? {},
    [data]
  );

  const isReady = useMemo(() => {
    const targetPrice = draftOrder?.finalPrice ?? 0;
    return (
      receivedPrice >= targetPrice &&
      layoutItem?.topicId &&
      frame?.id &&
      settingSize.id &&
      draftOrder?.key
    );
  }, [receivedPrice, draftOrder, layoutItem, frame, settingSize]);

  useEffect(() => {
    if (isReady) {
      const body = {
        receivedAmount: receivedPrice,
        topicId: layoutItem?.topicId,
        settingSizeId: settingSize.id,
        settingSizeKey: draftOrder?.key,
        frameId: frame?.id,
        promotionCode,
        denominations: JSON.stringify(denominationRef.current ?? {}),
      };

      if (promotionCode?.length >= 8) {
        body.promotionCode = promotionCode;
      }

      cashMutate({
        variables: body,
      });
    }
  }, [isReady]);

  useEffect(() => {
    const billHandler = (_event: any, bill: any) => {
      if (bill > 0) {
        console.log("[billHandler]------------------->", bill);

        denominationRef.current[bill] =
          (denominationRef.current[bill] ?? 0) + 1;

        setReceivedPrice((prev) => prev + bill);
      }
    };

    const unsubscribe = window.api.startBillingPolling(billHandler);

    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <Layout
      showLogo={false}
      title="VUI LÒNG THANH TOÁN"
      description="Please settle the payment"
    >
      <div className="flex w-full h-full flex-col justify-center items-center">
        <div className="w-[1200px] h-[300px] bg-primary_color mt-10 gap-3 rounded-2xl flex flex-col items-center justify-center">
          <SText type="main" className="text-[40px] font-bold">
            Hệ thống không trả lại tiền thừa
          </SText>
          <SText type="main" className="text-[40px] font-bold">
            Hãy liên hệ nhân viên nếu cần đổi trả
          </SText>
          <SText type="sub" className="text-[24px]">
            The system does not return excess money. Please ask staff for
            changes if needed.
          </SText>
        </div>

        <div className="flex flex-row w-[1200px] gap-[85px] mt-[53px]">
          <div className="w-[381px] h-[381px]">
            <img src={CashIcon} alt="cash" />
          </div>

          <div className="flex flex-col flex-1 p-10 gap-2 rounded-2xl items-start border-2 border-secondary_color justify-between">
            <div className="flex items-center justify-between w-full">
              <div className="flex flex-col gap-1">
                <SText type="main" className="text-[50px] font-medium">
                  Giá tiền
                </SText>

                <SText type="sub" className="text-[24px]">
                  Payment amount
                </SText>
              </div>

              <SText type="main" className="font-bold text-[80px]">
                {draftOrder?.finalPrice?.toLocaleString("vi-VN", {
                  style: "currency",
                  currency: "VND",
                })}
              </SText>
            </div>
            <div className="w-full h-[2px] bg-secondary_color" />
            <div className="flex items-center justify-between w-full">
              <div className="flex flex-col gap-1">
                <SText type="main" className="text-[50px] font-medium">
                  Đã trả
                </SText>

                <SText type="sub" className="text-[24px]">
                  Paid
                </SText>
              </div>

              <SText type="main" className="text-[80px] font-bold text-black">
                {receivedPrice.toLocaleString("vi-VN", {
                  style: "currency",
                  currency: "VND",
                })}
              </SText>
            </div>
          </div>
        </div>

        <div className="flex flex-1 flex-col justify-end mt-[38px]">
          <SIconButton
            LeftIcon={"SArrowLeft"}
            text="Back"
            left={{
              color: "primary_text_color",
              size: 27,
            }}
            className="flex w-[250px] h-[65px] flex-row items-center justify-center"
            onClick={() => {
              setPromotionCode(null);
              navigate(-1);
            }}
          />
        </div>
      </div>
    </Layout>
  );
};

export default Cash;
