import GuideStep from "@/components/molecules/GuideStep";
import { ROUTES } from "@/cores/routes";
import { CLIENT_APP_FINISH_PRINT } from "@/graphql/mutations/order";
import { CLIENT_APP_PROFILE } from "@/graphql/queries";
import { useApp } from "@/hooks/useApp";
import { useAuth } from "@/hooks/useAuth";
import { useOrder } from "@/hooks/useOrder";
import useRequestPrint from "@/hooks/useRequestPrint";
import useSMutation from "@/hooks/useSMutation";
import useSNavigate from "@/hooks/useSNavigate";
import useSQuery from "@/hooks/useSQuery";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { useEffect, useMemo } from "react";

const Home = () => {
  const navigate = useSNavigate();
  const { print } = useRequestPrint({
    callback: () => {
      finishPrintMutate();
      setRemotePrintJob(null);
    },
  });
  const { reset } = useOrder();
  const { accessToken } = useAuth();
  const {
    waitForSync,
    remotePrintJob,
    setRemotePrintJob,
    snapbox,
    setSnapbox,
  } = useApp();
  const { data } = useWaitingScreen();

  const { profileMutate } = useSQuery({
    gql: CLIENT_APP_PROFILE,
    options: {
      key: "profile",
      isLoadingAuto: false,
      onCompleted: (data) => {
        if (data?.clientAppGetProfile) {
          setSnapbox(data?.clientAppGetProfile);
        }
      },
      onError: (_error) => {
        console.log("_error", _error);
      },
    },
  });

  const { finishPrintMutate } = useSMutation({
    gql: CLIENT_APP_FINISH_PRINT,
    options: {
      key: "finishPrint",
      onCompleted: () => {
        console.log("finishPrintMutate");
      },
    },
  });

  const slides = useMemo(() => {
    return data?.clientAppWaitingScreen?.images || [];
  }, [data?.clientAppWaitingScreen]);

  useEffect(() => {
    if (waitForSync) {
      navigate({
        primary: ROUTES.SYNC_DATA,
        at: "primary",
      });
    }
  }, [waitForSync]);

  useEffect(() => {
    if (remotePrintJob?.imageUrl) {
      const quantity = remotePrintJob?.numberPrint || 1;
      const cutable = remotePrintJob?.size?.toLowerCase() === "small";
      let printerQuantity = cutable ? quantity / 2 : quantity;

      if (printerQuantity < 1) {
        printerQuantity = 1;
      }

      print({
        imgUrl: remotePrintJob.imageUrl,
        options: {
          quantity: printerQuantity,
          cutable: cutable,
        },
      });
    }
  }, [remotePrintJob]);

  useEffect(() => {
    if (!snapbox?.machine?.id && accessToken) {
      profileMutate();
    }
  }, [accessToken]);

  return (
    <GuideStep
      slides={slides}
      onClick={async () => {
        try {
          reset();
          await window.api.setupCamera();

          navigate({
            primary: ROUTES.TOPICS,
            at: "primary",
          });
        } catch (error) {
          console.log(error);
        }
      }}
    />
  );
};

export default Home;
