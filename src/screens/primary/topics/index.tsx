import STopic from "@/components/molecules/STopic";
import STopicThumb from "@/components/molecules/STopicThumb";
import Layout from "@/components/templates/Layout";
import { cn } from "@/cores/helpers";
import { ROUTES } from "@/cores/routes";
import { useOrder } from "@/hooks/useOrder";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";

const Topics = () => {
  const navigate = useNavigate();
  const { setLayoutItem, setFrame } = useOrder();

  const handleSetLayoutItem = (layoutItem: any) => {
    setLayoutItem(layoutItem);
    setFrame(null);
    navigate(ROUTES.SIZE_SELECTION.replace(":id", layoutItem.id.toString()));
  };

  const { data } = useWaitingScreen();
  const formats = useMemo(
    () => data?.clientAppGetLayouts?.[0]?.formats?.[0]?.layoutItems ?? [],
    [data]
  );

  const renderLayout = (format: any) => {
    const sortedItems = Array.isArray(format)
      ? [...format].sort((a: any, b: any) => a.position - b.position)
      : [];
    const itemCount = sortedItems.length;

    switch (itemCount) {
      case 1:
        return (
          <STopicThumb
            item={sortedItems[0]}
            className="w-full h-full"
            onClick={handleSetLayoutItem}
          />
        );
      case 2:
        return (
          <div className="flex flex-1 flex-col gap-[48px]">
            {sortedItems.map((item: any) => (
              <STopicThumb
                key={item.id}
                item={item}
                className="max-h-[calc(100vh/2-1rem)]"
                onClick={handleSetLayoutItem}
              />
            ))}
          </div>
        );
      case 3:
        return (
          <STopic
            items={sortedItems}
            onClick={handleSetLayoutItem}
            className="h-full"
          />
        );
      case 4:
        return (
          <div className="grid grid-cols-2 flex-1 gap-[48px]">
            <div className="col-span-1 flex flex-col gap-[48px]">
              {sortedItems.slice(0, 2).map((item: any) => (
                <STopicThumb
                  key={item.id}
                  item={item}
                  onClick={handleSetLayoutItem}
                  className={cn(
                    "w-[888px] flex",
                    item.position === 1 && "h-[516px]",
                    item.position === 2 && "h-[420px]"
                  )}
                />
              ))}
            </div>
            <div className="col-span-1 flex flex-col flex-1 gap-[48px]">
              {sortedItems.slice(2, 4).map((item: any) => (
                <STopicThumb
                  key={item.id}
                  item={item}
                  onClick={handleSetLayoutItem}
                  className={cn(
                    "w-[888px] flex",
                    item.position === 4 && "h-[420px]",
                    item.position === 3 && "h-[516px]"
                  )}
                />
              ))}
            </div>
          </div>
        );
      case 5:
        return (
          <div className="flex flex-col flex-1 gap-[48px] h-[236px]">
            <STopic
              items={sortedItems.slice(0, 2)}
              onClick={handleSetLayoutItem}
            />
            <STopic
              items={sortedItems.slice(2, 5)}
              onClick={handleSetLayoutItem}
            />
          </div>
        );
      case 6:
        return (
          <div className="flex flex-col flex-1 gap-[48px] h-[700px]">
            <STopic
              items={sortedItems.slice(0, 2)}
              className="flex-1"
              onClick={handleSetLayoutItem}
            />
            <STopic
              items={sortedItems.slice(2, 6)}
              className="flex-1"
              onClick={handleSetLayoutItem}
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Layout
      className="flex flex-col p-12 justify-center w-screen h-screen"
      showLogo={false}
    >
      {renderLayout(formats)}
    </Layout>
  );
};

export default Topics;
