import Layout from "@/components/templates/Layout";
import SIconButton from "@/components/molecules/SIconButton";
import { TSyncStep } from "@/cores/types";
import SText from "@/components/atoms/SText";

interface SyncDataViewProps {
  steps: TSyncStep[];
  currentStep: number;
  errorStep: number | null;
  handleRetry: () => void;
}

export const SyncDataView = ({
  steps,
  currentStep,
  errorStep,
  handleRetry,
}: SyncDataViewProps) => {
  return (
    <Layout title="ĐỒNG BỘ DỮ LIỆU" description="Sync data from Snap Box">
      <div className="flex items-center justify-center flex-1">
        <ol className="flex items-center justify-center w-[60%]">
          {steps.map((step, index) => (
            <li
              key={step.id}
              className={`flex flex-col items-center ${
                index !== steps.length - 1
                  ? "flex-1 relative before:content-[''] before:w-[calc(100%-3rem)] before:h-1 before:border-b before:border-4 before:absolute before:top-6 before:left-[calc(50%+1.5rem)] " +
                    (step.id < currentStep
                      ? "before:border-[#3FC66826] dark:before:border-[#3FC66826]"
                      : "before:border-gray-100 dark:before:border-gray-700")
                  : "flex-1"
              }`}
            >
              <span
                className={`flex items-center justify-center w-10 h-10 rounded-full lg:h-12 lg:w-12 shrink-0 relative z-[1] ${
                  errorStep === step.id
                    ? "bg-[#FF4E4E26]"
                    : step.id < currentStep
                      ? "bg-[#3FC66826]"
                      : step.id === currentStep
                        ? "bg-[#3FC66826]"
                        : "bg-gray-100 dark:bg-gray-700"
                }`}
              >
                {errorStep === step.id ? (
                  <svg
                    className="w-3.5 h-3.5 text-[#FF4E4E]"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 14 14"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M1 1l12 12m0-12L1 13"
                    />
                  </svg>
                ) : step.id < currentStep ? (
                  <svg
                    className="w-3.5 h-3.5 text-[#3FC668]"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 16 12"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M1 5.917 5.724 10.5 15 1.5"
                    />
                  </svg>
                ) : step.id === currentStep ? (
                  <div className="w-4 h-4 border-2 border-[#3FC668] border-t-transparent rounded-full animate-spin"></div>
                ) : null}
              </span>
              <div className="mt-3 text-center">
                <SText type="sub" className="text-sm">
                  {step.description}
                </SText>
              </div>
            </li>
          ))}
        </ol>
      </div>

      {errorStep && (
        <div className="flex items-center justify-center flex-1">
          <SIconButton
            onClick={handleRetry}
            text="Thử Lại"
            className="border-primary w-[250px] w-[65px] bg-primary px-4"
          />
        </div>
      )}
    </Layout>
  );
};
