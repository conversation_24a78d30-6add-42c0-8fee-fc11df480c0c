import { SYNC_STEPS } from "@/cores/constants";
import { EMachineStatus } from "@/cores/enums";
import { ROUTES } from "@/cores/routes";
import { CLIENT_APP_UPDATE_MACHINE_STATUS } from "@/graphql/mutations";
import { useApp } from "@/hooks/useApp";
import useSMutation from "@/hooks/useSMutation";
// import { useSyncDataProcess } from "@/hooks/useSyncDataProcess";
import { useSyncDataState } from "@/hooks/useSyncDataState";
import { useSyncDataSubscriptions } from "@/hooks/useSyncDataSubscription";
import { useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { SyncDataView } from "./view";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";

const SyncData = () => {
  const { setWaitForSync } = useApp();
  const navigate = useNavigate();
  const { setData, setBackupData } = useWaitingScreen();
  const {
    currentStep,
    setCurrentStep,
    errorStep,
    // setErrorStep,
    syncCompleted,
  } = useSyncDataState();

  const handleSyncCompleted = useCallback(() => {
    syncCompleted();
    setWaitForSync(false);
    navigate(ROUTES.HOME);
  }, [syncCompleted, navigate]);

  const handleProcessDataFinished = useCallback((apiData: any) => {
    setData(apiData);
    setBackupData(null);

    updateSyncStatusMutate({
      variables: {
        status: EMachineStatus.COMPLETED,
      },
    });
  }, []);

  const { updateSyncStatusMutate } = useSMutation({
    gql: CLIENT_APP_UPDATE_MACHINE_STATUS,
    options: {
      key: "updateSyncStatus",
      onCompleted: handleSyncCompleted,
    },
  });

  // const { processData } = useSyncDataProcess({
  //   currentStep,
  //   setCurrentStep,
  //   setErrorStep,
  //   errorStep,
  //   onFinished: handleProcessDataFinished,
  // });

  const { dummyMutate } = useSyncDataSubscriptions({
    processData: (data) => {
      handleProcessDataFinished(data);
      
      setTimeout(() => {
        setCurrentStep(9);
      }, 5000);
    },
  });

  const handleRetry = () => {
    if (errorStep) {
      setCurrentStep(errorStep);
      
      if (errorStep === 1) {
        dummyMutate();
        return;
      }
    }
  };

  useEffect(() => {
    dummyMutate();
  }, []);

  return (
    <SyncDataView
      steps={SYNC_STEPS}
      currentStep={currentStep}
      errorStep={errorStep}
      handleRetry={handleRetry}
    />
  );
};

export default SyncData;
