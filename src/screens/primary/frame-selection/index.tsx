import { useNavigate } from "react-router-dom";

import SCarousel from "@/components/molecules/SCarousel";
import SNavigatorLayout from "@/components/templates/SNavigatorLayout";
import { ROUTES } from "@/cores/routes";
import { useOrder } from "@/hooks/useOrder";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { useMemo, useState } from "react";

const TotalSelection = () => {
  const navigate = useNavigate();
  const [currentIndex, setCurrentIndex] = useState(0);
  const { setFrame, size, layoutItem } = useOrder();
  const { data } = useWaitingScreen();

  const slides = useMemo(() => {
    return (
      data?.clientAppGetFrames
        ?.filter(
          (frm: any) =>
            frm.frameSize?.toLowerCase() === size?.toLowerCase() &&
            frm.topicId === layoutItem?.topic?.id
        )
        .map((frm: any) => ({
          ...frm,
          price: frm.price,
        })) ?? []
    );
  }, [data?.clientAppGetFrames, size, layoutItem]);

  const handleGoBack = () => navigate(ROUTES.SIZE_SELECTION);
  const handleSubmit = () => {
    const cfr = slides[currentIndex];
    setFrame({
      ...cfr,
      frameItems: [...cfr.frameItems].sort(
        (a: any, b: any) => a.position - b.position
      ),
    });

    navigate(ROUTES.TOTAL_SELECTION);
  };

  return (
    <SNavigatorLayout
      title="CHỌN THIẾT KẾ FRAME"
      description="Please choose the frame design"
      onGoBack={handleGoBack}
      onSubmit={handleSubmit}
      className="justify-center gap-[74px]"
    >
      <SCarousel
        items={slides}
        maxVisibleItems={3}
        onSelect={(_: any, index: number) => {
          setCurrentIndex(index);
        }}
        className="rounded-xl p-4"
        itemClassName="rounded-2xl"
        defaultIndex={currentIndex}
      />
    </SNavigatorLayout>
  );
};

export default TotalSelection;
