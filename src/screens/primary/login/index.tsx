import { SInput } from "@/components/atoms";
import SText from "@/components/atoms/SText";
import SIconButton from "@/components/molecules/SIconButton";
import { CLIENT_APP_SIGN_IN_BY_CODE } from "@/graphql/mutations";
import { useAuth } from "@/hooks/useAuth";
import useSMutation from "@/hooks/useSMutation";
import { useTheme } from "@/hooks/useTheme";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import type { ObjectSchema } from "yup";
import * as yup from "yup";

interface LoginFormInputs {
  machineCode?: string;
  machinePin?: string;
}

const schema: ObjectSchema<LoginFormInputs> = yup.object().shape({
  machineCode: yup.string().required("Vui lòng nhập ID máy"),
  machinePin: yup.string().required("Vui lòng nhập mã PIN"),
});

const ENVIRONMENT_ACCOUNT = {
  production: {
    machineCode: "",
    machinePin: "",
  },
  development: {
    machineCode: "SNAPBOX1741777947048",
    machinePin: "4406",
  },
};

const env = process.env.NODE_ENV as keyof typeof ENVIRONMENT_ACCOUNT;
const DEFAULT_ACCOUNT = ENVIRONMENT_ACCOUNT[env];

const Login = () => {
  const { setLoggedIn } = useAuth();
  const theme = useTheme();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormInputs>({
    /* @ts-ignore */
    resolver: yupResolver(schema),
    defaultValues: {
      machineCode: DEFAULT_ACCOUNT.machineCode,
      machinePin: DEFAULT_ACCOUNT.machinePin,
    },
  });

  const { loginMutate, loginLoading } = useSMutation({
    gql: CLIENT_APP_SIGN_IN_BY_CODE,
    options: {
      key: "login",
      isLoadingAuto: false,
      onCompleted: (data) => {
        if (data?.clientAppSignInByCode?.id) {
          setLoggedIn({
            isLoggedIn: true,
            id: data?.clientAppSignInByCode?.id,
            machineId: data?.clientAppSignInByCode?.machineId,
            accessToken: data?.clientAppSignInByCode?.token,
          });
        }
      },
    },
  });

  const onSubmit = async (data: LoginFormInputs) => {
    loginMutate({
      variables: {
        machineCode: data.machineCode,
        machinePin: data.machinePin,
      },
    });
  };

  return (
    <div className="bg-white w-screen h-screen flex flex-col justify-between items-center py-10">
      <div className="flex flex-col justify-center items-center gap-8">
        <div className="w-[250px] h-[100px]">
          <img src={theme.logo} alt="logo" className="w-full h-full" />
        </div>
        <div className="flex flex-col items-center">
          <SText className="text-[40px]">CẤU HÌNH MÁY</SText>
          <SText type="sub" className="text-2xl text-center">
            Hãy nhập ID máy và mã PIN Snap Box Việt Nam <br /> cung cấp để đăng
            nhập
          </SText>
        </div>
      </div>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex-1 flex flex-col justify-between items-center mt-20"
      >
        <div className="flex flex-col gap-4">
          <SInput
            {...register("machineCode")}
            placeholder="ID Máy"
            error={errors.machineCode?.message}
          />
          <SInput
            {...register("machinePin")}
            placeholder="Mã PIN"
            error={errors.machinePin?.message}
            type="password"
          />
        </div>
        <SIconButton
          type="submit"
          RightIcon={"ArrowRightCircleIcon"}
          text="TIẾP TỤC"
          disabled={loginLoading}
          isLoading={loginLoading}
        />
      </form>
    </div>
  );
};

export default Login;
