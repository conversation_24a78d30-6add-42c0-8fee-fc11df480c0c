import { SButton } from "@/components/atoms";
import SIcon from "@/components/atoms/SIcon";
import SText from "@/components/atoms/SText";
import Layout from "@/components/templates/Layout";
import { isOverheadMachine } from "@/cores/constants";
import { ROUTES, SECONDARY_ROUTES } from "@/cores/routes";
import { useOrder } from "@/hooks/useOrder";
import QRCode from "react-qr-code";
import { useNavigate } from "react-router-dom";
import React, { useEffect } from "react";

const FinalResult = () => {
  const { draftOrder, reset } = useOrder();
  const navigate = useNavigate();
  const [videoSrc, setVideoSrc] = React.useState<string | null>(null);

  const getVideoUrl = (path: string) => {
    // Try original path first
    const originalUrl = `video:///${path}`;
    // Try with forward slashes
    const forwardSlashUrl = `video:///${path.replace(/\\/g, "/")}`;
    return { originalUrl, forwardSlashUrl };
  };

  useEffect(() => {
    if (draftOrder?.timelapseVideoUrl) {
      const { originalUrl, forwardSlashUrl } = getVideoUrl(
        draftOrder.timelapseVideoUrl
      );
      setVideoSrc(originalUrl);
    }
  }, [draftOrder?.timelapseVideoUrl]);

  const imgUrl = draftOrder?.imgUrl
    ? `app:///${encodeURIComponent(draftOrder?.imgUrl)}`
    : null;

  return (
    <Layout
      title="QUÉT MÃ QR ĐỂ TẢI ẢNH & VIDEO"
      description="Scan QR code to download photos & videos"
      showLogo={false}
    >
      <div className="flex w-full h-full flex-col justify-center items-center">
        <div className="justify-between mt-10 items-center gap-[60px] grid grid-cols-3">
          <div className="flex flex-col w-[400px] h-[400px] justify-center items-center bg-primary_color rounded-2xl p-[35px] gap-4">
            <img
              src={imgUrl}
              className="w-full h-full max-h-[250px] object-contain"
            />

            <div className="flex-col flex justify-center items-center gap-2">
              <SText type="main" className="font-bold text-[40px]">
                Tệp ảnh
              </SText>

              <SText type="sub" className="text-[24px]">
                Photo file
              </SText>
            </div>
          </div>

          <div className="flex flex-col w-[400px] h-[400px] justify-between items-center bg-primary_color rounded-2xl p-[35px] gap-4 relative">
            {!videoSrc ? (
              <div className="h-[250px] w-full inset-0 bg-black opacity-50 flex justify-center items-center rounded-2xl">
                <div className="w-4 h-4 border-2 border-[#3FC668] border-t-transparent rounded-full animate-spin" />
              </div>
            ) : (
              <div className="max-h-[250px] w-full">
                <video
                  key={videoSrc}
                  className="flex-1"
                  playsInline
                  autoPlay
                  muted
                  loop
                  onError={(e) => {
                    console.error("Video loading error:", e);
                    const target = e.target as
                      | HTMLVideoElement
                      | HTMLSourceElement;
                    if (target instanceof HTMLSourceElement) {
                      const { originalUrl, forwardSlashUrl } = getVideoUrl(
                        draftOrder?.timelapseVideoUrl || ""
                      );
                      // If current URL is original, try forward slash version
                      if (videoSrc === originalUrl) {
                        console.log("Trying forward slash version");
                        setVideoSrc(forwardSlashUrl);
                      } else if (videoSrc === forwardSlashUrl) {
                        console.log("Trying original version");
                        setVideoSrc(originalUrl);
                      }
                    }
                  }}
                  onLoadStart={() => {
                    console.log("Video loading started");
                  }}
                  onLoadedData={() => {
                    console.log("Video data loaded successfully");
                  }}
                >
                  <source src={videoSrc} type="video/webm" />
                </video>
              </div>
            )}

            <div className="flex-col flex justify-center items-center gap-2">
              <SText type="main" className="font-bold text-[40px]">
                Tệp video
              </SText>

              <SText type="sub" className="text-[24px]">
                Video file
              </SText>
            </div>
          </div>

          <div className="flex flex-col w-[400px] h-[400px] justify-between items-center bg-primary_color rounded-2xl p-[35px] gap-4">
            {!draftOrder?.domain ? (
              <div className="max-h-[250px] bg-primary opacity-50 flex justify-center items-center">
                <div className="w-4 h-4 border-2 border-[#3FC668] border-t-transparent rounded-full animate-spin" />
              </div>
            ) : (
              <div className="w-full h-full max-h-[250px] flex justify-center items-center">
                <QRCode size={256} value={draftOrder?.domain} />
              </div>
            )}

            <div className="flex-col flex justify-center items-center gap-2">
              <SText type="main" className="font-bold text-[40px]">
                Quét mã QR
              </SText>

              <SText type="sub" className="text-[24px]">
                Scan QR Code
              </SText>
            </div>
          </div>
        </div>

        <div className="flex flex-col justify-center items-center w-full mt-20 gap-2">
          <SText type="main" className="font-bold text-[40px] text-center">
            Bạn có thể tải ảnh và video trong vòng 72 tiếng bằng cách quét mã
            QR. <br /> Sau thời gian này ảnh sẽ bị tự động xoá.
          </SText>

          <SText type="sub" className="text-[24px]">
            QR code photos and video can be downloaded within 72 hours and will
            be deleted automatically.
          </SText>

          <SButton
            noDefaultStyle
            onClick={async () => {
              await window.api.disconnectRelay();
              reset();
              navigate(ROUTES.HOME);

              if (isOverheadMachine) {
                window.api.secondaryNavigate(SECONDARY_ROUTES.HOME);
              }
            }}
            className="w-[250px] h-[65px] bg-primary_color rounded-full mt-4 flex items-center justify-center gap-4"
          >
            <SText type="main" className="font-bold text-[30px]">
              Home
            </SText>

            <SIcon name="SReturn" color="primary_text_color" size={28} />
          </SButton>
        </div>
      </div>
    </Layout>
  );
};

export default FinalResult;
