import { SButton } from "@/components/atoms";
import SIcon from "@/components/atoms/SIcon";
import SText from "@/components/atoms/SText";
import Layout from "@/components/templates/Layout";
import { isOverheadMachine, isMultipleCamera } from "@/cores/constants";
import { ECaptureMode } from "@/cores/enums";
import { ROUTES, SECONDARY_ROUTES } from "@/cores/routes";
import { useOrder } from "@/hooks/useOrder";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const CaptureMode = () => {
  const navigate = useNavigate();
  const { setCaptureMode } = useOrder();
  const [countdown, setCountdown] = useState(30);

  const handleCaptureMode = async (captureMode: ECaptureMode) => {
    setCaptureMode(captureMode);

    if (isOverheadMachine) {
      window.api.secondaryNavigate(SECONDARY_ROUTES.COUNT_DOWN);
      navigate(ROUTES.COUNT_DOWN);

      return;
    }

    if (isMultipleCamera) {
      window.api.secondaryNavigate(SECONDARY_ROUTES.CAPTURE);
    }

    navigate(ROUTES.CAPTURE);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setCountdown(countdown - 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [countdown]);

  useEffect(() => {
    if (countdown === 0) {
      handleCaptureMode(ECaptureMode.AUTO);
    }
  }, [countdown]);

  return (
    <Layout
      showLogo={false}
      title="VUI LÒNG CHỌN CHẾ ĐỘ CHỤP"
      description="Please choose the number of printed photos"
    >
      <SText
        type="main"
        className="text-[46px] font-bold absolute top-10 right-10"
      >
        {countdown}
      </SText>

      <div className="w-full flex-1 flex flex-col items-center justify-center relative">
        <div className="w-full flex flex-1 justify-center items-center gap-[61px]">
          <SButton
            onClick={() => handleCaptureMode(ECaptureMode.AUTO)}
            className="w-[400px] h-[400px] bg-primary rounded-2xl flex flex-col items-center justify-center"
          >
            {isMultipleCamera ? (
              <SIcon size={150} name="STwoViews" color="primary_color" />
            ) : (
              <SIcon size={150} name="SAutoMode" color="primary_text_color" />
            )}

            <div className="flex  items-center justify-center mt-10 gap-1">
              <SText type="main" className="text-[40px] font-bold">
                Hẹn giờ
              </SText>

              {isMultipleCamera && (
                <SText type="main" className="text-[30px] font-bold">
                  (2 góc chụp)
                </SText>
              )}
            </div>
            <SText type="sub" className="text-[24px] mt-3">
              {`Timer${isMultipleCamera ? " (2 views)" : ""}`}
            </SText>
          </SButton>

          <SButton
            onClick={() => handleCaptureMode(ECaptureMode.MANUAL)}
            className="w-[400px] h-[400px] bg-primary flex rounded-2xl flex-col items-center justify-center"
          >
            <SIcon size={144} name="SRemoteMode" color="primary_text_color" />
            <SText type="main" className="text-[40px] font-bold mt-10">
              Điều khiển
            </SText>
            <SText type="sub" className="text-[24px] mt-3">
              Remote
            </SText>
          </SButton>
        </div>
      </div>
    </Layout>
  );
};

export default CaptureMode;
