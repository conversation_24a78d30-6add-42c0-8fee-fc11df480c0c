import { ROUTES, SECONDARY_ROUTES } from "@/cores/routes";
import QRCode from "react-qr-code";
import { useNavigate } from "react-router-dom";
import { SLoading } from "@/components/atoms/SLoading";
import SText from "@/components/atoms/SText";
import Layout from "@/components/templates/Layout";
import { CLIENT_APP_ORDER_PAYMENT_ONLINE } from "@/graphql/mutations/payment";
import usePayments from "@/hooks/usePayments";
import useSMutation from "@/hooks/useSMutation";
import { useEffect, useMemo, useState } from "react";
import { useOrder } from "@/hooks/useOrder";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { isMultipleCamera, isOverheadMachine } from "@/cores/constants";
import SIconButton from "@/components/molecules/SIconButton";

const QR = () => {
  const [qrCode, setQrCode] = useState<string>("");
  const [qrCodeUrl, setQrCodeUrl] = useState<string>("");
  const [refCode, setRefCode] = useState<string | null>(null);

  const navigate = useNavigate();
  const { isPaid } = usePayments({ refCode, setRefCode });
  const { layoutItem, draftOrder, promotionCode, frame, setPromotionCode } =
    useOrder();
  const { data } = useWaitingScreen();

  const { qrCodeMutate, qrCodeLoading } = useSMutation({
    gql: CLIENT_APP_ORDER_PAYMENT_ONLINE,
    options: {
      key: "qrCode",
      isLoadingAuto: false,
      onCompleted: (data) => {
        if (data?.clientAppCreateOrderPayOnline?.skipPayment) {
          if (isOverheadMachine) {
            window.api.secondaryNavigate(SECONDARY_ROUTES.COUNT_DOWN);
            navigate(ROUTES.COUNT_DOWN);

            return;
          }

          navigate(ROUTES.CAPTURE_MODE);
          return;
        }

        if (data?.clientAppCreateOrderPayOnline?.qrCode) {
          setRefCode(data?.clientAppCreateOrderPayOnline?.refCode);
          setQrCode(data?.clientAppCreateOrderPayOnline?.qrCode);

          return;
        }

        if (data?.clientAppCreateOrderPayOnline?.qrCodeUrl) {
          setQrCodeUrl(data?.clientAppCreateOrderPayOnline?.qrCodeUrl);
          setRefCode(data?.clientAppCreateOrderPayOnline?.refCode);
        }
      },
      onError: (error) => console.log(error),
    },
  });

  const settingSize = useMemo(
    () => data?.clientAppGetSettingSizes?.[0] ?? {},
    [data]
  );

  const isValid =
    !!layoutItem?.topicId &&
    !!draftOrder?.id &&
    !!frame?.id &&
    !!settingSize?.id &&
    !!draftOrder?.key;

  useEffect(() => {
    if (isValid) {
      const body: any = {
        topicId: layoutItem?.topicId,
        settingSizeId: settingSize.id,
        settingSizeKey: draftOrder.key,
        frameId: frame?.id,
      };

      if (promotionCode?.length >= 8) {
        body.promotionCode = promotionCode;
      }

      qrCodeMutate({
        variables: body,
      });
    }
  }, []);

  useEffect(() => {
    if (isPaid) {
      if (isOverheadMachine) {
        window.api.secondaryNavigate(SECONDARY_ROUTES.COUNT_DOWN);
        navigate(ROUTES.COUNT_DOWN);

        return;
      }

      navigate(ROUTES.CAPTURE_MODE);
    }
  }, [isPaid]);

  return (
    <Layout
      showLogo={false}
      title="VUI LÒNG THANH TOÁN"
      description="Please settle the payment"
    >
      <div className="flex w-full h-full flex-col justify-center items-center">
        <div className="w-[1200px] h-[250px] bg-primary_color mt-10 gap-3 rounded-2xl flex flex-col items-center justify-center">
          <SText type="main" className="text-[40px] font-bold">
            VUI LÒNG QUÉT MÃ QR DƯỚI ĐÂY
          </SText>
          <SText type="main" className="text-[40px] font-bold">
            BẰNG ỨNG DỤNG NGÂN HÀNG HOẶC VÍ ĐIỆN TỬ CỦA BẠN
          </SText>
          <SText type="sub" className="text-[24px]">
            Please scan QR-CODE below with your mobile banking or e-wallet
            application
          </SText>
        </div>

        <div className="flex justify-center items-center border-2 border-primary_text_color rounded-2xl w-[402px] h-[402px] mt-[35px] relative">
          {qrCodeLoading ? (
            <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
              <SLoading />
            </div>
          ) : (
            <div className="flex-1 flex justify-center items-center rounded-xl">
              {qrCodeUrl?.length > 0 ? (
                <img
                  className="w-[315px] h-[315px] object-contain"
                  src={qrCodeUrl}
                  alt="qr-code"
                />
              ) : (
                <QRCode size={315} value={qrCode} />
              )}
            </div>
          )}
        </div>

        <div className="flex flex-1 flex-col justify-end mt-[38px]">
          <SIconButton
            LeftIcon={"SArrowLeft"}
            text="Back"
            left={{
              color: "primary_text_color",
              size: 27,
            }}
            className="flex w-[250px] h-[65px] flex-row items-center justify-center"
            onClick={() => {
              setPromotionCode(null);
              navigate(-1);
            }}
          />
        </div>
      </div>
    </Layout>
  );
};

export default QR;
