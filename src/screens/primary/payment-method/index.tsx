import { SButton, SInput } from "@/components/atoms";
import SIcon from "@/components/atoms/SIcon";
import SText from "@/components/atoms/SText";
import SIconButton from "@/components/molecules/SIconButton";
import SKeyboard from "@/components/molecules/SKeyboard";
import { ROUTES } from "@/cores/routes";
import { CLIENT_APP_CHECK_PROMOTION } from "@/graphql/queries/promotion";
import { useOrder } from "@/hooks/useOrder";
import useSQuery from "@/hooks/useSQuery";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";

const PaymentMethod = () => {
  const navigate = useNavigate();
  const { setPaymentMethod, setDraftOrder, draftOrder, setPromotionCode } =
    useOrder();
  const [voucher, setVoucher] = useState("");
  const [voucherStatus, setVoucherStatus] = useState("");
  const [discountValue, setDiscountValue] = useState(0);
  const { data } = useWaitingScreen();

  const havePayScreen =
    data?.clientAppGetAppearanceSetting?.havePayScreen ?? false;

  const { promotionCodeMutate, promotionCodeLoading } = useSQuery({
    gql: CLIENT_APP_CHECK_PROMOTION,
    options: {
      key: "promotionCode",
      isLoadingAuto: false,
      onCompleted: (data) => {
        if (data?.clientAppCheckPromotion?.canUse) {
          setPromotionCode(voucher);
          setVoucherStatus("valid");
          setDiscountValue(data?.clientAppCheckPromotion?.discountValue ?? 0);
        }
      },
      onError: (_error) => {
        setVoucherStatus("invalid");
      },
    },
  });

  const handleSetPaymentMethod = (method: "cash" | "qr") => {
    setPaymentMethod(method);
    setDraftOrder({
      ...draftOrder,
      finalPrice: finalPrice,
    });

    if (voucherStatus === "invalid") {
      setPromotionCode("");
    }

    const route = method === "cash" ? ROUTES.CASH_PAYMENT : ROUTES.QR_PAYMENT;
    navigate(route);
  };

  const handleFinish = () => {
    if (voucher?.length >= 8) {
      promotionCodeMutate({
        variables: {
          promotionCode: voucher,
        },
      });
    }
  };

  const finalPrice = useMemo(
    () => Math.max(0, (draftOrder?.price ?? 0) - (discountValue ?? 0)),
    [draftOrder?.price, discountValue]
  );

  const renderVoucherStatus = useCallback(() => {
    if (!voucher || voucher.length < 8) {
      return <></>;
    }

    if (voucherStatus === "valid") {
      return (
        <div
          className="flex items-center justify-center w-[30px] h-[30px] rounded-full"
          style={{
            backgroundColor: "green",
          }}
        >
          <SIcon name="CheckIcon" weight="bold" color="white" size={24} />
        </div>
      );
    }

    if (voucherStatus === "invalid") {
      return (
        <div
          className="flex items-center justify-center w-[30px] h-[30px] rounded-full"
          style={{
            backgroundColor: "red",
          }}
        >
          <SIcon name="XMarkIcon" weight="fill" color="white" size={24} />
        </div>
      );
    }

    return <></>;
  }, [voucherStatus, voucher]);

  useEffect(() => {
    if (finalPrice <= 0) {
      handleSetPaymentMethod("cash");
    }
  }, [finalPrice]);

  return (
    <div className="h-screen w-screen flex flex-col items-center justify-center p-[100px]">
      <div className="w-full flex flex-row justify-between gap-10">
        <div className="flex w-[480px] p-11 flex-col gap-2 border-2 rounded-2xl">
          <div className="flex flex-col gap-2 border-b-2 border-b-primary_text_color pb-4">
            <SText type="main" className="text-[40px] font-semibold">
              Voucher Code
            </SText>
            <div className="flex flex-row items-center gap-2">
              <SText type="sub" className="text-[20px]">
                Nhập mã voucher (nếu có) và nhấn
              </SText>

              <div className="w-[36px] h-[36px] bg-primary_color rounded-full flex items-center justify-center">
                <SIcon
                  name="SArrowUDownLeft"
                  color="primary_text_color"
                  size={20}
                />
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2 border-b-2 border-b-primary_text_color pb-4 ">
            <SText type="main" className="text-[40px] font-bold mt-[35px]">
              Total Payment:
            </SText>
            <SText type="sub" className="text-[20px] mt-2">
              Số tiền bạn cần thanh toán là:
            </SText>
          </div>
        </div>

        <div className="flex flex-1 border-2 p-11 rounded-2xl flex-col gap-2">
          <div className="w-full mt-6 relative">
            <SInput
              type="text"
              autoFocus
              value={voucher}
              placeholder="Voucher code"
              onChange={(e) => {
                setVoucher(e.target.value);
              }}
              className="border-2 rounded-lg p-4 text-[24px] w-full h-full"
            />
            <div className="absolute right-4 top-1/2 -translate-y-1/2">
              {renderVoucherStatus()}
            </div>
          </div>

          <div className="flex flex-col border-b-2 border-b-primary_text_color mt-[55px] justify-center pb-4">
            <SText type="main" className="text-[70px] font-bold">
              {finalPrice?.toLocaleString("vi-VN", {
                style: "currency",
                currency: "VND",
              })}
            </SText>
          </div>
        </div>

        <div className="flex w-[480px] p-11 flex-col gap-2 border-2 rounded-2xl">
          <SKeyboard
            onFinish={handleFinish}
            disabled={promotionCodeLoading}
            setVoucher={setVoucher}
            voucher={voucher}
          />
        </div>
      </div>

      <div className="w-full flex flex-col items-center mt-[35px]">
        <SText type="main" className="text-[30px] font-bold">
          Hãy chọn hình thức thanh toán bạn muốn
        </SText>
        <SText type="sub" className="text-[24px] mt-2">
          Please select your preferred payment method
        </SText>
      </div>

      <div className="w-full flex flex-row justify-center gap-10 mt-[35px] px-[100px]">
        <SButton
          noBackground
          effect="dark"
          onClick={() => handleSetPaymentMethod("cash")}
          className="w-[470px] h-[142px] rounded-2xl bg-primary_color flex flex-row items-center justify-center gap-4"
        >
          <div className="w-[100px] h-[100px] rounded-full bg-background_color flex items-center justify-center">
            <SIcon name="SMoney" color="primary_color" size={60} />
          </div>
          <div className="flex flex-col items-start gap-2">
            <SText type="main" className="text-[36px] font-bold">
              Nạp tiền mặt
            </SText>
            <SText type="sub" className="text-[24px]">
              Pay Cash
            </SText>
          </div>
        </SButton>

        {havePayScreen ? (
          <SButton
            noBackground
            effect="dark"
            onClick={() => handleSetPaymentMethod("qr")}
            className="w-[470px] h-[142px] rounded-2xl bg-primary_color flex flex-row items-center justify-center gap-4"
          >
            <div className="w-[100px] h-[100px] rounded-full bg-background_color flex items-center justify-center">
              <SIcon name="QrCodeIcon" color="primary_color" size={60} />
            </div>
            <div className="flex flex-col items-start gap-2">
              <SText type="main" className="text-[36px] font-bold">
                Quét mã QR
              </SText>
              <SText type="sub" className="text-[24px]">
                Pay with QR
              </SText>
            </div>
          </SButton>
        ) : null}
      </div>

      <div className="flex flex-1 flex-col justify-end mt-[38px]">
        <SIconButton
          LeftIcon={"SArrowLeft"}
          text="Back"
          left={{
            color: "primary_text_color",
            size: 27,
          }}
          className="flex w-[250px] h-[65px] flex-row items-center justify-center"
          onClick={() => {
            navigate(ROUTES.TOTAL_SELECTION);
          }}
        />
      </div>
    </div>
  );
};

export default PaymentMethod;
