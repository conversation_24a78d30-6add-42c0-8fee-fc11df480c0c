import SIcon from "@/components/atoms/SIcon";
import React, { FC } from "react";

type Props = {
  image: string;
  index: number;
  onImageChange: (data: number) => void;
  onRemoveImage: (data: number) => void;
  isActive?: boolean;
};

const ImageItem: FC<Props> = ({
  image,
  index,
  onImageChange,
  onRemoveImage,
  isActive,
}) => {
  const displayUrl = `app:///${encodeURIComponent(image)}`;

  return (
    <div className="relative col-span-1 border-2 border-gray-300 aspect-[6/4] ">
      <img
        src={displayUrl}
        className="w-full h-auto object-cover cursor-pointer"
        onClick={() => onImageChange(index)}
        alt={`image-select-item-${index}`}
      />
      {isActive && (
        <div className="rounded-full w-fit h-fit absolute bg-white -bottom-2 -right-2 flex justify-center items-center">
          <SIcon size={32} name="SCheck" weight="fill" color="green" />
        </div>
      )}

      {isActive && (
        <div className="w-full h-full absolute bg-transparent top-0 left-0 flex justify-center items-center">
          <button
            className="w-full h-full bg-transparent flex justify-center items-center"
            onClick={() => onRemoveImage(index)}
          />
        </div>
      )}
    </div>
  );
};

export default React.memo(ImageItem);
