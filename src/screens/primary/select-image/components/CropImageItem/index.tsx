import SIcon from "@/components/atoms/SIcon";
import usePhotoDimension from "@/hooks/usePhotoDimension";
import dayjs from "dayjs";
import React, { FC, useMemo, useState } from "react";
import QRCode from "react-qr-code";

type Props = {
  x_coordinate: number;
  y_coordinate: number;
  width?: number;
  height?: number;
  image?: string;
  aspectRatioWidth: number;
  aspectRatioHeight: number;
  style?: any;
  qr?: string;
  qrUrl?: string;
  date?: boolean;
  angle?: number;
  haveLoading?: boolean;
  onRemoveImage?: () => void;
  background?: any;
};

const CropImageItem: FC<Props> = ({
  x_coordinate = 0,
  y_coordinate = 0,
  width = 0,
  height = 0,
  image,
  aspectRatioWidth,
  aspectRatioHeight,
  style = {},
  qr,
  qrUrl,
  date,
  haveLoading = true,
  angle = 0,
  onRemoveImage,
  background,
}) => {
  const [isLoading, setIsLoading] = useState(true);

  const { safeWidth, safeHeight, finalWidth, finalHeight, finalX, finalY } =
    useMemo(() => {
      const rx = typeof x_coordinate === "number" ? x_coordinate : 0;
      const ry = typeof y_coordinate === "number" ? y_coordinate : 0;
      const _safeWidth = typeof width === "number" ? width : 0;
      const _safeHeight = typeof height === "number" ? height : 0;
      const _safeX = typeof rx === "number" ? rx : 0;
      const _safeY = typeof ry === "number" ? ry : 0;
      const _finalX = Math.max(0, _safeX * aspectRatioWidth);
      const _finalY = Math.max(0, _safeY * aspectRatioHeight);

      const _finalWidth = Math.max(0, _safeWidth * aspectRatioWidth);
      const _finalHeight = Math.max(0, _safeHeight * aspectRatioHeight);

      return {
        safeWidth: _safeWidth,
        safeHeight: _safeHeight,
        finalWidth: _finalWidth,
        finalHeight: _finalHeight,
        finalX: _finalX,
        finalY: _finalY,
      };
    }, [
      x_coordinate,
      y_coordinate,
      width,
      height,
      aspectRatioWidth,
      aspectRatioHeight,
    ]);

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const removable = useMemo(() => {
    return !!onRemoveImage;
  }, [onRemoveImage]);

  const { croppedImage } = usePhotoDimension({
    url: image,
    rectWidth: safeWidth,
    rectHeight: safeHeight,
    background,
  });

  if (qr && qrUrl) {
    return (
      <div
        style={{
          width: `${finalWidth}px`,
          height: `${finalHeight}px`,
          left: `${finalX}px`,
          top: `${finalY}px`,
          position: "absolute",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          transform: `rotate(${angle}deg)`,
        }}
      >
        <QRCode size={finalWidth} value={qrUrl} />
      </div>
    );
  }

  if (date) {
    return (
      <div
        style={{
          height: `${finalHeight}px`,
          left: `${finalX}px`,
          top: `${finalY}px`,
          position: "absolute",
          display: "flex",
          alignItems: "center",
          transform: `rotate(${angle}deg)`,
        }}
        className="z-50"
      >
        <span className="text-[10px] font-normal text-black">
          {dayjs().format("DD.MM.YYYY")}
        </span>
      </div>
    );
  }

  return (
    <div
      style={{
        width: `${finalWidth}px`,
        height: `${finalHeight}px`,
        left: `${finalX}px`,
        top: `${finalY}px`,
        position: "absolute",
      }}
    >
      {isLoading && haveLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black opacity-40">
          <div className="w-4 h-4 border-2 border-green border-t-transparent rounded-full animate-spin" />
        </div>
      )}

      {!isLoading && removable && (
        <div
          className="w-8 h-8 rounded-full absolute bg-white -bottom-2 -right-2 flex justify-center items-center"
          onClick={() => onRemoveImage?.()}
        >
          <SIcon size={32} name="SClear" weight="fill" color="primary_color" />
        </div>
      )}

      {removable && (
        <div className="w-full h-full absolute bg-transparent top-0 right-0 flex justify-center items-center">
          <button
            className="w-full h-full bg-transparent flex justify-center items-center"
            onClick={() => onRemoveImage?.()}
          />
        </div>
      )}

      <img
        src={croppedImage}
        onLoad={handleImageLoad}
        className="absolute -z-[1] object-fit w-full h-full"
        style={{
          ...style,
          transform: `rotate(${angle}deg)`,
          ...(background && {
            backgroundImage: `url(${background?.fileUrl})`,
            backgroundRepeat: "no-repeat",
            backgroundSize: "100% 100%",
            backgroundOrigin: "content-box",
          }),
        }}
        loading="lazy"
        decoding="async"
        width={finalWidth}
        height={finalHeight}
        crossOrigin="anonymous"
        alt="snapbox"
      />
    </div>
  );
};

export default React.memo(CropImageItem);
