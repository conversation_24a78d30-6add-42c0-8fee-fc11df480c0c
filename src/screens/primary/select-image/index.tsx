import SText from "@/components/atoms/SText";
import SIconButton from "@/components/molecules/SIconButton";
import SMixedFrame from "@/components/molecules/SMixedFrame";
import Layout from "@/components/templates/Layout";
import { ROUTES } from "@/cores/routes";
import { useOrder } from "@/hooks/useOrder";
import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import ImageItem from "./components/ImageItem";

const SelectImage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState<number>(200);
  const [selectedPhotos, setSelectedPhotos] = useState<number[]>([]);
  const { photos, frame, photoPaths } = useOrder();
  const [currentFrameRect, setCurrentFrameRect] = useState<any>(null);
  const navigate = useNavigate();

  const limit = useMemo(() => frame?.limit ?? 10, [frame?.limit]);

  const frameSize = useMemo(() => {
    return {
      width: frame?.orientation === "Horizontal" ? 1800 : 1200,
      height: frame?.orientation === "Horizontal" ? 1200 : 1800,
      ratio:
        frame?.orientation === "Horizontal"
          ? window.innerWidth / 1800
          : window.innerHeight / 1200,
    };
  }, [frame?.orientation]);

  const formatDisplayFrames =
    frame?.frameItems?.filter(
      (record: any) => record?.itemType === "SubFrame"
    ) ?? [];

  useEffect(() => {
    if (frame?.frameItems?.length > 0) {
      const firstFrame = frame.frameItems.find(
        (item: any) => item.itemType === "SubFrame"
      );
      setCurrentFrameRect(firstFrame);
    }
  }, [frame?.frameItems]);

  const getDupFrames = (frameItem: any) => {
    return (
      frame?.frameItems?.filter(
        (item: any) =>
          item.itemType === "DubFrame" && item.parentId === frameItem?.itemId
      ) || []
    );
  };

  const handleImageSelect = (index: number) => {
    if (currentFrameRect?.id) {
      const maxFrames = formatDisplayFrames.length;
      const newSelectedPhotos = [...selectedPhotos];

      // Tìm vị trí trống đầu tiên hoặc cuối mảng
      let currentIndex = newSelectedPhotos.findIndex((p) => p === -1);
      if (currentIndex === -1) currentIndex = newSelectedPhotos.length;

      // Dừng nếu vượt quá số frame
      if (currentIndex >= maxFrames) return;

      // Đảm bảo độ dài mảng đủ (có thể thay bằng fill)
      if (currentIndex >= newSelectedPhotos.length) {
        newSelectedPhotos[currentIndex] = index;
      } else {
        newSelectedPhotos[currentIndex] = index;
      }

      setSelectedPhotos(newSelectedPhotos);
      // Chuyển frame tiếp theo nếu chưa đến cuối
      if (currentIndex < maxFrames - 1) {
        setCurrentFrameRect(formatDisplayFrames[currentIndex + 1]);
      }
    }
  };

  const photoMaps = useMemo(() => {
    const photoMap = new Map();

    formatDisplayFrames?.forEach((frame: any, index: number) => {
      const photoIndex = selectedPhotos[index];
      if (photoIndex !== undefined && photoIndex !== -1) {
        photoMap.set(frame.id, photoIndex);

        const dupFrames = getDupFrames(frame);
        dupFrames.forEach((dupFrame: any) => {
          photoMap.set(dupFrame.id, photoIndex);
        });
      }
    });
    return photoMap;
  }, [selectedPhotos, formatDisplayFrames]);

  const handleRemoveImage = (photoIndex: number) => {
    // Tìm tất cả các frame (SubFrame và DupFrame) đang sử dụng ảnh này
    const newSelectedPhotos = [...selectedPhotos];

    // Tìm SubFrame đang sử dụng ảnh này
    const frameIndex = selectedPhotos.indexOf(photoIndex);
    if (frameIndex !== -1) {
      // Xóa ảnh khỏi SubFrame
      newSelectedPhotos[frameIndex] = -1;

      // Set currentFrameRect về frame vừa bị xóa ảnh
      setCurrentFrameRect(formatDisplayFrames[frameIndex]);
    }

    setSelectedPhotos(newSelectedPhotos);
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // Khi hết giờ, tự động điền các ảnh còn thiếu
          const remainingSlots =
            limit - selectedPhotos.filter((p) => p !== -1).length;
          if (remainingSlots > 0) {
            const newSelectedPhotos = [...selectedPhotos];
            let photoIndex = 0;

            // Điền các vị trí trống (-1) trước
            for (let i = 0; i < newSelectedPhotos.length; i++) {
              if (newSelectedPhotos[i] === -1) {
                // Tìm ảnh chưa được sử dụng
                while (selectedPhotos.includes(photoIndex)) {
                  photoIndex++;
                }
                newSelectedPhotos[i] = photoIndex++;
              }
            }

            // Thêm các ảnh mới nếu cần
            while (newSelectedPhotos.length < formatDisplayFrames?.length) {
              while (selectedPhotos.includes(photoIndex)) {
                photoIndex++;
              }
              newSelectedPhotos.push(photoIndex++);
            }

            setSelectedPhotos(newSelectedPhotos);
          }
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [limit, selectedPhotos, formatDisplayFrames, navigate, photoMaps]);

  const handleContinue = async () => {
    setIsLoading(true);
    const maps: any = {};
    // Convert Map entries to object correctly
    for (const [key, value] of photoMaps.entries()) {
      maps[key] = photoPaths[value];
    }

    setIsLoading(false);
    navigate(ROUTES.FILTER_STICK, { state: { selectedPhotos, photoMaps } });
  };

  const items = useMemo(() => {
    if (photos.length < 16) {
      return Array.from({ length: 16 });
    }

    return Array.from({ length: photos.length });
  }, [photos]);

  useEffect(() => {
    if (countdown <= 0 && selectedPhotos.length >= formatDisplayFrames.length) {
      handleContinue();
    }
  }, [countdown, selectedPhotos, formatDisplayFrames.length]);

  return (
    <Layout
      title="VUI LÒNG CHỌN ẢNH BẠN MUỐN IN"
      description="Please choose the photos to print"
      showLogo={false}
    >
      <div className="flex flex-col mt-10 flex-1">
        <div className="flex w-full px-[100px] relative h-[700px]">
          <div className="absolute -top-40 right-10 p-4 flex justify-end w-full h-full">
            <SText className="text-[52px] font-bold" type="main">
              {countdown}
            </SText>
          </div>

          <SMixedFrame
            frame={frame}
            frameSize={frameSize}
            photoMaps={photoMaps}
            photos={photos}
            handleRemoveImage={handleRemoveImage}
          />

          <div className="grid grid-cols-3 border-2 border-secondary_color p-10 gap-4 flex-1 h-full overflow-y-auto">
            {items.map((_, index: number) => {
              const photo = photos[index];

              if (photo) {
                return (
                  <ImageItem
                    key={`image-select-item-${index}`}
                    index={index}
                    image={photo}
                    isActive={selectedPhotos?.includes(index)}
                    onImageChange={() => handleImageSelect(index)}
                    onRemoveImage={() => handleRemoveImage(index)}
                  />
                );
              }

              return (
                <div
                  key={`image-select-item-${index}`}
                  className="cols-span-1 aspect-[6/4]"
                />
              );
            })}
          </div>
        </div>
        <div className="flex justify-center items-center mt-10">
          <SIconButton
            RightIcon={"SArrowRight"}
            text="Next"
            className="bg-primary h-[65px] w-[250px] flex flex-row items-center gap-4"
            onClick={handleContinue}
            isLoading={isLoading}
            disabled={
              selectedPhotos.length < formatDisplayFrames.length || isLoading
            }
          />
        </div>
      </div>
    </Layout>
  );
};

export default SelectImage;
