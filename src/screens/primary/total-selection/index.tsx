import { useNavigate } from "react-router-dom";

import SPriceSelector from "@/components/molecules/SPriceSelector";
import SNavigatorLayout from "@/components/templates/SNavigatorLayout";
import { ROUTES } from "@/cores/routes";
import { useOrder } from "@/hooks/useOrder";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { useMemo, useState } from "react";

const sizeSmallPrices = [
  {
    id: 1,
    vi_title: "02",
    en_title: "2 Photos",
    quantity: 2,
    key: "smallSizePrice2",
  },
  {
    id: 2,
    vi_title: "04",
    en_title: "4 Photos",
    quantity: 4,
    key: "smallSizePrice4",
  },
  {
    id: 3,
    vi_title: "06",
    en_title: "6 Photos",
    quantity: 6,
    key: "smallSizePrice6",
  },
  {
    id: 4,
    vi_title: "08",
    en_title: "8 Photos",
    quantity: 8,
    key: "smallSizePrice8",
  },
  {
    id: 5,
    vi_title: "10",
    en_title: "10 Photos",
    quantity: 10,
    key: "smallSizePrice10",
  },
];

const sizeBigPrices = [
  {
    id: 6,
    vi_title: "02",
    en_title: "2 Photos",
    quantity: 2,
    key: "largeSizePrice2",
  },
  {
    id: 7,
    vi_title: "03",
    en_title: "3 Photos",
    quantity: 3,
    key: "largeSizePrice3",
  },
  {
    id: 8,
    vi_title: "04",
    en_title: "4 Photos",
    quantity: 4,
    key: "largeSizePrice4",
  },
  {
    id: 9,
    vi_title: "05",
    en_title: "5 Photos",
    quantity: 5,
    key: "largeSizePrice5",
  },
  {
    id: 10,
    vi_title: "06",
    en_title: "6 Photos",
    quantity: 6,
    key: "largeSizePrice6",
  },
];

const TotalSelection = () => {
  const navigate = useNavigate();
  const { setDraftOrder, size, layoutItem, draftOrder, setPaymentMethod } =
    useOrder();
  const { data } = useWaitingScreen();
  const [selectedPrice, setSelectedPrice] = useState<any>(draftOrder);

  const handleGoBack = () => navigate(ROUTES.FRAME_SELECTION);

  const settingPrice = useMemo(
    () => data?.clientAppGetSettingSizes?.[0] ?? {},
    [data]
  );

  const extraFee = useMemo(
    () => layoutItem?.topic?.extraFee ?? 0,
    [layoutItem]
  );

  const sizes = useMemo(() => {
    const priceSizes = size === "small" ? sizeSmallPrices : sizeBigPrices;

    return priceSizes.map((prz) => ({
      ...prz,
      price: (settingPrice?.[prz.key] ?? 0) + extraFee,
    }));
  }, [size, settingPrice, extraFee]);

  const handleSubmit = () => {
    setDraftOrder({
      ...selectedPrice,
      finalPrice: selectedPrice.price,
    });

    navigate(ROUTES.PAYMENT_METHOD);
  };

  return (
    <SNavigatorLayout
      title="CHỌN SỐ LƯỢNG ẢNH IN"
      description="Please choose the number of printed photos"
      onGoBack={handleGoBack}
      onSubmit={handleSubmit}
      className="justify-center gap-[74px]"
      disabled={!selectedPrice}
    >
      <div className="flex flex-row gap-[50px] flex-1 w-full justify-center items-center px-[100px]">
        {sizes.map((sz) => (
          <SPriceSelector
            key={sz.id}
            size={sz}
            selectedPrice={selectedPrice}
            setSelectedPrice={setSelectedPrice}
          />
        ))}
      </div>
    </SNavigatorLayout>
  );
};

export default TotalSelection;
