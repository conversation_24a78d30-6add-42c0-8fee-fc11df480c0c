import { useCallback, useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { ROUTES } from "@/cores/routes";
import FilterStickerView from "./view";
import useRequestPrint from "@/hooks/useRequestPrint";
import { useOrder } from "@/hooks/useOrder";
import { useAuth } from "@/hooks/useAuth";
import { apiUrl } from "@/cores/utils/config";
import { isEnableRemoveBackground } from "@/cores/constants";

const MAX_DURATION = isEnableRemoveBackground ? 200 : 120;

const FilterSticker = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { print } = useRequestPrint({});
  const state = location.state;
  const frameRef = useRef<any>(null);
  const printingRef = useRef<boolean>(false); // Ref to track printing state
  const {
    filterData,
    setFilterData,
    setCurrentSticker,
    frame,
    photos,
    size,
    photoPaths,
    setDraftOrder,
    draftOrder,
    captureMode,
  } = useOrder();

  const { accessToken } = useAuth();
  const [countdown, setCountdown] = useState<number>(MAX_DURATION);
  const selectedPhotos = state?.selectedPhotos ?? [];
  const photoMaps = state?.photoMaps ?? new Map();
  const [isPrinting, setIsPrinting] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);

          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [navigate]);

  const handleDeleteSticker = useCallback(
    (pos: any) => {
      setFilterData({
        stickers:
          filterData?.stickers?.filter(
            (__: any, index: number) => index !== pos
          ) || [],
      });
      setCurrentSticker(undefined);
    },
    [filterData, setFilterData, setCurrentSticker]
  );

  const handleStickerChange = useCallback(
    (data: any, pos: number) => {
      setFilterData({
        ...filterData,
        stickers:
          filterData?.stickers?.map((record: any, index: number) =>
            record?.id === data?.id && index === pos
              ? {
                  ...record,
                  ...data,
                }
              : record
          ) || [],
      });

      const stickerRecord = filterData?.stickers?.find(
        (record: any, index: number) => record?.id === data?.id && index === pos
      );
      if (stickerRecord) {
        setCurrentSticker({
          ...stickerRecord,
          ...data,
        });
      }
    },
    [filterData, setFilterData]
  );

  const handlePrintImage = async () => {
    try {
      if (draftOrder?.id && frame?.imageUrl) {
        setIsPrinting(true); // For UI updates

        const quantity = draftOrder?.quantity || 1;
        const cutable = size === "small";
        let printerQuantity = size === "small" ? quantity / 2 : quantity;

        if (printerQuantity < 1) {
          printerQuantity = 1;
        }

        const positions = frame?.frameItems.filter(
          (item: any) =>
            item.itemType === "SubFrame" ||
            (item.itemType === "DubFrame" && !!item.parentId)
        );

        const dates =
          frame?.frameItems?.filter((item: any) => item.itemType === "Date") ??
          [];

        const qrCodes =
          frame?.frameItems?.filter(
            (item: any) => item.itemType === "QrCode"
          ) ?? [];

        const maps: any = {};
        for (const [key, value] of photoMaps.entries()) {
          maps[key] = {
            path: photoPaths[value],
          };
        }

        const { localPath, withoutQRLocalPath } =
          await window.api.mergePhotosToFrame({
            framePath: frame?.imageUrl,
            photoMaps: maps,
            positions,
            dir: `orders/${draftOrder?.id}`,
            filename: `merged-image-${Date.now()}.png`,
            filterType: filterData?.filter,
            stickers: filterData?.stickers,
            dateColor: frame?.dateColor || "#000000",
            dates: filterData?.date_image ? dates : [],
            qrCodes: filterData?.qr_type ? qrCodes : [],
            qrUrl: draftOrder?.domain,
            viewWidth: frameRef?.current?.getFrameDimensions()?.width,
            viewHeight: frameRef?.current?.getFrameDimensions()?.height,
            background: filterData?.background,
          });

        if (localPath) {
          if (!printingRef.current) {
            printingRef.current = true;

            print({
              imgUrl: localPath,
              options: {
                quantity: printerQuantity,
                cutable,
              },
            });
          }

          window.api.updateOrder({
            photos: photoPaths,
            finalUrl: withoutQRLocalPath,
            timelapseUrl: draftOrder?.timelapseVideoUrl,
            orderId: draftOrder?.id,
            captureMode: captureMode,
            apiUrl,
            accessToken,
          });

          setDraftOrder({
            imgUrl: withoutQRLocalPath,
          });

          navigate(ROUTES.FINAL_RESULT);
        }
      }
    } catch (err) {
      printingRef.current = false;
      setIsPrinting(false); // For UI updates
      console.error("Error generating image:", err);
      // You might want to show an error message to the user here
    }
  };

  useEffect(() => {
    if (!selectedPhotos?.length) {
      navigate("/");
    }
  }, [selectedPhotos, navigate]);

  useEffect(() => {
    if (countdown === 0 && !printingRef.current) {
      const _timer = setTimeout(() => {
        // Double-check again right before executing to prevent race conditions
        if (!printingRef.current) {
          handlePrintImage();
        }
      }, 3000);

      return () => {
        clearTimeout(_timer);
      };
    }
  }, [countdown]);

  return (
    <FilterStickerView
      countdown={countdown}
      frame={frame}
      selectedPhotos={selectedPhotos}
      filterData={filterData}
      isPrinting={isPrinting}
      onDeleteSticker={handleDeleteSticker}
      onStickerChange={handleStickerChange}
      onPrintImage={handlePrintImage}
      photoMaps={photoMaps}
      photos={photos}
      frameRef={frameRef}
    />
  );
};

export default FilterSticker;
