import { FC, memo, useMemo, useState } from "react";
import CustomizeFilterSection from "./components/CustomizeFilterSection";
import { returnFilterStyling } from "@/cores/helpers";
import { Frame } from "@/cores/types";
import SFilterFrame from "@/components/molecules/SFilterFrame";
import Layout from "@/components/templates/Layout";
import SText from "@/components/atoms/SText";
import { SButton } from "@/components/atoms";

interface Position {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface Sticker {
  id: string;
  url: string;
  position: Position;
}

interface FilterStickerViewProps {
  countdown: number;
  frame: Frame;
  selectedPhotos: number[];
  frameRef: any;
  filterData: {
    filter: string;
    stickers: Sticker[];
    date_image?: boolean;
    qr_type?: boolean;
  };
  isPrinting: boolean;
  onDeleteSticker: (pos: number) => void;
  onStickerChange: (sticker: Sticker, pos: number) => void;
  onPrintImage: () => void;
  photoMaps: any;
  photos: string[];
}

const FilterStickerView: FC<FilterStickerViewProps> = ({
  countdown,
  frame,
  filterData,
  isPrinting,
  photoMaps,
  photos,
  onDeleteSticker,
  onStickerChange,
  onPrintImage,
  frameRef,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const frameSize = useMemo(() => {
    return {
      width: frame?.orientation === "Horizontal" ? 1800 : 1200,
      height: frame?.orientation === "Horizontal" ? 1200 : 1800,
      ratio:
        frame?.orientation === "Horizontal"
          ? window.innerWidth / 1800
          : window.innerHeight / 1200,
    };
  }, [frame?.orientation]);

  const getFilterStyle = (isMixed: any) => {
    if (filterData?.filter === "mix") {
      return isMixed
        ? returnFilterStyling("mix")
        : returnFilterStyling("origin");
    }
    return returnFilterStyling(filterData?.filter);
  };

  return (
    <Layout title="CHỈNH SỬA ẢNH" description="Photo editing" showLogo={false}>
      <div className="flex w-full mt-[65px] px-[100px] flex-1">
        <div className="flex flex-col flex-1 items-center gap-16">
          {frame?.imageUrl && (
            <SFilterFrame
              frame={frame}
              frameSize={frameSize}
              photos={photos}
              photoMaps={photoMaps}
              filters={filterData}
              currentIndex={currentIndex}
              setCurrentIndex={setCurrentIndex}
              onDeleteSticker={onDeleteSticker}
              onStickerChange={onStickerChange}
              getFilterStyle={getFilterStyle}
              ref={frameRef}
            />
          )}

          <div className="w-[250px] h-[65px] flex justify-center items-center">
            <SText type="main" className="text-[30px] font-bold">
              {countdown}
            </SText>
          </div>
        </div>

        <div className="flex flex-col flex-1 h-full items-center gap-16">
          <CustomizeFilterSection
            setCurrentIndex={setCurrentIndex}
            currentIndex={currentIndex}
            isPrinting={isPrinting}
          />

          <SButton
            onClick={onPrintImage}
            disabled={isPrinting}
            className="w-[250px] h-[65px] rounded-full flex justify-center items-center gap-2"
          >
            <SText type="main" className="font-bold text-[30px]">
              Print
            </SText>

            {isPrinting && (
              <svg
                aria-hidden="true"
                className="w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                viewBox="0 0 100 101"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor"
                />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="currentFill"
                />
              </svg>
            )}
          </SButton>
        </div>
      </div>
    </Layout>
  );
};

export default memo(FilterStickerView);
