import { FILTER_OPTIONS } from "@/cores/constants";
import SFilterItem from "@/components/atoms/SFilterItem";
import { useOrder } from "@/hooks/useOrder";
import SText from "@/components/atoms/SText";

const FilterSection = ({ isPrinting }: { isPrinting: boolean }) => {
  const { filterData, setFilterData } = useOrder();

  return (
    <div className="flex flex-row gap-4 items-center">
      <div className="flex flex-col gap-2">
        <SText type="main" className="text-[30px] font-bold">
          Bộ lọc
        </SText>
        <SText type="sub" className="text-[20px]">
          Filter
        </SText>
      </div>

      <div className="flex flex-wrap items-center gap-5">
        {FILTER_OPTIONS.map((record) => (
          <SFilterItem
            key={record?.value}
            {...record}
            isPrinting={isPrinting}
            isActive={filterData?.filter === record?.value}
            onFilterChange={(value: string) =>
              setFilterData({
                filter: value,
              })
            }
          />
        ))}
      </div>
    </div>
  );
};

export default FilterSection;
