import { useOrder } from "@/hooks/useOrder";
import SText from "@/components/atoms/SText";
import { useMemo } from "react";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { cn } from "@/cores/helpers";
import SIcon from "@/components/atoms/SIcon";

const NO_BACKGROUND_IMAGE = {
  id: 0,
  fileName: "Không nền",
  fileUrl: "",
};

const BackgroundSection = ({ isPrinting }: { isPrinting: boolean }) => {
  const { filterData, setFilterData } = useOrder();
  const { data } = useWaitingScreen();
  const backgroundImages = useMemo(
    () => data?.clientAppGetBackgroundImages?.images ?? [],
    [data]
  );

  return (
    <div className="flex flex-row items-center gap-6 flex-1">
      <div className="flex flex-col gap-2 w-[180px]">
        <SText type="main" className="text-[30px] font-bold">
          AI Magic ✨
        </SText>
        <SText type="sub" className="text-[20px]">
          AI Background
        </SText>
      </div>

      <div className="flex flex-row items-center gap-3 flex-1">
        <div
          onClick={() => setFilterData({ background: NO_BACKGROUND_IMAGE })}
          className={cn(
            "w-[60px] h-[60px] rounded-lg flex justify-center items-center overflow-hidden bg-secondary_color",
            NO_BACKGROUND_IMAGE?.id === filterData?.background?.id &&
              "border-2 border-primary_color"
          )}
        >
          <SIcon name="SNoBackground" size={25} color="primary_color" />
        </div>

        <div className="flex flex-1 flex-row gap-3 overflow-x-auto max-w-[400px] scrollbar-hide">
          {backgroundImages.map((record: any, index: number) => (
            <div
              onClick={() => setFilterData({ background: record })}
              key={`${record?.id}-${index}`}
              className={cn(
                "w-[60px] h-[60px] rounded-lg flex justify-center shrink-0 items-center overflow-hidden bg-secondary_color",
                record?.id === filterData?.background?.id &&
                  "border-2 border-primary_color"
              )}
            >
              <img
                className="w-full h-full object-cover"
                src={record?.fileUrl}
                alt={record?.fileName}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BackgroundSection;
