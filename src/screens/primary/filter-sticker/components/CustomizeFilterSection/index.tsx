import SCircleCheckbox from "@/components/atoms/SCircleCheckbox";
import SText from "@/components/atoms/SText";
import { useOrder } from "@/hooks/useOrder";
import { FC } from "react";
import FilterSection from "../FilterSection";
import StickerSection from "../StickerSection";
import BackgroundSection from "../BackgroundSection";
import { isEnableRemoveBackground } from "@/cores/constants";

type Props = {
  setCurrentIndex: any;
  currentIndex: number;
  isPrinting: boolean;
};

const CustomizeFilterSection: FC<Props> = ({
  setCurrentIndex,
  currentIndex,
  isPrinting,
}) => {
  const { filterData, setFilterData } = useOrder();

  return (
    <div className="flex flex-col flex-1 rounded-2xl border-2 border-secondary_color p-10 h-[630px] gap-10">
      <div className="flex flex-row gap-[30px] relative">
        <div className="flex flex-col gap-2">
          <SText type="main" className="text-[30px] font-bold">
            In lên ảnh
          </SText>

          <SText type="sub" className="text-[20px]">
            Print on photo
          </SText>
        </div>

        <div className="flex gap-[30px]">
          <SCircleCheckbox
            value={true}
            label="Mã QR tải ảnh"
            description="QR code to download photo"
            isActive={filterData?.qr_type}
            onChange={() =>
              setFilterData({
                qr_type: !filterData?.qr_type,
              })
            }
          />
          <SCircleCheckbox
            value={true}
            label="Ngày chụp ảnh"
            description="Photo day"
            isActive={filterData?.date_image}
            onChange={() =>
              setFilterData({
                date_image: !filterData?.date_image,
              })
            }
          />
        </div>
      </div>
      <FilterSection isPrinting={isPrinting} />
      {isEnableRemoveBackground && (
        <BackgroundSection isPrinting={isPrinting} />
      )}
      <StickerSection
        setCurrentIndex={setCurrentIndex}
        currentIndex={currentIndex}
        isPrinting={isPrinting}
      />
    </div>
  );
};

export default CustomizeFilterSection;
