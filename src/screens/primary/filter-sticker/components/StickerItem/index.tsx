import SIcon from "@/components/atoms/SIcon";
import { FC, useRef, useState, useEffect } from "react";
import Draggable from "react-draggable";

type Props = {
  data: any;
  onDeleteSticker?: (data: any) => void;
  onStickerChange?: (data: any, pos: number) => void;
  isActive?: boolean;
  onStickerClick?: () => void;
  currentIndex: number;
};

const StickerItem: FC<Props> = ({
  data: stickerData,
  onDeleteSticker,
  onStickerChange,
  isActive = false,
  onStickerClick,
  currentIndex,
}) => {
  const [size, setSize] = useState({ width: 80, height: 80 });
  const [isResizing, setIsResizing] = useState(false);
  const [position, setPosition] = useState(stickerData);
  const [initialPinchDistance, setInitialPinchDistance] = useState<
    number | null
  >(null);
  const [initialSize, setInitialSize] = useState<{
    width: number;
    height: number;
  } | null>(null);
  const nodeRef = useRef(null);

  useEffect(() => {
    if (isActive) {
      const handleGlobalTouchStart = (e: TouchEvent) => {
        if (e.touches.length === 2) {
          e.preventDefault();
          setIsResizing(true);

          const distance = getDistance(e.touches[0], e.touches[1]);
          setInitialPinchDistance(distance);
          setInitialSize({
            width: size.width,
            height: size.height,
          });
        }
      };

      const handleGlobalTouchMove = (e: TouchEvent) => {
        if (!initialPinchDistance || !initialSize) return;

        if (e.touches.length === 2) {
          const distance = getDistance(e.touches[0], e.touches[1]);
          const scale = distance / initialPinchDistance;
          const sensitivity = 0.3;
          const adjustedScale = 1 + (scale - 1) * sensitivity;
          const maxScale = 3;
          const minScale = 0.3;
          const boundedScale = Math.min(
            Math.max(adjustedScale, minScale),
            maxScale
          );

          requestAnimationFrame(() => {
            setSize({
              width: Math.round(initialSize.width * boundedScale),
              height: Math.round(initialSize.height * boundedScale),
            });
          });
        }
      };

      const handleGlobalTouchEnd = () => {
        if (initialPinchDistance !== null) {
          setInitialPinchDistance(null);
          setInitialSize(null);
          setIsResizing(false);

          onStickerChange?.({
            ...stickerData,
            ...position,
            width: size.width,
            height: size.height,
          }, currentIndex);
        }
      };

      document.addEventListener("touchstart", handleGlobalTouchStart, {
        passive: false,
      });
      document.addEventListener("touchmove", handleGlobalTouchMove, {
        passive: false,
      });
      document.addEventListener("touchend", handleGlobalTouchEnd, {
        passive: false,
      });

      return () => {
        document.removeEventListener("touchstart", handleGlobalTouchStart);
        document.removeEventListener("touchmove", handleGlobalTouchMove);
        document.removeEventListener("touchend", handleGlobalTouchEnd);
      };
    }
  }, [
    isActive,
    initialPinchDistance,
    initialSize,
    size,
    onStickerChange,
    stickerData,
    position,
    currentIndex,
  ]);

  const handleDrag = (_: any, data: any) => {
    setPosition({
      x: data?.x,
      y: data?.y,
    });
  };

  const handleDragStop = (_: any, data: any) => {
    const finalPosition = { x: data.x, y: data.y };
    onStickerChange?.({
      ...stickerData,
      ...finalPosition,
    }, currentIndex);
  };

  const getDistance = (touch1: React.Touch, touch2: React.Touch) => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  };

  return (
    <Draggable
      nodeRef={nodeRef}
      position={position}
      onDrag={handleDrag}
      onStop={handleDragStop}
      bounds="parent"
      disabled={isResizing || !isActive}
      enableUserSelectHack={false}
    >
      <div
        ref={nodeRef}
        className={`absolute ${isActive ? "cursor-move" : ""} z-40`}
        onClick={() => onStickerClick?.()}
      >
        <div
          onDragStart={(e) => e.preventDefault()}
          className={`relative ${isActive ? "border border-solid border-primary" : ""}`}
          style={{
            touchAction: "none",
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
          }}
        >
          {isActive && (
            <>
              <div className="flex flex-col items-center absolute left-1/2 -translate-x-1/2 -top-[2.8rem]">
                <button
                  className="w-10 h-10 cursor-pointer border-2 border-solid rounded-full border-primary bg-white flex items-center justify-center shadow-md hover:bg-gray-50 active:bg-gray-100"
                  onTouchStart={(e: any) => {
                    e.stopPropagation();
                    if (!isResizing) {
                      onDeleteSticker?.(stickerData);
                    }
                  }}
                  onClick={(e: any) => {
                    e.stopPropagation();
                    if (!isResizing) {
                      onDeleteSticker?.(stickerData);
                    }
                  }}
                  style={{
                    touchAction: "none",
                    WebkitTapHighlightColor: "transparent",
                    WebkitUserSelect: "none",
                    userSelect: "none",
                  }}
                >
                  <SIcon
                    name="TrashIcon"
                    className="w-5 h-5"
                    color="primary_color"
                  />
                </button>
                <div className="w-[2px] h-2.5 bg-primary" />
              </div>
            </>
          )}

          <img
            key={stickerData?.id}
            src={stickerData?.image}
            draggable={false}
            style={{
              width: `${size.width}px`,
              height: `${size.height}px`,
              objectFit: "contain",
              touchAction: "none",
              WebkitUserSelect: "none",
              userSelect: "none",
            }}
          />
        </div>
      </div>
    </Draggable>
  );
};

export default StickerItem;
