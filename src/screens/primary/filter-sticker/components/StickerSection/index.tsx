import SText from "@/components/atoms/SText";
import StickerSlider from "@/components/molecules/StickerSlider";
import { useOrder } from "@/hooks/useOrder";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { useState } from "react";

const StickerSection = ({ setCurrentIndex, isPrinting }: any) => {
  const [position, setPosition] = useState(0);
  const { filterData, setFilterData } = useOrder();
  const { data } = useWaitingScreen();
  const stickers = data?.clientAppGetStickers ?? [];

  const onStickerChange = (stickerData: any) => {
    // Ensure we have an array to work with
    const currentStickers = filterData?.stickers || [];

    // Add new sticker to the array
    const updatedStickers = [
      ...currentStickers,
      {
        image: stickerData.image,
        x: 0, // default position
        y: 0,
        scale: 1, // default scale
        rotation: 0, // default rotation
        ...stickerData,
      },
    ];

    setFilterData({
      ...filterData,
      stickers: updatedStickers,
    });
    setCurrentIndex(updatedStickers.length - 1);
  };

  if (!stickers?.length) return null;

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-2">
        <SText type="main" className="text-[30px] font-bold">
          Nhãn dán
        </SText>
        <SText type="sub" className="text-[20px]">
          Sticker
        </SText>
      </div>

      <StickerSlider
        currentIndex={position}
        onSlideChange={setPosition}
        onStickerChange={onStickerChange}
        stickers={stickers}
        isPrinting={isPrinting}
      />
    </div>
  );
};

export default StickerSection;
