import "@/assets/main.css";
import "react-toastify/dist/ReactToastify.css";
import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";

import { SLoading } from "@/components/atoms/SLoading";
import { ROUTES } from "@/cores/routes";
import initializeApollo from "@/hooks/useApollo";
import { useApp } from "@/hooks/useApp";
import { useAuth } from "@/hooks/useAuth";
import { useOrder } from "@/hooks/useOrder";
import useRemotePrint from "@/hooks/useRemotePrint";
import useSyncMachine from "@/hooks/useSyncMachine";
import { useAppSelector } from "@/redux/hook";
import { ApolloProvider } from "@apollo/client";
import { lazy, Suspense, useEffect, useMemo } from "react";
import {
  createHashRouter,
  Outlet,
  RouterProvider,
  useLocation,
  useNavigate,
} from "react-router-dom";
import { ToastContainer } from "react-toastify";
import CountdownSuccess from "./countdown-success";
const Login = lazy(() => import("./login"));
const Home = lazy(() => import("./home"));
const Topics = lazy(() => import("./topics"));
const SizeSelection = lazy(() => import("./size-selection"));
const TotalSelection = lazy(() => import("./total-selection"));
const PaymentMethod = lazy(() => import("./payment-method"));
const CashPayment = lazy(() => import("./cash"));
const QRPayment = lazy(() => import("./qr"));
const CaptureMode = lazy(() => import("./capture-mode"));
const Capture = lazy(() => import("./capture"));
const FrameSelection = lazy(() => import("./frame-selection"));
const SelectImage = lazy(() => import("./select-image"));
const FilterSticker = lazy(() => import("./filter-sticker"));
const FinalResult = lazy(() => import("./final-result"));
const SyncData = lazy(() => import("./sync-data"));
const Countdown = lazy(() => import("./countdown"));
import STheme from "@/components/molecules/STheme";
import useReupload from "@/hooks/useReupload";

const RootLayout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isSynced } = useApp();
  const { setDraftOrder, reset } = useOrder();
  const { isLoggedIn } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (isLoggedIn) {
      if (!isSynced) {
        navigate(ROUTES.SYNC_DATA);

        return;
      }

      if (location.pathname === ROUTES.LOGIN || location.pathname === "/") {
        navigate(ROUTES.HOME);
      }

      return;
    }

    navigate(ROUTES.LOGIN);
  }, [location.pathname, isSynced, isLoggedIn]);

  useEffect(() => {
    const unsubscribe = window.api.onPrimaryNavigate(
      (_event: any, path: string) => {
        navigate(path);
      }
    );

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const handleTimelapseComplete = (_event: any, payload: any) => {
      console.log("[PrimaryApp] handleTimelapseComplete", payload);

      setDraftOrder({
        timelapseVideoUrl: payload.videoUrl,
      });
    };

    const unsubscribe = window.api.onTimelapseCompleted(
      handleTimelapseComplete
    );

    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    const handleIdleState = async (_event: any) => {
      navigate(ROUTES.HOME, {
        replace: true,
      });

      setTimeout(() => {
        reset();
      }, 1000);
    };

    const unsubscribeIdleState =
      window.api.onSubscribeToIdleState(handleIdleState);

    return () => {
      unsubscribeIdleState();
    };
  }, []);

  return <Outlet />;
};

const router = createHashRouter([
  {
    path: "/",
    element: <RootLayout />,
    children: [
      {
        path: ROUTES.LOGIN,
        element: <Login />,
      },
      {
        path: ROUTES.HOME,
        element: <Home />,
      },
      {
        path: ROUTES.TOPICS,
        element: <Topics />,
      },
      {
        path: ROUTES.SIZE_SELECTION,
        element: <SizeSelection />,
      },
      {
        path: ROUTES.TOTAL_SELECTION,
        element: <TotalSelection />,
      },
      {
        path: ROUTES.PAYMENT_METHOD,
        element: <PaymentMethod />,
      },
      {
        path: ROUTES.CASH_PAYMENT,
        element: <CashPayment />,
      },
      {
        path: ROUTES.QR_PAYMENT,
        element: <QRPayment />,
      },
      {
        path: ROUTES.CAPTURE_MODE,
        element: <CaptureMode />,
      },
      {
        path: ROUTES.CAPTURE,
        element: <Capture />,
      },
      {
        path: ROUTES.FRAME_SELECTION,
        element: <FrameSelection />,
      },
      {
        path: ROUTES.SELECT_IMAGE,
        element: <SelectImage />,
      },
      {
        path: ROUTES.FILTER_STICK,
        element: <FilterSticker />,
      },
      {
        path: ROUTES.FINAL_RESULT,
        element: <FinalResult />,
      },
      {
        path: ROUTES.SYNC_DATA,
        element: <SyncData />,
      },
      {
        path: ROUTES.COUNT_DOWN,
        element: <Countdown />,
      },
      {
        path: ROUTES.COUNT_DOWN_SUCCESS,
        element: <CountdownSuccess />,
      },
    ],
  },
]);

const App = () => {
  useSyncMachine();
  useRemotePrint();
  useReupload();

  const { accessToken } = useAuth();

  const client = useMemo(
    () => initializeApollo({}, { accessToken }),
    [accessToken]
  );

  return (
    <ApolloProvider client={client}>
      <Suspense fallback={<SLoading />}>
        <RouterProvider
          future={{ v7_startTransition: true }}
          router={router}
          fallbackElement={<SLoading />}
        />
      </Suspense>
      <STheme />

      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </ApolloProvider>
  );
};

export default App;
