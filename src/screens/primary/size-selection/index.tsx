import LargeSize from "@/assets/size/large-size.png";
import SmallSize from "@/assets/size/small-size.png";
import { SButton } from "@/components/atoms";
import SText from "@/components/atoms/SText";
import SIconButton from "@/components/molecules/SIconButton";
import Layout from "@/components/templates/Layout";
import { formatCurrencyVND } from "@/cores/helpers";
import { ROUTES } from "@/cores/routes";
import { useOrder } from "@/hooks/useOrder";
import { useWaitingScreen } from "@/hooks/useWaitingScreen";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";

const SizeSelection = () => {
  const navigate = useNavigate();
  const { setSize, layoutItem, setFrame } = useOrder();
  const { data } = useWaitingScreen();
  const extraFee = useMemo(
    () => layoutItem?.topic?.extraFee ?? 0,
    [layoutItem]
  );

  const { smallSizePrice, largeSizePrice } = useMemo(() => {
    const minimumSize = data?.clientAppGetSettingSizes[0] ?? {};

    return {
      smallSizePrice: minimumSize.smallSizePrice2,
      largeSizePrice: minimumSize.largeSizePrice2,
    };
  }, [data?.clientAppGetSettingSizes]);

  const handleSetSize = (size: string) => {
    setSize(size);
    setFrame(null);

    const timer = setTimeout(() => {
      navigate(ROUTES.FRAME_SELECTION);
    }, 500);

    return () => clearTimeout(timer);
  };

  return (
    <Layout
      showLogo={false}
      title="VUI LÒNG CHỌN CỠ ẢNH"
      description="Please choose the size of photo"
    >
      <div className="flex flex-row gap-[60px] flex-1 justify-center items-center">
        <SButton
          onClick={() => handleSetSize("small")}
          className="w-[500px] h-[500px] rounded-xl flex flex-col items-center justify-center"
        >
          <img
            src={SmallSize}
            alt="small-size"
            className="w-[145px] h-[202px] object-contain"
          />

          <SText type="main" className="mt-[54px] text-[38px] font-bold">
            Khổ nhỏ 1+1
          </SText>
          <SText type="main" className="text-[24px]">
            Small size 1+1
          </SText>

          <div className="mt-[6px] h-16 px-14 bg-background_color flex justify-center items-center rounded-full">
            <SText type="main" className="text-[38px] font-bold">
              {`${formatCurrencyVND(smallSizePrice + extraFee)}`}
            </SText>
            <SText type="main" className="text-[22px] font-bold">
              đ
            </SText>
          </div>
        </SButton>

        <SButton
          onClick={() => handleSetSize("big")}
          className="w-[500px] h-[500px] rounded-xl flex flex-col items-center justify-center"
        >
          <img
            src={LargeSize}
            alt="large-size"
            className="w-[243px] h-[208px] object-contain"
          />

          <SText type="main" className="mt-[54px] text-[38px] font-bold">
            Khổ lớn 1+1
          </SText>
          <SText type="main" className="text-[24px]">
            Big size 1+1
          </SText>

          <div className="mt-[6px] h-16 px-14 bg-background_color flex justify-center items-center rounded-full">
            <SText type="main" className="text-[38px] font-bold">
              {`${formatCurrencyVND(largeSizePrice + extraFee)}`}
            </SText>
            <SText type="main" className="text-[22px] font-bold">
              đ
            </SText>
          </div>
        </SButton>
      </div>

      <div className="flex flex-row items-center justify-center">
        <SIconButton
          LeftIcon={"SArrowLeft"}
          className="w-[250px] h-[65px] bg-primary"
          left={{
            color: "primary_text_color",
            size: 27,
          }}
          text="BACK"
          onClick={() => {
            navigate(ROUTES.TOPICS);
          }}
        />
      </div>
    </Layout>
  );
};

export default SizeSelection;
