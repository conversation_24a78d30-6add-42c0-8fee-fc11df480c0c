import { ECaptureMode } from "@/cores/enums";
import { useDimensions } from "@/hooks/useDimensions";
import { useLiveView } from "@/hooks/useLiveView";
import { useOrder } from "@/hooks/useOrder";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { CaptureView } from "./view";
import { useApp } from "@/hooks/useApp";
import _ from "lodash";
import { useAuth } from "@/hooks/useAuth";
import { apiUrl } from "@/cores/utils/config";
import { AUTO_TIME, isMultipleCamera, MANUAL_TIME } from "@/cores/constants";

type CaptureProps = {
  handleSubmit: () => void;
  source: "primary" | "secondary";
};

const Capture = ({ handleSubmit, source }: CaptureProps) => {
  const timelapseRef = useRef<any>(null);
  const { accessToken } = useAuth();
  const { isResetAutoCaptureTimer, setIsResetAutoCaptureTimer } = useApp();
  const isRenderingRef = useRef(false);
  const canvasRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const bufferCanvasRef = useRef<HTMLCanvasElement>(null);
  const renderTimeoutRef = useRef<number>();
  const frameCanvasRef = useRef<HTMLCanvasElement>(null);
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null);
  const [isCameraNotConnected, setIsNotCameraConnected] = useState(false);
  const [reconnectCameraCountdown, setReconnectCameraCountdown] = useState(0);
  const {
    frame,
    captureMode,
    setCaptureMode,
    photos,
    photoPaths,
    currentPosition,
    setCurrentPosition,
    updatePhotos,
    updatePhotoPaths,
    draftOrder,
  } = useOrder();
  const [countdown, setCountdown] = useState<number>(
    captureMode === ECaptureMode.AUTO ? AUTO_TIME : MANUAL_TIME
  );

  const photoHandler = (localUrl: any) => {
    updatePhotos([...photos, localUrl]);
    updatePhotoPaths([...photoPaths, localUrl]);

    console.log("[photoHandler] currentPosition:", currentPosition);
    console.log("[photoHandler] frame?.numberPicture:", frame?.numberPicture);

    if (currentPosition < frame?.numberPicture) {
      setCurrentPosition(currentPosition + 1);
    }

    if (captureMode === ECaptureMode.AUTO && photos.length < defaultLimit) {
      setCountdown(AUTO_TIME);
    }
  };

  const resetCountdown = async () => {
    if (captureMode === ECaptureMode.AUTO) {
      setCountdown(AUTO_TIME);
    }
  };

  const defaultLimit = useMemo(
    () => frame?.numberPicture ?? 8,
    [frame?.numberPicture]
  );

  const frameItems =
    frame?.frameItems?.filter((item: any) => item.itemType === "SubFrame") ??
    [];

  const selectedPosition = useMemo(() => {
    if (!frameItems.length) return null;

    const iPosition = currentPosition % frameItems.length;
    return frameItems[iPosition];
  }, [frame, currentPosition]);

  const liveViewDimensions = useMemo(() => {
    return {
      naturalWidth: 6000,
      naturalHeight: 4000,
      displayWidth: window.innerWidth - 80 - 220 * 2,
      displayHeight: window.innerHeight - 120,
    };
  }, []);

  const dimensions = useDimensions(liveViewDimensions, selectedPosition);

  const {
    videoRef,
    renderLiveView,
    renderCameraSelector,
    isLoading,
    activeCameraIndex,
    onCapturePhoto,
  } = useLiveView({
    containerRef,
    photos,
    canvasRef,
    photoHandler,
    frame,
    defaultLimit,
    setCountdown,
    resetCountdown,
    captureMode,
  });

  // Render vào buffer canvas
  const renderToBuffer = useCallback(() => {
    if (
      !bufferCanvasRef.current ||
      !frame ||
      !dimensions.frame.scaled ||
      !selectedPosition ||
      !dimensions.liveView.width ||
      !dimensions.liveView.height
    ) {
      return;
    }

    const canvas = bufferCanvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas dimensions before drawing
    canvas.width = Math.max(dimensions.liveView.width, 1);
    canvas.height = Math.max(dimensions.liveView.height, 1);
    const { scaled, frameItem: pos } = dimensions.frame;

    return new Promise<void>((resolve) => {
      const img = new Image();
      img.onload = () => {
        if (canvas.width === 0 || canvas.height === 0) {
          resolve();
          return;
        }

        const posAngle = selectedPosition.angle ?? 0;
        const postNumAngle = Number(posAngle);
        const angle = _.isNumber(postNumAngle) ? postNumAngle : 0;

        const angleRad = (angle * Math.PI) / 180;

        const frameCenterX =
          selectedPosition.x_coordinate + selectedPosition.width / 2; // 123 + 292/2 = 269
        const frameCenterY =
          selectedPosition.y_coordinate + selectedPosition.height / 2; // 158 + 334/2 = 325

        // Tạo canvas tạm
        const tempCanvas = document.createElement("canvas");
        tempCanvas.width = img.width;
        tempCanvas.height = img.height;
        const tempCtx = tempCanvas.getContext("2d");

        // Vẽ ảnh gốc lên canvas tạm và xoay quanh tâm hình học
        tempCtx.save();
        tempCtx.translate(frameCenterX, frameCenterY); // Dịch đến tâm hình học
        tempCtx.rotate(-angleRad); // Xoay ngược lại (-30 độ)
        tempCtx.drawImage(img, -frameCenterX, -frameCenterY); // Vẽ ảnh gốc
        tempCtx.restore();

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(
          tempCanvas, // 1. Nguồn ảnh (source image)
          selectedPosition.x_coordinate, // 2. Vị trí x bắt đầu cắt từ ảnh gốc
          selectedPosition.y_coordinate, // 3. Vị trí y bắt đầu cắt từ ảnh gốc
          selectedPosition.width, // 4. Chiều rộng vùng cắt từ ảnh gốc
          selectedPosition.height, // 5. Chiều cao vùng cắt từ ảnh gốc
          pos.x_coordinate, // 6. Vị trí x trên canvas để vẽ
          pos.y_coordinate, // 7. Vị trí y trên canvas để vẽ
          scaled.width, // 8. Chiều rộng của ảnh sau khi vẽ
          scaled.height // 9. Chiều cao của ảnh sau khi vẽ
        );
        resolve();
      };
      img.onerror = () => {
        resolve();
      };

      img.src = frame.imageUrl;
    });
  }, [dimensions, frame, selectedPosition]);

  // Copy từ buffer sang display canvas
  const copyBufferToDisplay = useCallback(() => {
    if (
      !frameCanvasRef.current ||
      !bufferCanvasRef.current ||
      !dimensions.liveView.width ||
      !dimensions.liveView.height
    )
      return;

    const displayCtx = frameCanvasRef.current.getContext("2d");
    if (!displayCtx) return;

    frameCanvasRef.current.width = Math.max(dimensions.liveView.width, 1);
    frameCanvasRef.current.height = Math.max(dimensions.liveView.height, 1);

    displayCtx.clearRect(
      0,
      0,
      frameCanvasRef.current.width,
      frameCanvasRef.current.height
    );
    displayCtx.drawImage(bufferCanvasRef.current, 0, 0);
  }, [dimensions]);

  // Render overlay
  const renderOverlay = useCallback(() => {
    if (!overlayCanvasRef.current || !dimensions.frame.scaled) return;

    const canvas = overlayCanvasRef.current;
    const ctx = canvas.getContext("2d", { alpha: true });
    if (!ctx) return;

    canvas.width = dimensions.liveView.width;
    canvas.height = dimensions.liveView.height;

    const { scaled, frameItem: pos } = dimensions.frame;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Vẽ overlay với opacity thấp hơn
    ctx.fillStyle = "rgba(255, 255, 255, 1)";

    // Vẽ 4 phần riêng biệt
    ctx.fillRect(0, 0, canvas.width, pos.y_coordinate); // trên
    ctx.fillRect(
      0,
      pos.y_coordinate + scaled.height,
      canvas.width,
      canvas.height - (pos.y_coordinate + scaled.height)
    ); // dưới
    ctx.fillRect(0, pos.y_coordinate, pos.x_coordinate, scaled.height); // trái
    ctx.fillRect(
      pos.x_coordinate + scaled.width,
      pos.y_coordinate,
      canvas.width - (pos.x_coordinate + scaled.width),
      scaled.height
    ); // phải
  }, [dimensions]);

  // Optimize render function to be more performant
  const render = useCallback(async () => {
    if (isRenderingRef.current) {
      return;
    }

    // Use requestAnimationFrame instead of setTimeout
    if (renderTimeoutRef.current) {
      cancelAnimationFrame(renderTimeoutRef.current);
    }

    renderTimeoutRef.current = requestAnimationFrame(async () => {
      isRenderingRef.current = true;
      try {
        await renderToBuffer();
        copyBufferToDisplay();
        renderOverlay();
      } finally {
        isRenderingRef.current = false;
      }
    });
  }, [renderToBuffer, copyBufferToDisplay, renderOverlay]);

  // Optimize cleanup function
  const cleanup = useCallback(() => {
    if (videoRef.current?.srcObject) {
      const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
      tracks.forEach((track) => track.stop());
      videoRef.current.srcObject = null;
    }

    if (renderTimeoutRef.current) {
      cancelAnimationFrame(renderTimeoutRef.current);
    }

    // Batch canvas cleanup operations
    requestAnimationFrame(() => {
      const canvasContexts = [
        canvasRef.current?.getContext("2d"),
        bufferCanvasRef.current?.getContext("2d"),
        frameCanvasRef.current?.getContext("2d"),
        overlayCanvasRef.current?.getContext("2d"),
      ];

      canvasContexts.forEach((ctx) => {
        if (ctx) {
          ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
        }
      });
    });

    isRenderingRef.current = false;
  }, []);

  // Effect để trigger render
  useEffect(() => {
    if (frame && dimensions.frame.scaled && selectedPosition) {
      render();
    }
  }, [dimensions, frame, selectedPosition, render]);

  useEffect(() => {
    if (photos.length < defaultLimit) {
      const interval = setInterval(() => {
        setCountdown((prev) => {
          const next = prev === 0 ? 0 : prev - 1;

          return next;
        });
      }, 1000);

      return () => {
        clearInterval(interval);
      };
    }
  }, [photos.length, defaultLimit]);

  useEffect(() => {
    const execute = async () => {
      // Only execute capture if this is the primary source or if we're using a single camera
      if (source === "primary" || !isMultipleCamera) {
        await onCapturePhoto();
      }
    };

    if (countdown === 0) {
      if (captureMode === ECaptureMode.AUTO) {
        execute();
        return;
      }

      setCaptureMode(ECaptureMode.AUTO);
      setCountdown(AUTO_TIME);
    }
  }, [countdown, captureMode, source]);

  useEffect(() => {
    if (photos.length >= defaultLimit) {
      const isValidSubmitTimelapse = isMultipleCamera
        ? source === "primary"
        : true;

      if (draftOrder?.id && !timelapseRef.current && isValidSubmitTimelapse) {
        window.api.stopTimelapse({
          dir: `orders/${draftOrder?.id}`,
          id: draftOrder?.id,
          token: accessToken,
          apiUrl,
          captureMode: captureMode,
        });

        timelapseRef.current = true;
      }

      cleanup();

      const timer = setTimeout(() => {
        handleSubmit();
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [photos.length, defaultLimit, cleanup]);

  useEffect(() => {
    if (isResetAutoCaptureTimer) {
      setCountdown(AUTO_TIME);
      setIsResetAutoCaptureTimer(false);
    }
  }, [isResetAutoCaptureTimer]);

  useEffect(() => {
    const unsubscribeCameraNotConnected = window.api.onCameraNotConnected(
      () => {
        setIsNotCameraConnected(true);
        setReconnectCameraCountdown(10);
      }
    );

    return () => {
      unsubscribeCameraNotConnected();
    };
  }, []);

  useEffect(() => {
    if (reconnectCameraCountdown > 0) {
      const interval = setInterval(() => {
        setReconnectCameraCountdown(reconnectCameraCountdown - 1);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [reconnectCameraCountdown]);

  useEffect(() => {
    const setupCamera = async () => {
      try {
        await window.api.restartLiveview();
      } catch (error) {
        console.log(error);
      } finally {
        if (draftOrder?.id) {
          window.api.onRegisterCaptureWorkspace(`orders/${draftOrder?.id}`);
        }

        setIsNotCameraConnected(false);
        setReconnectCameraCountdown(0);
        setIsResetAutoCaptureTimer(true);
      }
    };

    if (reconnectCameraCountdown === 0 && isCameraNotConnected) {
      setupCamera();
    }
  }, [reconnectCameraCountdown, isCameraNotConnected, draftOrder?.id]);

  // State để theo dõi số lần thử lại chụp ảnh
  const [captureRetryCount, setCaptureRetryCount] = useState<number>(0);

  useEffect(() => {
    let _timer: any;

    const unsubscribe = window.api.onCaptureError(
      (_event: any, errorData: any) => {
        console.log("[Capture] Received capture error:", errorData);

        // Kiểm tra xem có đang ở trạng thái có thể retry không
        if (countdown === 0 && photos.length < defaultLimit) {
          // Tăng số lần retry
          const newRetryCount = captureRetryCount + 1;
          setCaptureRetryCount(newRetryCount);

          // Hiển thị thông báo lỗi cho người dùng
          // TODO: Thêm UI hiển thị thông báo lỗi và đang thử lại
          if (
            captureMode === ECaptureMode.AUTO &&
            photos.length < defaultLimit &&
            countdown === 0
          ) {
            setCountdown(AUTO_TIME);
          }
        }
      }
    );

    return () => {
      if (_timer) {
        clearTimeout(_timer);
      }
      unsubscribe();
    };
  }, [
    countdown,
    photos.length,
    captureMode,
    defaultLimit,
    onCapturePhoto,
    captureRetryCount,
  ]);

  return (
    <CaptureView
      photos={photos}
      limitPicture={defaultLimit}
      countdown={countdown}
      containerRef={containerRef}
      bufferCanvasRef={bufferCanvasRef}
      frameCanvasRef={frameCanvasRef}
      overlayCanvasRef={overlayCanvasRef}
      canvasRef={canvasRef}
      onCapturePhoto={onCapturePhoto}
      isLoading={isLoading}
      renderLiveView={renderLiveView}
      isCameraNotConnected={isCameraNotConnected}
      reconnectCameraCountdown={reconnectCameraCountdown}
      renderCameraSelector={renderCameraSelector}
      activeCameraIndex={activeCameraIndex}
      source={source}
    />
  );
};

export default Capture;
