import SText from "@/components/atoms/SText";
import { RefObject } from "react";
import <PERSON><PERSON> from "lottie-react";
import loadingAnimation from "@/assets/capture-loading.json";
import { isMultipleCamera } from "@/cores/constants";

interface CaptureViewProps {
  photos: string[];
  limitPicture: number;
  countdown: number;
  containerRef: RefObject<HTMLDivElement>;
  bufferCanvasRef: RefObject<HTMLCanvasElement>;
  frameCanvasRef: RefObject<HTMLCanvasElement>;
  overlayCanvasRef: RefObject<HTMLCanvasElement>;
  canvasRef: RefObject<HTMLCanvasElement>;
  onCapturePhoto: () => Promise<void>;
  isLoading: boolean;
  renderLiveView: React.ReactNode;
  isCameraNotConnected: boolean;
  reconnectCameraCountdown: number;
  renderCameraSelector: React.ReactNode;
  activeCameraIndex: number;
  source: "primary" | "secondary";
}

export const CaptureView = ({
  photos,
  limitPicture,
  countdown,
  containerRef,
  bufferCanvasRef,
  frameCanvasRef,
  overlayCanvasRef,
  canvasRef,
  isLoading,
  renderLiveView,
  isCameraNotConnected,
  reconnectCameraCountdown,
  renderCameraSelector,
  activeCameraIndex,
  source,
}: CaptureViewProps) => {
  if (source === "primary" && activeCameraIndex === 1 && isMultipleCamera) {
    return (
      <div className="w-screen h-screen relative flex-col flex justify-center items-center gap-10">
        <div className="w-[1200px] bg-primary_color rounded-lg py-16 flex flex-col justify-center items-center">
          <SText type="main" className="text-[40px] font-bold">
            Bạn hãy nhìn lên màn hình trên cao để chụp ảnh
          </SText>
          <SText type="sub" className="text-[24px]">
            Please look up at the screen above to take a photo
          </SText>
        </div>

        <div className="w-[1200px] bg-primary_color rounded-lg py-16 flex flex-col justify-center items-center">
          <SText type="main" className="text-[40px] font-bold">
            Vui lòng không sử dụng điều khiển
          </SText>
          <SText type="sub" className="text-[24px]">
            Please do not use the remote control
          </SText>
        </div>

        {renderCameraSelector}
      </div>
    );
  }

  if (source === "secondary" && activeCameraIndex === 0 && isMultipleCamera) {
    return (
      <div className="w-screen h-screen relative flex-col flex justify-center items-center gap-10">
        <div className="w-[1200px] bg-primary_color rounded-lg py-16 flex flex-col justify-center items-center">
          <SText type="main" className="text-[40px] font-bold">
            Vui lòng nhìn vào camera đối diện để chụp ảnh
          </SText>
          <SText type="sub" className="text-[24px]">
            Please look at the camera opposite to take a photo
          </SText>
        </div>

        {renderCameraSelector}
      </div>
    );
  }

  return (
    <>
      {isLoading && (
        <div className="w-screen h-screen absolute z-50 bg-white flex-col flex justify-center items-center">
          <Lottie animationData={loadingAnimation} loop={true} />
          {isCameraNotConnected && reconnectCameraCountdown > 0 ? (
            <div className="flex flex-col justify-center items-center gap-4">
              <SText type="main" className="text-[40px] font-bold">
                Quá trình kết nối camera sẽ hoàn tất trong{" "}
                {reconnectCameraCountdown}s...
              </SText>
              <SText type="sub" className="text-[24px]">
                The camera connection process will complete in{" "}
                {reconnectCameraCountdown}s...
              </SText>
            </div>
          ) : (
            <div className="flex flex-col justify-center items-center gap-4">
              <SText type="main" className="text-[40px] font-bold">
                Hệ thống đang kết nối camera. Vui lòng chờ trong giây lát!
              </SText>
              <SText type="sub" className="text-[24px]">
                The system is connecting to the camera. Please wait a moment!
              </SText>
            </div>
          )}
        </div>
      )}
      <div className="w-screen h-screen px-[100px] py-[50px] gap-[50px] flex justify-center items-center">
        {!isLoading && (
          <div className="flex w-[200px] h-[200px] rounded-2xl border-2 border-secondary_color justify-center items-center flex-col items-end">
            <SText type="main" className="text-[30px] font-bold">
              Số ảnh
            </SText>
            <SText type="sub" className="text-[20px]">
              photos
            </SText>
            <SText type="main" className="text-[60px] font-bold mt-4">
              {photos.length}/{limitPicture}
            </SText>

            {renderCameraSelector}
          </div>
        )}

        <div
          ref={containerRef}
          className="rounded-xl overflow-hidden flex-1 relative flex justify-center items-center h-full"
        >
          {renderLiveView}
          <canvas ref={bufferCanvasRef} className="hidden" />
          <canvas
            ref={frameCanvasRef}
            className="absolute inset-0 z-10 pointer-events-none"
            style={{
              width: "100%",
              height: "100%",
            }}
          />
          <canvas
            ref={overlayCanvasRef}
            className="absolute inset-0 z-20 pointer-events-none"
            style={{
              width: "100%",
              height: "100%",
            }}
          />
          <canvas ref={canvasRef} className="hidden" />
          <div className="absolute inset-0 z-30 bg-white pointer-events-none flash-effect" />
        </div>

        {!isLoading && (
          <div className="flex w-[200px] h-[200px] rounded-2xl border-2 border-secondary_color justify-center items-center flex-col">
            <SText type="main" className="text-[30px] font-bold">
              Thời gian
            </SText>
            <SText type="sub" className="text-[20-px]">
              Countdown
            </SText>
            <SText type="main" className="text-[100px] font-bold">
              {countdown}
            </SText>
          </div>
        )}
      </div>
    </>
  );
};
