import SIcon from "@/components/atoms/SIcon";
import SText from "@/components/atoms/SText";
import Layout from "@/components/templates/Layout";
import { useEffect, useState } from "react";

type CountdownProps = {
  handleSubmit: () => void;
};

const Countdown = ({ handleSubmit }: CountdownProps) => {
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev === 0) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (countdown === 0) {
      handleSubmit();
    }
  }, [countdown]);

  return (
    <Layout showLogo={false}>
      <div className="flex w-full h-full flex-col justify-center items-center">
        <div className="px-10 h-[300px] bg-primary_color mt-10 gap-3 rounded-2xl flex flex-col items-center justify-center">
          <SText type="main" className="text-[40px] font-bold">
            VUI LÒNG NHÌN VÀO CAMERA PHÍA TRÊN
          </SText>
          <SText type="main" className="text-[40px] font-bold">
            CHỤP HÌNH TỰ ĐỘNG BẮT ĐẦU SAU 10 GIÂY
          </SText>
          <SText type="sub" className="text-[24px]">
            Please look at the camera above. Shooting starts in 10 secs
          </SText>
        </div>

        <div className="mt-[85px] flex justify-center items-center relative">
          <SIcon name="SCoundown" size={338} color="primary_color" />
          <div className="w-full h-full top-0 left-0 absolute z-50 flex justify-center items-center">
            <SText type="sub" className="text-[200px] font-bold">
              {countdown}
            </SText>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Countdown;
