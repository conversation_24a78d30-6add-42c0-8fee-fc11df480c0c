import "@/assets/main.css";
import "react-toastify/dist/ReactToastify.css";
import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";

import { SLoading } from "@/components/atoms/SLoading";
import { SECONDARY_ROUTES } from "@/cores/routes";
import initialize<PERSON>pollo from "@/hooks/useApollo";
import { useAuth } from "@/hooks/useAuth";
import { ApolloProvider } from "@apollo/client";
import { lazy, Suspense, useEffect, useMemo } from "react";
import {
  createHashRouter,
  Outlet,
  RouterProvider,
  useNavigate,
} from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { useOrder } from "@/hooks/useOrder";
const Home = lazy(() => import("./home"));
const Capture = lazy(() => import("./capture"));
const Countdown = lazy(() => import("./countdown"));
const CaptureFinished = lazy(() => import("./capture-finished"));
import STheme from "@/components/molecules/STheme";

const RootLayout = () => {
  const navigate = useNavigate();
  const { setDraftOrder } = useOrder();

  useEffect(() => {
    const unsubscribe = window.api.onSecondaryNavigate(
      (_event: any, path: string) => {
        navigate(path);
      }
    );

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const handleIdleState = async (_event: any) => {
      navigate(SECONDARY_ROUTES.HOME);
    };

    const unsubscribeIdleState =
      window.api.onSubscribeToIdleState(handleIdleState);

    return () => {
      unsubscribeIdleState();
    };
  }, []);

  useEffect(() => {
    const handleTimelapseComplete = (_event: any, payload: any) => {
      setDraftOrder({
        timelapseVideoUrl: payload.videoUrl,
      });
    };

    const unsubscribe = window.api.onTimelapseCompleted(
      handleTimelapseComplete
    );

    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    navigate(SECONDARY_ROUTES.HOME);
  }, []);

  return <Outlet />;
};

const router = createHashRouter([
  {
    path: "/",
    element: <RootLayout />,
    children: [
      {
        path: SECONDARY_ROUTES.HOME,
        element: <Home />,
      },
      {
        path: SECONDARY_ROUTES.COUNT_DOWN,
        element: <Countdown />,
      },
      {
        path: SECONDARY_ROUTES.CAPTURE,
        element: <Capture />,
      },
      {
        path: SECONDARY_ROUTES.CAPTURE_FINISHED,
        element: <CaptureFinished />,
      },
    ],
  },
]);

const App = () => {
  const { accessToken } = useAuth();

  const client = useMemo(
    () => initializeApollo({}, { accessToken }),
    [accessToken]
  );

  return (
    <ApolloProvider client={client}>
      <Suspense fallback={<SLoading />}>
        <RouterProvider
          future={{ v7_startTransition: true }}
          router={router}
          fallbackElement={<SLoading />}
        />
      </Suspense>
      <STheme />
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </ApolloProvider>
  );
};

export default App;
