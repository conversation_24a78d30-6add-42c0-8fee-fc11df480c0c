import SLogo from "@/components/atoms/SLogo";
import SText from "@/components/atoms/SText";
import Layout from "@/components/templates/Layout";

const Home = () => {
  return (
    <Layout showLogo={false}>
      <div className="flex w-full h-full flex-col justify-center items-center">
        <div className="px-10 h-[300px] bg-primary_color mt-10 gap-3 rounded-2xl flex flex-col items-center justify-center">
          <SText type="main" className="text-[40px] font-bold text-center">
            VUI LÒNG THAO TÁC VÀ THANH TOÁN TRÊN MÀN HÌNH CHÍNH
          </SText>
          <SText type="sub" className="text-[24px]">
            Please proceed and pay on the main screen
          </SText>
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="w-[250px] h-[110px]">
          <SLogo />
        </div>
      </div>
    </Layout>
  );
};

export default Home;
