import { ROUTES, SECONDARY_ROUTES } from "@/cores/routes";
import CaptureScreen from "../../common/capture";
import useSNavigate from "@/hooks/useSNavigate";
import { isMultipleCamera } from "@/cores/constants";
import { useNavigate } from "react-router-dom";

const Capture = () => {
  const navigate = useNavigate();
  const sNavigate = useSNavigate();

  const handleSubmit = () => {
    if (isMultipleCamera) {
      navigate(SECONDARY_ROUTES.CAPTURE_FINISHED);

      return;
    }

    sNavigate({
      primary: ROUTES.SELECT_IMAGE,
      secondary: SECONDARY_ROUTES.CAPTURE_FINISHED,
      at: "secondary",
    });
  };

  return <CaptureScreen source="secondary" handleSubmit={handleSubmit} />;
};

export default Capture;
