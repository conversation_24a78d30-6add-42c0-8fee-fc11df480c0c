import { LOG_LEVEL } from "../enums";

const logFunctions = {
  [LOG_LEVEL.info]: console.info,
  [LOG_LEVEL.log]: console.log,
  [LOG_LEVEL.warn]: console.warn,
  [LOG_LEVEL.error]: console.error,
};

function printLog(
  logType: LOG_LEVEL,
  msg: string,
  extra?: Record<string, unknown>
) {
  const logFunc = logFunctions[logType] || console.log;
  logFunc(`${msg}`, extra ?? "");
}

export function info(msg: string, extra?: Record<string, unknown>): void {
  printLog(LOG_LEVEL.info, msg, extra);
}

export function log(msg: string, extra?: Record<string, unknown>): void {
  printLog(LOG_LEVEL.log, msg, extra);
}

export function warn(msg: string, extra?: Record<string, unknown>): void {
  printLog(LOG_LEVEL.warn, msg, extra);
}

export function error(msg: string, extra?: Record<string, unknown>): void {
  printLog(LOG_LEVEL.error, msg, extra);
}

export default {
  info,
  log,
  warn,
  error,
};
