import fs from "fs";
import https from "https";
import fsPromises from "fs/promises";

export const downloadImage = async (params: any) => {
  const { url, localUrl } = params;

  return new Promise<boolean>((resolve, reject) => {
    const options = {
      family: 4,
      timeout: 10000,
    };

    https.get(url, options, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`[downloadImage] Failed to download: ${response.statusCode}`));
        return;
      }

      const fileStream = fs.createWriteStream(localUrl);
      response.pipe(fileStream);

      fileStream.on("finish", () => {
        fileStream.close();
        resolve(true);
      });

      fileStream.on("error", (err) => {
        console.log("[downloadImage] Error downloading image [1]:", err);
        fsPromises.unlink(localUrl).catch(console.error);
        
        reject(false);
      });
    });
  });
};
