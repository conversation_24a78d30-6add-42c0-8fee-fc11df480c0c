import { toast } from 'react-toastify';
import { EAlertType } from '../enums';

type TCallback = () => void;

const showAlert = (title: string, type: EAlertType, callback?: TCallback) => {
  toast(title, {
    type,
    onClose: () => callback?.(),
  });
};

export const pushSuccessAlert = (title: string, callback?: TCallback) => {
  showAlert(title, EAlertType.SUCCESS, callback);
};

export const pushWarnAlert = (title: string, callback?: TCallback) => {
  showAlert(title, EAlertType.WARN, callback);
};

export const pushErrorAlert = (title: string, callback?: TCallback) => {
  try {
    const errs = JSON.parse(title);

    if (Array.isArray(errs)) {
      errs.forEach((err: any) => pushErrorAlert(err.message));
    } else {
      showAlert(errs.message, EAlertType.ERROR, callback);
    }
  } catch (error) {
    showAlert(title, EAlertType.ERROR, callback);
  }
};

export const pushInfoAlert = (title: string, callback?: TCallback) => {
  showAlert(title, EAlertType.INFO, callback);
};
