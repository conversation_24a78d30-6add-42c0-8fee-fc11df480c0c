import path from "path";
import * as ini from "ini";
import * as fs from "fs";

export const getConfig = () => {
  try {
    // Đọc file config.ini từ thư mục chứa file thực thi
    const configPath = path.join(
      "C:",
      "Users",
      "M710q",
      "snapbox",
      "config.ini"
    );
    const config = ini.parse(fs.readFileSync(configPath, "utf-8"));
    return {
      relayPort: config.SerialPorts.RelayPort,
      billAcceptorPort: config.SerialPorts.BillAcceptorPort,
      baudRate: parseInt(config.SerialPorts.BaudRate),
      noRelay: config.SerialPorts.NoRelay ?? false,
      newRelay: config.SerialPorts.NewRelay ?? false,
      machineType: config.SerialPorts.MachineType,
      primaryScreen: config.Screens?.Primary,
      overheadScreen: config.Screens?.Overhead,
      sharedPrinterDir: config.Printer?.SharedDir,
      enableMultipleCameras: config.Cameras?.EnableMultipleCameras ?? false,
      isEnableRemoveBackground: config.AI?.RemoveBackground ?? false,
    };
  } catch (error) {
    console.error("Error reading config:", error);
    // Giá trị mặc định nếu không đọc được config
    return {
      relayPort: "COM4",
      billAcceptorPort: "COM5",
      baudRate: 9600,
      machineType: "overhead",
      primaryScreen: "AAA",
      overheadScreen: "BBB",
      noRelay: false,
      newRelay: false,
      enableMultipleCameras: false,
      isEnableRemoveBackground: false,
    };
  }
};

const env = process.env.NODE_ENV as "development" | "production";
const API_URL = {
  development: process.env.DEV_API_URL,
  production: process.env.API_URL,
};

export const apiUrl = API_URL[env];
