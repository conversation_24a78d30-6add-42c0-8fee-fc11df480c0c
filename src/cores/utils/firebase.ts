import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

type Environment = "development" | "production";

const CONFIG: Record<Environment, any> = {
  development:  {
    apiKey: "AIzaSyD5ofQuR9M7rf-lk0e0Cm7AlA13pZDxCGk",
    authDomain: "snapbox-dev.firebaseapp.com",
    projectId: "snapbox-dev",
    storageBucket: "snapbox-dev.firebasestorage.app",
    messagingSenderId: "730116746317",
    appId: "1:730116746317:web:1f48f99004e9d032640004",
    measurementId: "G-5M81KHRYXT",
  },
  production:  {
    apiKey: "AIzaSyC25404xEogqKKCAuS2xjNG4LCq14o0t5s",
    authDomain: "snapbox-eecbe.firebaseapp.com",
    projectId: "snapbox-eecbe",
    storageBucket: "snapbox-eecbe.firebasestorage.app",
    messagingSenderId: "993913466532",
    appId: "1:993913466532:web:7ec326ac6e982d7bf28031",
    measurementId: "G-WC9LH57QC1"
  }
}

const env = process.env.NODE_ENV as Environment;
const firebaseConfig = CONFIG[env];
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);

export { auth, db, storage };
