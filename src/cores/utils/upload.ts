import { createReadStream, readFile } from "fs";
import mime from "mime-types";
import fsPromises from "fs/promises";
import path from "path";
import { Agent, FormData, fetch } from "undici";

export const retryUpload = async (options: any, retries = 5, delay = 1000) => {
  const variables = {
    input: { orderId: options.orderId, captureMode: options.captureMode },
  };

  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      console.log(
        `[${options.workerName}] Upload attempt ${attempt + 1}/${retries}`,
        JSON.stringify(options, null, 2)
      );

      const stats = await fsPromises.stat(options.fileUrl);
      console.log(`[${options.workerName}] File size: ${stats.size} bytes`);

      // Create a new FormData for each attempt
      const fileStream = await fsPromises.readFile(options.fileUrl);
      const filename = path.basename(options.fileUrl);
      const contentType = mime.lookup(filename) || "application/octet-stream";
      const form = new FormData();

      form.append(
        "operations",
        JSON.stringify({ query: options.query, variables })
      );
      form.append("map", JSON.stringify({ "0": ["variables.file"] }));
      // Chỉ truyền 3 tham số: fieldName, stream, filename
      const file = new File([fileStream], filename, { type: contentType });
      form.append("0", file, filename);

      const response = await fetch(options.apiUrl, {
        method: "POST",
        body: form,
        headers: {
          Authorization: `Bearer ${options.accessToken}`,
          "apollo-require-preflight": "true",
          "x-auth-token": "SNAPBOX-VIE-STAG",
        },
        dispatcher: new Agent({
          keepAliveTimeout: 1000,
          keepAliveMaxTimeout: 1000,
        }),
      });

      const data: any = await response.json();

      const isSuccess =
        data?.data?.clientAppCreateImage?.domain ||
        data?.data?.clientAppCreateTimeLapse?.isSuccess;

      if (isSuccess) {
        console.log(`[${options.workerName}] Upload successful`);
        return;
      }

      console.log(
        `[${options.workerName}] response.data`,
        JSON.stringify(data, null, 2)
      );

      throw new Error(
        `[${options.workerName}] Upload failed: ${JSON.stringify(data.errors)}`
      );
    } catch (error: any) {
      console.error(
        `[${options.workerName}] Upload attempt ${attempt + 1} failed:`,
        error
      );

      if (attempt === retries - 1) {
        throw error; // Last attempt failed, rethrow the error
      }

      // Calculate exponential backoff delay
      const backoffDelay = delay * Math.pow(2, attempt);
      console.log(`[${options.workerName}] Retrying in ${backoffDelay}ms...`);
      await new Promise((resolve) => setTimeout(resolve, backoffDelay));
    }
  }
};
