import { SYNC_STEPS } from "../constants";

export * from "./auth";
export * from "./subscription";

export interface FrameItem {
  type: string;
  x_coordinate: number;
  y_coordinate: number;
  width: number;
  height: number;
  itemId: number;
  itemType: string;
}

export interface Frame {
  id: number;
  imageUrl: string;
  description: string;
  numberPicture: number;
  width: number;
  height: number;
  orientation: string;
  frameItems: FrameItem[];
}

export interface PriceOption {
  id: number;
  vi_title: string;
  en_title: string;
  price: number;
}

export interface FrameItem {
  x_coordinate: number;
  y_coordinate: number;
  width: number;
  height: number;
}

export interface DimensionsState {
  liveView: {
    width: number;
    height: number;
    ratio: number;
    natural: {
      width: number;
      height: number;
    };
  };
  frame: {
    width: number;
    height: number;
    ratio: number;
    frameItem: {
      x_coordinate: number;
      y_coordinate: number;
    };
    scaled: {
      width: number;
      height: number;
      scale: number;
    };
  };
}

export interface LiveViewDimensions {
  naturalWidth: number;
  naturalHeight: number;
  displayWidth: number;
  displayHeight: number;
}

export interface IPrintOptions {
  cutable: boolean;
  quantity: number;
}

export interface IDownloadImageParams {
  url: string;
  type: string;
  filename: string;
}

export type TSyncStep = (typeof SYNC_STEPS)[number];

export type MergePhotosToFrameParams = {
  framePath: string;
  photoMaps: any;
  positions: any[];
  dir: string;
  filename: string;
  filterType?: string;
  stickers?: any[];
  viewWidth: number;
  viewHeight: number;
  dates?: any[];
  qrCodes?: any[];
  qrUrl?: string;
  dateColor?: string;
  background?: any;
};

export type MergePhotosToFrameResult = {
  localPath: string;
  withoutQRLocalPath: string;
};
