export type IdleStateCallback = (event: any) => void;
export type UpdateOrderCallback = (event: any, zipPath: string) => void;
export type UpdateOrderDirectCallback = (zipPath: string) => void;
export type TakePictureCallback = (event: any, payload: any) => void;
export type LiveViewCallback = (event: any, payload: any) => void;
export type TimelapseCompleteCallback = (event: any, payload: any) => void;
export type BillDetectedCallback = (event: any, bill: any) => void;
export type RouterCallback = (event: any, path: string) => void;
export type StopTimelapseParameters = {
  dir: string;
  id: string;
  token: string;
  apiUrl: string;
  captureMode: string;
};

export type CameraNotConnectedCallback = (event: any, data: any) => void;
export type CamerasListCallback = (event: any, cameras: CameraInfo[]) => void;
export type CameraSwitchedCallback = (
  event: any,
  data: { success: boolean; cameraIndex: number }
) => void;
export type CameraErrorCallback = (event: any, error: any) => void;

export interface CameraInfo {
  index: number;
  name: string;
}

export type ReuploadParameters = {
  orderId: string;
  apiUrl: string;
  accessToken: string;
};
