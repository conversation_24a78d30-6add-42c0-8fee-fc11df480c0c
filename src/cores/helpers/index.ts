import { ClassNameValue, twMerge } from "tailwind-merge";
import clsx from "clsx";

import { TSColorName, sColors } from "@/cores/theme/colors";

export const cn = (...inputs: ClassNameValue[]) => {
  return twMerge(clsx(inputs));
};

export const sColorFrom = (color: TSColorName) => {
  return sColors[color];
};

export async function executeRequest<T>(
  requestFunc: () => Promise<T>
): Promise<T | null> {
  try {
    const response = await requestFunc();
    return response;
  } catch (error) {
    console.error("Error occurred:", error);
    return null;
  }
}

export const returnFilterStyling = (type: string) => {
  switch (type) {
    case "mono":
    case "mix":
      return {
        filter: "grayscale(100%)",
      };

    case "vivid_warm":
      return {
        filter: "contrast(1.3) saturate(1.2) brightness(0.9)",
      };

    case "vivid_cool":
      return {
        filter: "contrast(1.1) saturate(0.8) brightness(0.9) hue-rotate(10deg)",
      };

    case "dramatic":
      return {
        filter:
          "contrast(145%) saturate(80%) brightness(90%) sepia(20%) hue-rotate(-10deg)",
      };

    default:
      return {};
  }
};

export const captureViaMacbookWebcam = async (canvas: any, video: any) => {
  const ctx = canvas.getContext("2d");
  canvas.width = video.videoWidth;
  canvas.height = video.videoHeight;

  if (!ctx) return;

  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
  const imageData = canvas.toDataURL("image/png");

  return imageData;
};

export function formatCurrencyVND(value?: number): string {
  if (!value && typeof value !== "number") {
    return "0";
  }

  return new Intl.NumberFormat("vi-VN", {
    style: "decimal",
    minimumFractionDigits: 0,
    maximumFractionDigits: 3,
  }).format(value);
}

export const getNoBackgroundFileFromUrl = (url: string) => {
  const fileNames = url?.split("/");
  const fileName = fileNames[fileNames.length - 1];
  const newFileName = "no_background_" + fileName.split(".")[0] + ".png";
  const noBackgroundUrl = url?.replace(fileName, newFileName);

  return noBackgroundUrl;
};
