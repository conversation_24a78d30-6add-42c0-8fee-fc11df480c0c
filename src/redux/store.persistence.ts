import ElectronStore from "electron-store";

// Tạo schema để xác định cấu trúc dữ liệu lưu trữ
const schema = {
  redux: {
    type: "object",
  },
  // Thêm các trường kh<PERSON>c nếu cần
  lastSaved: {
    type: "number",
  },
};

// Tạo instance của electron-store để lưu trữ dữ liệu
export const snapboxStore: any = new ElectronStore<{
  redux?: any;
  lastSaved?: number;
}>({ schema });

// Hàm để lưu trữ Redux state vào electron-store
export const saveReduxState = (state: any) => {
  try {
    // Lưu trữ state
    snapboxStore.set("redux", { ...state });
    // Lưu trữ thời gian lưu trữ gần nhất
    snapboxStore.set("lastSaved", Date.now());
    return true;
  } catch (error) {
    console.error("[Store Persistence] Error saving Redux state:", error);
    return false;
  }
};

// Hàm để lấy Redux state từ electron-store
export const loadReduxState = (): any | undefined => {
  try {
    const state = snapboxStore.get("redux") as any | undefined;
    return state;
  } catch (error) {
    console.error("[Store Persistence] Error loading Redux state:", error);
    return undefined;
  }
};

// Hàm để xóa Redux state từ electron-store
export const clearReduxState = () => {
  try {
    snapboxStore.delete("redux");
    return true;
  } catch (error) {
    console.error("[Store Persistence] Error clearing Redux state:", error);
    return false;
  }
};
