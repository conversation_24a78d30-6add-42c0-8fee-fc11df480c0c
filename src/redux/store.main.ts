// store.main.ts

import { combineReducers, configureStore } from "@reduxjs/toolkit";

import appReducer from "./slices/appSlice";
import authReducer from "./slices/authSlice";
import orderReducer from "./slices/orderSlice";
import waitingScreenReducer from "./slices/waitingScreenSlice";

import { forwardToRenderer, replayActionMain } from "electron-redux";
import { loadReduxState, saveReduxState } from "./store.persistence";

// Middleware để lưu trữ state sau mỗi action
const persistenceMiddleware = (store: any) => (next: any) => (action: any) => {
  try {
    const result = next(action);
    // Lưu trữ state vào đĩa cứng sau mỗi action
    saveReduxState(store.getState());
    return result;
  } catch (error) {
    console.error("[Main Store] Error processing action:", error);
    throw error;
  }
};

const rootReducer = combineReducers({
  app: appReducer,
  auth: authReducer,
  order: orderReducer,
  waitingScreen: waitingScreenReducer,
});

// Lấy state đã lưu trữ từ đĩa cứng (nếu có)
const preloadedState = loadReduxState();

// Tạo store cho main process
const store = configureStore({
  reducer: rootReducer,
  preloadedState,
  middleware: (getDefaultMiddleware: any) =>
    getDefaultMiddleware({
      serializableCheck: false,
      immutableCheck: false, // Tắt kiểm tra immutable để tránh lỗi
    }).concat(persistenceMiddleware, forwardToRenderer), // Thêm forwardToRenderer để chuyển tiếp action đến renderer
  devTools: process.env.NODE_ENV !== "production",
});

// replayActionMain sẽ lắng nghe các action từ renderer process và áp dụng chúng vào store
try {
  replayActionMain(store); // ✅ main process sẽ replay các action từ renderer
} catch (error) {
  console.error("[Main Store] Error setting up replayActionMain:", error);
}

export default store;
