import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AuthState {
  isLoggedIn: boolean;
  id: string | null;
  machineCode: string | null;
  machineId: string | null;
  accessToken: string | null;
}

const initialState: AuthState = {
  isLoggedIn: false,
  id: null,
  machineCode: null,
  machineId: null,
  accessToken: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLoggedIn: (state, action: PayloadAction<any>) => {
      if (action.payload?.accessToken) {
        state.accessToken = action.payload.accessToken;
        state.isLoggedIn = true;
        state.id = action.payload.id;
        state.machineCode = action.payload.machineCode;
        state.machineId = action.payload.machineId;
      }
    },
    reset: () => initialState,
  },
});

export const { setLoggedIn, reset } = authSlice.actions;

export default authSlice.reducer;
