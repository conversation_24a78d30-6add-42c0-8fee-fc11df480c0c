import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ECaptureMode } from "@/cores/enums";
import { merge } from "lodash";
type TCaptureMode = ECaptureMode.AUTO | ECaptureMode.MANUAL;

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu dựa trên orderStore.ts hiện tại
interface OrderState {
  layoutItem: any;
  frame: any;
  draftOrder: any;
  size: any;
  captureMode: TCaptureMode;
  paymentMethod: any;
  photos: string[];
  photoPaths: string[];
  filterData: any;
  currentSticker: any;
  promotionCode: string;
  activeCameraIndex: number;
  currentPosition: number;
}

const initialState: OrderState = {
  layoutItem: null,
  frame: null,
  draftOrder: null,
  size: null,
  paymentMethod: null,
  captureMode: ECaptureMode.AUTO,
  photos: [],
  photoPaths: [],
  filterData: {
    qr_type: true,
    date_image: true,
    stickers: [],
    filter: null,
    backgroundIndex: 0,
  },
  currentSticker: null,
  promotionCode: "",
  activeCameraIndex: 0,
  currentPosition: 0,
};

const orderSlice = createSlice({
  name: "order",
  initialState,
  reducers: {
    setDraftOrder: (state, action: PayloadAction<any>) => {
      state.draftOrder = {
        ...state.draftOrder,
        ...action.payload,
      };
    },
    updatePhotos: (state, action: PayloadAction<any[]>) => {
      state.photos = merge(state.photos, action.payload);
    },
    updatePhotoPaths: (state, action: PayloadAction<string[]>) => {
      state.photoPaths = merge(state.photoPaths, action.payload);
    },
    setSize: (state, action: PayloadAction<any>) => {
      state.size = action.payload;
    },
    setFrame: (state, action: PayloadAction<any>) => {
      state.frame = action.payload;
    },
    setLayoutItem: (state, action: PayloadAction<any>) => {
      state.layoutItem = action.payload;
    },
    setFilterData: (state, action: PayloadAction<any>) => {
      const oldData = state.filterData ?? {};
      const newData = action.payload ?? {};

      state.filterData = Object.assign(oldData, newData);
    },
    setCaptureMode: (state, action: PayloadAction<TCaptureMode>) => {
      state.captureMode = action.payload;
    },
    setPaymentMethod: (state, action: PayloadAction<any>) => {
      state.paymentMethod = action.payload;
    },
    setPromotionCode: (state, action: PayloadAction<string>) => {
      state.promotionCode = action.payload;
    },
    setCurrentSticker: (state, action: PayloadAction<any>) => {
      state.currentSticker = action.payload;
    },
    setActiveCameraIndex: (state, action: PayloadAction<number>) => {
      state.activeCameraIndex = action.payload;
    },
    setCurrentPosition: (state, action: PayloadAction<number>) => {
      state.currentPosition = action.payload;
    },
    reset: () => initialState,
  },
});

export const {
  setDraftOrder,
  updatePhotos,
  updatePhotoPaths,
  setSize,
  setFrame,
  setLayoutItem,
  setFilterData,
  setCaptureMode,
  setPaymentMethod,
  setPromotionCode,
  setCurrentSticker,
  setActiveCameraIndex,
  setCurrentPosition,
  reset,
} = orderSlice.actions;

export default orderSlice.reducer;
