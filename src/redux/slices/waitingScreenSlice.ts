import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface WaitingScreenState {
  data: any | null;
  backupData: any | null;
  setData: (data: any) => void;
  fetchData: () => Promise<void>;
  setBackupData: (data: any) => void;
}

const initialState: WaitingScreenState = {
  data: null,
  backupData: null,
  setData: () => {},
  fetchData: () => Promise.resolve(),
  setBackupData: () => {},
};

const waitingScreenSlice = createSlice({
  name: 'waitingScreen',
  initialState,
  reducers: {
    setData: (state, action: PayloadAction<any>) => {
      state.data = action.payload
    },
    setBackupData: (state, action: PayloadAction<any>) => {
      state.backupData = action.payload;
    },
    reset: () => initialState,
  },
});

export const { setData, setBackupData, reset } = waitingScreenSlice.actions;

export default waitingScreenSlice.reducer;
