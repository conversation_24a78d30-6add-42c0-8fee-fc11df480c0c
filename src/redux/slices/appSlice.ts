import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface AppState {
  isSynced: boolean;
  loadingCounter: number;
  waitForSync: boolean;
  remotePrintJob: any;
  isResetAutoCaptureTimer: boolean;
  snapbox: any;
}

const initialState: AppState = {
  isSynced: false,
  loadingCounter: 0,
  waitForSync: false,
  remotePrintJob: null,
  isResetAutoCaptureTimer: false,
  snapbox: null,
};

const appSlice = createSlice({
  name: "app",
  initialState,
  reducers: {
    syncCompleted: (state) => {
      state.isSynced = true;
    },
    setRemotePrintJob: (state, action: PayloadAction<any>) => {
      state.remotePrintJob = action.payload;
    },
    setWaitForSync: (state, action: PayloadAction<boolean>) => {
      state.waitForSync = action.payload;
    },
    increase: (state) => {
      state.loadingCounter += 1;
    },
    decrease: (state) => {
      state.loadingCounter = Math.max(0, state.loadingCounter - 1);
    },
    setIsResetAutoCaptureTimer: (state, action: PayloadAction<boolean>) => {
      state.isResetAutoCaptureTimer = action.payload;
    },
    setSnapbox: (state, action: PayloadAction<any>) => {
      state.snapbox = action.payload;
    },
    reset: () => initialState,
  },
});

export const {
  syncCompleted,
  setRemotePrintJob,
  setWaitForSync,
  increase,
  decrease,
  reset,
  setIsResetAutoCaptureTimer,
  setSnapbox,
} = appSlice.actions;

export default appSlice.reducer;
