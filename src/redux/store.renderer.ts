import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { forwardToMain, replayActionRenderer } from "electron-redux";

import appReducer from "./slices/appSlice";
import authReducer from "./slices/authSlice";
import orderReducer from "./slices/orderSlice";
import waitingScreenReducer from "./slices/waitingScreenSlice";

// Combine tất cả reducers
const rootReducer = combineReducers({
  app: appReducer,
  auth: authReducer,
  order: orderReducer,
  waitingScreen: waitingScreenReducer,
});

// Middleware để lưu trữ state vào đĩa cứng sau mỗi action
const persistenceMiddleware = (store: any) => (next: any) => (action: any) => {
  // Dispatch action trước
  const result = next(action);

  // Lưu trữ state sau khi action đã được x<PERSON> lý
  try {
    if (
      window.api &&
      typeof window.api.reduxPersistence === "object" &&
      window.api.reduxPersistence.saveState
    ) {
      // Chỉ lưu trữ state cho một số action nhất định để tránh quá nhiều I/O
      if (
        action.type &&
        (action.type.startsWith("auth/") || action.type.startsWith("app/"))
      ) {
        window.api.reduxPersistence.saveState(store.getState());
      }
    }
  } catch (error) {
    console.error("[Persistence Middleware] Error saving state:", error);
  }

  return result;
};

// Get initial state from main process
const preloadedState = window.api.getInitialState();

// Parse the initial state
let parsedPreloadedState;
try {
  parsedPreloadedState = JSON.parse(preloadedState);
} catch (error) {
  console.error("[Renderer] Error parsing initial state:", error);
  parsedPreloadedState = {};
}

// Tạo store với middleware forwardToMain từ electron-redux
// forwardToMain sẽ chuyển tiếp các action đến main process
export const store = configureStore({
  reducer: rootReducer,
  preloadedState: parsedPreloadedState,
  middleware: (getDefaultMiddleware: any) => {
    // Thêm forwardToMain middleware vào cuối chuỗi middleware
    // Điều này đảm bảo rằng tất cả các action sẽ được xử lý bởi reducer trước
    // khi được chuyển tiếp đến main process
    const middleware = getDefaultMiddleware({
      serializableCheck: false,
      immutableCheck: false, // Tắt kiểm tra immutable để tránh lỗi
    }).concat(persistenceMiddleware, forwardToMain);
    return middleware;
  },
  devTools: process.env.NODE_ENV !== "production",
});

// Set up action replaying from main to renderer
// replayActionRenderer sẽ lắng nghe các action từ main process và áp dụng chúng vào store
try {
  replayActionRenderer(store);
} catch (error) {
  console.error("[Renderer] Error setting up replayActionRenderer:", error);
}

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
