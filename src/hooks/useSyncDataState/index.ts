import { useState } from "react";
import { useApp } from "../useApp";
import { useWaitingScreen } from "../useWaitingScreen";

export const useSyncDataState = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [errorStep, setErrorStep] = useState<number | null>(null);
  const { data, setData, setBackupData } = useWaitingScreen();
  const { syncCompleted } = useApp();

  return {
    currentStep,
    setCurrentStep,
    errorStep,
    setErrorStep,
    data,
    setData,
    setBackupData,
    syncCompleted
  };
};
