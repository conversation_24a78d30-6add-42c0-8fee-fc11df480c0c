import { useEffect, useState } from 'react';

export const useNetwork = () => {
  const [isOnline, setIsOnline] = useState<boolean>(true);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      window.api.sendNetworkStatusToMain('network-status-changed', true);
    };

    const handleOffline = () => {
      setIsOnline(false);
      window.api.sendNetworkStatusToMain('network-status-changed', false);
    };

    // Listen to browser's online/offline events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Listen to status updates from main process
    const unsubscribe = window.api.onNetworkStatusChanged((status: boolean) => {
      setIsOnline(status);
    });

    // Initial check
    window.api.checkNetwork().then(setIsOnline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    };
  }, []);

  return { isOnline };
};
