// src/hooks/useOrder.ts
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  setDraftOrder,
  updatePhotos,
  updatePhotoPaths,
  setSize,
  setFrame,
  setLayoutItem,
  setFilterData,
  setCaptureMode,
  setPaymentMethod,
  setPromotionCode,
  setCurrentSticker,
  setActiveCameraIndex,
  setCurrentPosition,
  reset as resetOrder,
} from "@/redux/slices/orderSlice";

export const useOrder = () => {
  const dispatch = useAppDispatch();
  const orderState = useAppSelector((state) => state.order);

  return {
    ...orderState,
    setDraftOrder: (payload: any) => dispatch(setDraftOrder(payload)),
    updatePhotos: (payload: any[]) => dispatch(updatePhotos(payload)),
    setCurrentPosition: (payload: number) =>
      dispatch(setCurrentPosition(payload)),
    updatePhotoPaths: (payload: string[]) =>
      dispatch(updatePhotoPaths(payload)),
    setSize: (payload: any) => dispatch(setSize(payload)),
    setFrame: (payload: any) => dispatch(setFrame(payload)),
    setLayoutItem: (payload: any) => dispatch(setLayoutItem(payload)),
    setFilterData: (payload: any) => dispatch(setFilterData(payload)),
    setCaptureMode: (payload: any) => dispatch(setCaptureMode(payload)),
    setPaymentMethod: (payload: any) => dispatch(setPaymentMethod(payload)),
    setCurrentSticker: (payload: any) => dispatch(setCurrentSticker(payload)),
    setPromotionCode: (payload: string) => dispatch(setPromotionCode(payload)),
    setActiveCameraIndex: (payload: number) =>
      dispatch(setActiveCameraIndex(payload)),
    reset: () => dispatch(resetOrder()),
  };
};
