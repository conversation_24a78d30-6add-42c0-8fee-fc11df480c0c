import { db } from "@/cores/utils/firebase";
import { doc, onSnapshot } from "firebase/firestore";
import { useEffect } from "react";
import { useApp } from "../useApp";
import { useAuth } from "../useAuth";

const useRemotePrint = () => {
  const { machineId } = useAuth();
  const { setRemotePrintJob } = useApp();

  useEffect(() => {
    if (machineId) {
      try {
        const printRef = doc(db, "ImagePrint", machineId);

        const unsubscribe = onSnapshot(
          printRef,
          (record: any) => {
            const printJob = record.data();
            if (printJob?.machineId) {
              setRemotePrintJob(printJob);
            }
          },
          (err) => {
            console.log("[useRemotePrint] Setup error:", err);
          }
        );

        return () => {
          unsubscribe();
        };
      } catch (err) {
        console.log("[useRemotePrint] Setup error:", err);
      }
    } else {
      console.log("[useRemotePrint] Missing required values:", { machineId });
    }
  }, [machineId]);
};

export default useRemotePrint;
