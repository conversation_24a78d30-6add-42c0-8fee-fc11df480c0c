// src/hooks/useAuth.ts
import { useAppDispatch, useAppSelector } from '@/redux/hook';
import {
  setLoggedIn,
  reset as resetAuth
} from '@/redux/slices/authSlice';

export const useAuth = () => {
  const dispatch = useAppDispatch();
  const authState = useAppSelector((state) => state.auth);

  return {
    ...authState,
    setLoggedIn: async (payload: any) => {
      if (payload?.accessToken) {
        dispatch(setLoggedIn(payload));
      }
    },
    reset: () => dispatch(resetAuth()),
  };
};
