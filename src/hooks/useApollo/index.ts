import { pushErrorAlert } from "@/cores/utils";
import {
  ApolloClient,
  ApolloLink,
  concat,
  DefaultOptions,
  from,
  InMemoryCache,
  NormalizedCacheObject,
} from "@apollo/client";
import { onError } from "@apollo/client/link/error";
import { RetryLink } from "@apollo/client/link/retry";
import { logger } from "@/cores/utils";
// @ts-ignore
import { createUploadLink } from "apollo-upload-client";

type Secure = {
  accessToken: string | null;
};

const env = process.env.NODE_ENV as "development" | "production";
const API_URL = {
  development: process.env.DEV_API_URL,
  production: process.env.API_URL,
};

const apiUrl = API_URL[env];
let apolloClient: ApolloClient<NormalizedCacheObject> | undefined;

const handleExtensionError = (errors: any, extension: any) => {
  const errorCode: any = extension?.error?.errorCode;
  const details: any[] = extension?.error?.details;
  logger.error("[Error]: ", extension);

  if (errorCode === "BAD_AUTHENTICATION") {
    pushErrorAlert("Sai tài khoản hoặc mật khẩu đăng nhập");

    return;
  }

  if (details?.length > 0) {
    details.forEach((detail: any) => pushErrorAlert(detail?.message));

    return;
  }

  pushErrorAlert(
    errors?.at(0)?.message || extension?.error?.message || "Lỗi không xác định"
  );
};

function createApolloClient(secure: Secure) {
  const cache = new InMemoryCache({ addTypename: false });

  const errorLink = onError(
    ({ graphQLErrors, networkError, operation, response }) => {
      const errors = response?.errors ?? [];

      if (errors.length > 0) {
        const extension: any = errors.at(0)?.extensions;

        handleExtensionError(errors, extension);
      }

      if (graphQLErrors) {
        graphQLErrors.forEach(({ message, locations, path }: any) => {
          logger.error(
            `[GraphQL error] (${
              operation.operationName
            }) Data: ${JSON.stringify(
              operation?.variables
            )} Message: ${message}, Location: ${JSON.stringify(
              locations
            )}, Path: ${path}`
          );
        });
      }

      if (networkError) {
        logger.error(
          `[Network error] (${operation.operationName}) Data: ${JSON.stringify(
            operation?.variables
          )} Message: ${networkError}`
        );

        pushErrorAlert("Hệ thống mạng không ổn định. Vui lòng kiểm tra lại");
      }
    }
  );

  const retryLink = new RetryLink({
    delay: {
      initial: 300, // Delay ban đầu (ms)
      max: Infinity, // Delay tối đa
      jitter: true, // Randomize delay để tránh thundering herd
    },
    attempts: {
      max: 5, // Số lần retry tối đa
      retryIf: (error, operation) => !!error, // Điều kiện để retry
    },
  });

  const gLink = createUploadLink({ uri: apiUrl });
  const authMiddleware = new ApolloLink((operation, forward) => {
    operation.setContext(({ headers = {} }) => ({
      headers: {
        ...headers,
        ...(secure?.accessToken
          ? { Authorization: `Bearer ${secure.accessToken}` }
          : {}),
        "Apollo-Require-Preflight": "true",
      },
    }));

    return forward(operation);
  });

  const defaultOptions: DefaultOptions = {
    watchQuery: { fetchPolicy: "no-cache", errorPolicy: "ignore" },
    query: { fetchPolicy: "no-cache", errorPolicy: "all" },
  };

  return new ApolloClient({
    link: from([retryLink, errorLink, concat(authMiddleware, gLink)]),
    cache,
    connectToDevTools: true,
    defaultOptions,
  });
}

const initializeApollo = (
  initialState = {},
  secure: Secure
): ApolloClient<NormalizedCacheObject> => {
  const _apolloClient = createApolloClient(secure);

  if (initialState) {
    const existingCache = _apolloClient.extract();
    _apolloClient.cache.restore({ ...existingCache, ...initialState });
  }

  if (typeof window === "undefined") return _apolloClient;
  if (!apolloClient) apolloClient = _apolloClient;

  return _apolloClient;
};

export default initializeApollo;
