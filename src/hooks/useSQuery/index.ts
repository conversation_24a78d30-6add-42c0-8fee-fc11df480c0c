import {
  DocumentNode,
  OperationVariables,
  TypedDocumentNode,
  useLazyQuery,
} from "@apollo/client";
import { useEffect, useMemo } from "react";
import { useApp } from "../useApp";

type Props = {
  gql: DocumentNode | TypedDocumentNode<any, OperationVariables>;
  options: {
    key: string;
    isLoadingAuto?: boolean;
    onCompleted?: (data: any) => void;
    onError?: (error: any) => void;
  };
};

const useSQuery = ({ gql, options }: Props) => {
  const { increase, decrease, snapbox } = useApp();
  const [mutate, { data, loading, error }] = useLazyQuery(gql, {
    errorPolicy: "all",
    onCompleted(data) {
      options?.onCompleted?.(data);
    },
    onError(error) {
      if (snapbox) {
        window.api.sendToLark({
          customer: snapbox?.client?.name ?? "SNAPBOX",
          machine: snapbox?.machine?.location ?? "SNAPBOX",
          code: snapbox?.machine?.machineCode ?? "SNAPBOX",
          time: new Date().toLocaleString(),
          payload: JSON.stringify({}),
          error: JSON.stringify(error),
        });
      }
      options?.onError?.(error);
    },
  });

  const graphqlError = useMemo(() => error?.graphQLErrors?.at(0), [error]);

  useEffect(() => {
    const _options = {
      isLoadingAuto: true,
      ...options,
    };

    if (_options?.isLoadingAuto) {
      loading ? increase() : decrease();
    }
  }, [loading, options.isLoadingAuto]);

  useEffect(() => {
    if (graphqlError) {
      decrease();
    }
  }, [graphqlError]);

  return {
    [`${options.key}Mutate`]: mutate,
    [`${options.key}Data`]: data,
    [`${options.key}Loading`]: loading,
    [`${options.key}Error`]: graphqlError,
  };
};

export default useSQuery;
