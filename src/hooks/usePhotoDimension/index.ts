import { getNoBackgroundFileFromUrl } from "@/cores/helpers";
import { useState, useEffect } from "react";

type Props = {
  url: string;
  rectWidth: number;
  rectHeight: number;
  background?: any;
};

const usePhotoDimension = ({
  url,
  rectWidth,
  rectHeight,
  background,
}: Props) => {
  const [result, setResult] = useState({
    cropedWidth: 0,
    cropedHeight: 0,
    croppedImage: null,
  });

  useEffect(() => {
    if (!url) return;

    const img = new Image();
    const hasBackground = !background?.fileUrl?.includes("https");

    // Thêm error handling
    img.onerror = () => {
      console.error("Failed to load image:");
    };

    img.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d", { alpha: !hasBackground });

      if (!ctx) return;

      const SCALE_FACTOR = 3;
      const isVertical = rectWidth < rectHeight;

      // Tính toán kích thước theo tỷ lệ container và ảnh
      let finalWidth, finalHeight;
      const containerRatio = rectWidth / rectHeight;
      const imageRatio = img.width / img.height;

      if (isVertical) {
        finalHeight = img.height / SCALE_FACTOR;
        finalWidth = (img.height / SCALE_FACTOR) * (rectWidth / rectHeight);
      } else {
        // Điều chỉnh logic cho trường hợp ảnh ngang
        if (imageRatio > containerRatio) {
          // Ảnh rộng hơn container theo tỷ lệ
          finalHeight = img.height / SCALE_FACTOR;
          finalWidth = finalHeight * containerRatio;
        } else {
          // Ảnh hẹp hơn container theo tỷ lệ
          finalWidth = img.width / SCALE_FACTOR;
          finalHeight = finalWidth / containerRatio;
        }
      }

      // Đảm bảo kích thước là số nguyên
      finalWidth = Math.round(finalWidth);
      finalHeight = Math.round(finalHeight);

      // Tính toán vị trí cắt để lấy phần giữa
      const sx = (img.width - finalWidth * SCALE_FACTOR) / 2;
      const sy = (img.height - finalHeight * SCALE_FACTOR) / 2;
      const sWidth = finalWidth * SCALE_FACTOR;
      const sHeight = finalHeight * SCALE_FACTOR;

      // Thiết lập kích thước canvas
      canvas.width = finalWidth;
      canvas.height = finalHeight;

      // Vẽ với chất lượng tốt nhất
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = "high";

      ctx.drawImage(
        img,
        sx,
        sy,
        sWidth,
        sHeight,
        0,
        0,
        finalWidth,
        finalHeight
      );

      // Optimize chất lượng ảnh output
      const croppedImage = canvas.toDataURL("image/png", 0.9); // Điều chỉnh quality

      setResult({
        cropedWidth: finalWidth,
        cropedHeight: finalHeight,
        croppedImage,
      });
    };

    if (hasBackground) {
      img.src = `app:///${encodeURIComponent(url)}`;
    } else {
      const noBackgroundUrl = getNoBackgroundFileFromUrl(url);
      img.src = `app:///${encodeURIComponent(noBackgroundUrl)}`;
    }

    // Cleanup
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [url, rectWidth, rectHeight, background]);

  return result;
};

export default usePhotoDimension;
