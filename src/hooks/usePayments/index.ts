import { db } from "@/cores/utils/firebase";
import { doc, onSnapshot } from "firebase/firestore";
import { useEffect, useState } from "react";
import { useAuth } from "../useAuth";
import { useOrder } from "../useOrder";

type Props = {
  refCode?: string;
  setRefCode: (refCode: string | null) => void;
};

const usePayments = ({ refCode, setRefCode }: Props) => {
  const [error, setError] = useState(null);
  const { id, machineId } = useAuth();
  const { setDraftOrder } = useOrder();
  const [isPaid, setIsPaid] = useState(false);

  useEffect(() => {
    if (id && machineId && refCode) {
      try {
        const paymentsRef = doc(db, id, machineId);

        const unsubscribe = onSnapshot(
          paymentsRef,
          (pay: any) => {
            const payment = pay.data();
            const _isValidPayment =
              payment?.orderId &&
              refCode === payment?.refCode &&
              payment?.status === "DELIVERED";

            if (_isValidPayment) {
              setRefCode(null);
              setDraftOrder({
                id: payment?.orderId,
                domain: payment?.domain,
              });

              window.api.onRegisterCaptureWorkspace(
                `orders/${payment?.orderId}`
              );
              setIsPaid(true);
            }
          },
          (err) => {
            setError(err);
          }
        );

        return () => {
          unsubscribe();
        };
      } catch (err) {
        console.log("Setup error:", err);
        setError(err);
      }
    } else {
      console.log("[usePayments] Missing required values:", {
        id,
        machineId,
        refCode,
      });
    }
  }, [id, machineId, refCode]);

  return { error, isPaid };
};

export default usePayments;
