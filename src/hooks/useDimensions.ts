import { DimensionsState, FrameItem, LiveViewDimensions } from '@/cores/types';
import { useEffect, useState } from 'react';

export const useDimensions = (
  liveViewDimensions: LiveViewDimensions,
  selectedPosition: FrameItem | null
) => {
  const [dimensions, setDimensions] = useState<DimensionsState>({
    liveView: {
      width: 0,
      height: 0,
      ratio: 0,
      natural: {
        width: 0,
        height: 0
      }
    },
    frame: {
      width: 0,
      height: 0,
      ratio: 0,
      frameItem: {
        x_coordinate: 0,
        y_coordinate: 0
      },
      scaled: {
        width: 0,
        height: 0,
        scale: 0
      }
    }
  });

  // Combine cả hai useEffect thành một
  useEffect(() => {
    if (!liveViewDimensions?.displayWidth || !liveViewDimensions?.displayHeight) {
      return;
    }

    const width = Math.round(liveViewDimensions.displayWidth);
    const height = Math.round(liveViewDimensions.displayHeight);
    
    setDimensions(prev => {
      const newLiveView = {
        width,
        height,
        ratio: width / height,
        natural: {
          width: liveViewDimensions.naturalWidth,
          height: liveViewDimensions.naturalHeight
        }
      };

      // Chỉ tính toán frame dimensions nếu có selectedPosition
      if (!selectedPosition) {
        return { ...prev, liveView: newLiveView };
      }

      const containerWidth = width;
      const containerHeight = height;

      const scaleWidth = containerWidth / selectedPosition.width;
      const scaleHeight = containerHeight / selectedPosition.height;
      const scale = Math.min(scaleWidth, scaleHeight);

      const scaledWidth = Math.round(selectedPosition.width * scale);
      const scaledHeight = Math.round(selectedPosition.height * scale);

      const x = Math.round((containerWidth - scaledWidth) / 2);
      const y = Math.round((containerHeight - scaledHeight) / 2);

      return {
        liveView: newLiveView,
        frame: {
          width: selectedPosition.width,
          height: selectedPosition.height,
          ratio: selectedPosition.width / selectedPosition.height,
          frameItem: { x_coordinate: x, y_coordinate: y },
          scaled: {
            width: scaledWidth,
            height: scaledHeight,
            scale
          }
        }
      };
    });
  }, [liveViewDimensions, selectedPosition]);
  return dimensions;
};
