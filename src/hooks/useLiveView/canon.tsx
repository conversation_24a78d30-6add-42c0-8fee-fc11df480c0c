import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useOrder } from "../useOrder";
import { CameraInfo } from "@/cores/types";
import { SCameraSelector } from "@/components/atoms";
import { ECaptureMode } from "@/cores/enums";

type Props = {
  containerRef: React.RefObject<HTMLDivElement>;
  canvasRef?: React.RefObject<HTMLCanvasElement>;
  photos: string[];
  photoHandler: (imageUrl: string) => void;
  defaultLimit: number;
  frame: any;
  setCountdown: (countdown: number) => void;
  resetCountdown: () => Promise<void>;
  captureMode: ECaptureMode;
};

export const useLiveView = ({
  containerRef,
  photos,
  photoHandler,
  defaultLimit,
  resetCountdown,
  captureMode,
}: Props) => {
  const { draftOrder, activeCameraIndex, setActiveCameraIndex } = useOrder();
  const videoRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const timerRef = useRef<any>(null);
  const [cameras, setCameras] = useState<CameraInfo[]>([]);
  const [cameraError, setCameraError] = useState<string | null>(null);

  // Lấy danh sách camera khi component mount
  useEffect(() => {
    // Xử lý danh sách camera
    const camerasListHandler = (_event: any, camerasList: CameraInfo[]) => {
      console.log("[useLiveView] Received cameras list:", camerasList);
      if (Array.isArray(camerasList) && camerasList.length > 0) {
        setCameras(camerasList);
      }
    };

    // Xử lý khi camera được chuyển đổi
    const cameraSwitchedHandler = async (
      _event: any,
      data: { success: boolean; cameraIndex: number; alreadyConnected: boolean }
    ) => {
      if (data.success) {
        setActiveCameraIndex(data.cameraIndex);

        // Reset countdown and restart from beginning using defaultLimit from settings
        await resetCountdown();

        // Chỉ hiển thị loading khi camera chưa được kết nối trước đó
        if (!data.alreadyConnected) {
          setIsLoading(true);
          console.log(
            "[useLiveView] Showing loading screen because camera was not connected before"
          );
        } else {
          console.log(
            "[useLiveView] Not showing loading screen because camera was already connected"
          );
        }
      }
    };

    // Xử lý lỗi camera
    const cameraErrorHandler = (_event: any, error: any) => {
      console.error("[useLiveView] Camera error:", error);
      setCameraError(
        typeof error === "string"
          ? error
          : error.error || "Unknown camera error"
      );
    };

    // Đăng ký các handler
    const unsubscribeCamerasList = window.api.onCamerasList(camerasListHandler);
    const unsubscribeCameraSwitched = window.api.onCameraSwitched(
      cameraSwitchedHandler
    );
    const unsubscribeCameraError = window.api.onCameraError(cameraErrorHandler);

    // Lấy danh sách camera
    const getCamerasList = async () => {
      try {
        console.log("[useLiveView] Getting cameras list");
        await window.api.getCameras();

        // Nếu sau 2 giây vẫn không nhận được danh sách camera, thử lại
        setTimeout(() => {
          if (cameras.length === 0) {
            console.log(
              "[useLiveView] No cameras received after timeout, retrying"
            );
            window.api.getCameras().catch((error) => {
              console.error(
                "[useLiveView] Error getting cameras on retry:",
                error
              );
            });
          }
        }, 2000);
      } catch (error) {
        console.error("[useLiveView] Error getting cameras:", error);
      }
    };

    getCamerasList();

    return () => {
      unsubscribeCamerasList();
      unsubscribeCameraSwitched();
      unsubscribeCameraError();
    };
  }, [cameras.length]);

  const onCapturePhoto = async () => {
    if (draftOrder?.id && photos.length < defaultLimit) {
      try {
        timerRef.current = setTimeout(() => {
          const flashElement =
            containerRef.current?.querySelector(".flash-effect");
          flashElement?.classList.add("active");
        }, 1000);

        await window.api.capture(draftOrder?.id?.toString());
      } catch (error) {
        console.error("Error capturing photo:", error);
      }
    }
  };

  // Chuyển đổi camera
  const switchCamera = useCallback((cameraIndex: number) => {
    console.log(`[useLiveView] Switching to camera at index ${cameraIndex}`);
    setIsLoading(true);
    setCameraError(null);

    window.api.switchCamera(cameraIndex).catch((error) => {
      console.error(
        `[useLiveView] Error switching to camera at index ${cameraIndex}:`,
        error
      );
      setCameraError(
        `Failed to switch camera: ${error.message || "Unknown error"}`
      );
      setIsLoading(false);
    });
  }, []);

  // Khởi động lại liveview
  const restartLiveView = useCallback(() => {
    console.log("[useLiveView] Restarting live view");
    setIsLoading(true);
    setCameraError(null);

    window.api.restartLiveview().catch((error) => {
      console.error("[useLiveView] Error restarting live view:", error);
      setCameraError(
        `Failed to restart live view: ${error.message || "Unknown error"}`
      );
      setIsLoading(false);
    });
  }, []);

  useEffect(() => {
    const handleTakePicture = (_event: any, payload: any) => {
      const flashElement = containerRef.current?.querySelector(".flash-effect");
      flashElement?.classList.remove("active");

      if (payload.localUrl) {
        photoHandler(payload.localUrl);
      }
    };

    const unsubscribe = window.api.onTakePicture(handleTakePicture);
    return () => {
      unsubscribe();

      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [photoHandler, containerRef.current, activeCameraIndex]);

  useEffect(() => {
    window.api.startTimelapse();
  }, []);

  useEffect(() => {
    const liveViewHandler = (_event: any, payload: any) => {
      if (videoRef.current) {
        if (
          typeof payload.imageDataURL === "string" &&
          payload.imageDataURL.startsWith("data:image") &&
          payload.activeCameraIndex === activeCameraIndex
        ) {
          videoRef.current.src = payload.imageDataURL;
          setIsLoading(false);
        } else {
          console.warn(
            "[useLiveView] Received invalid image URL:",
            typeof payload.imageDataURL === "string"
              ? payload.imageDataURL.substring(0, 20) + "..."
              : typeof payload.imageDataURL
          );
        }
      } else {
        console.warn("[useLiveView] Video ref is not available");
      }
    };

    // Khởi động lại liveview khi component mount
    window.api.restartLiveview().catch((error) => {
      console.error("[useLiveView] Error restarting live view:", error);
    });

    const unsubscribe = window.api.onLiveView(liveViewHandler);

    return () => {
      console.log("[useLiveView] Cleaning up live view handler");
      unsubscribe();
    };
  }, [activeCameraIndex]);

  // Render camera selector
  const renderCameraSelector = useMemo(() => {
    console.log("[useLiveView] Rendering camera selector, cameras:", cameras);

    // Chỉ hiển thị selector khi có nhiều hơn 1 camera
    if (
      !Array.isArray(cameras) ||
      cameras.length <= 1 ||
      captureMode === ECaptureMode.MANUAL
    ) {
      return null;
    }

    return <SCameraSelector onSwitchCamera={() => setIsLoading(false)} />;
  }, [cameras, activeCameraIndex, switchCamera, captureMode]);

  const renderLiveView = useMemo(() => {
    return (
      <>
        <img
          ref={videoRef}
          className="transform scale-x-[-1] object-cover"
          style={{
            position: "absolute",
            left: "50%",
            top: "50%",
            transform: "translate(-50%, -50%) scaleX(-1)",
            width: "100%", // Chỉ lớn hơn container một chút
            height: "100%",
          }}
        />
        {cameraError && (
          <div className="absolute bottom-4 left-4 right-4 bg-red-500 text-white p-2 rounded">
            {cameraError}
            <button
              className="ml-2 bg-white text-red-500 px-2 py-1 rounded"
              onClick={restartLiveView}
            >
              Retry
            </button>
          </div>
        )}
      </>
    );
  }, [cameraError, restartLiveView]);

  return {
    videoRef,
    isLoading,
    setIsLoading,
    onCapturePhoto,
    renderLiveView,
    cameras,
    activeCameraIndex,
    switchCamera,
    restartLiveView,
    renderCameraSelector,
  };
};
