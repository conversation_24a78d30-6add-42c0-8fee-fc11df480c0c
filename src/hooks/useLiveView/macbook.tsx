import fs from "fs";
import path from "path";
import { useEffect, useRef, useState } from "react";

type Props = {
  containerRef: React.RefObject<HTMLDivElement>;
  canvasRef?: React.RefObject<HTMLCanvasElement>;
  photoHandler: (jsonData: any) => void;
  photos: string[];
  frame: any;
  defaultLimit?: number;
};

export const useLiveView = ({
  canvasRef,
  photoHandler,
  photos,
  frame,
}: Props) => {
  const videoRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const onCapturePhoto = async () => {
    if (photos.length >= frame?.limit) return;
    if (!canvasRef.current || !videoRef.current) return;

    const ctx = canvasRef.current?.getContext("2d");
    canvasRef.current.width = videoRef.current?.videoWidth;
    canvasRef.current.height = videoRef.current?.videoHeight;

    if (!ctx) return;

    ctx.drawImage(
      videoRef.current,
      0,
      0,
      canvasRef.current?.width,
      canvasRef.current?.height
    );
    const base64 = canvasRef.current?.toDataURL("image/png");

    if (base64) {
      // Convert base64 to binary data
      const byteString = atob(base64.split(",")[1]);
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);

      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }

      const tempPath = await window.api.getTempPath();
      const filePath = path.join(tempPath, `take-photo-${Date.now()}.png`);
      fs.writeFileSync(filePath, Buffer.from(ab));
      photoHandler(filePath); // Pass the file path directly
    }
  };

  useEffect(() => {
    const handleLiveView = async () => {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: { ideal: 1280 }, height: { ideal: 720 } },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;

        videoRef.current.onloadedmetadata = () => {
          videoRef.current.naturalWidth = videoRef.current.videoWidth;
          videoRef.current.naturalWidth = videoRef.current.videoHeight;
          videoRef.current.complete = true;
          setIsLoading(false);
        };
      }
    };

    handleLiveView().catch((error) => {
      console.error("Error accessing camera:", error);
      setIsLoading(false);
    });

    return () => {
      if (videoRef.current?.srcObject) {
        const tracks = videoRef.current.srcObject.getTracks();
        tracks.forEach((track: any) => track.stop());
      }
    };
  }, []);

  return {
    videoRef,
    isLoading,
    setIsLoading,
    onCapturePhoto,
    renderLiveView: (
      <video
        ref={videoRef}
        className="transform scale-x-[-1] object-cover w-full h-full"
        autoPlay
        playsInline
        muted
        controls={false}
        loop
      />
    ),
  };
};
