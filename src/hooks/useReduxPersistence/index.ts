// src/hooks/useReduxPersistence/index.ts
import { RootState, store } from "@/redux/store.renderer";
import { useCallback } from "react";

// Ki<PERSON>m tra xem API có tồn tại không
const isReduxPersistenceAvailable = () => {
  return window.api && window.api.reduxPersistence;
};

export const useReduxPersistence = () => {
  // Hàm để lưu trữ Redux state vào đĩa cứng
  const saveState = useCallback(async () => {
    try {
      if (!isReduxPersistenceAvailable()) {
        return false;
      }

      const state = store.getState();
      return await window.api.reduxPersistence.saveState(state);
    } catch (error) {
      console.error("[useReduxPersistence] Error saving Redux state:", error);
      return false;
    }
  }, []);

  // Hàm để lấy Redux state từ đĩa cứng
  const loadState = useCallback(async (): Promise<RootState | undefined> => {
    try {
      if (!isReduxPersistenceAvailable()) {
        return undefined;
      }

      return await window.api.reduxPersistence.loadState();
    } catch (error) {
      console.error("[useReduxPersistence] Error loading Redux state:", error);
      return undefined;
    }
  }, []);

  // Hàm để xóa Redux state từ đĩa cứng
  const clearState = useCallback(async () => {
    try {
      if (!isReduxPersistenceAvailable()) {
        return false;
      }

      return await window.api.reduxPersistence.clearState();
    } catch (error) {
      console.error("[useReduxPersistence] Error clearing Redux state:", error);
      return false;
    }
  }, []);

  return {
    saveState,
    loadState,
    clearState,
  };
};
