import { useEffect, useState } from "react";

type Props = {
  ref: any;
  frame: any;
  frameSize: any;
}

const useFrameDimensions = ({
  ref,
  frame,
  frameSize
}: Props) => {
  const [frameDimensions, setFrameDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);

  useEffect(() => {
    const calculateDimensions = () => {
      if (!ref.current || !frame?.imageUrl) return;

      const boundRect = ref.current.getBoundingClientRect();
      const boundWidth = boundRect.width;
      const boundHeight = boundRect.height;

      const isHorizontal = frameSize.width > frameSize.height;
      let width, height;

      if (isHorizontal) {
        // For horizontal frames (1800x1200)
        width = Math.min(boundWidth, 1800);
        height = (width * frameSize.height) / frameSize.width;

        // Check if height exceeds bound height
        if (height > boundHeight) {
          height = boundHeight;
          width = (height * frameSize.width) / frameSize.height;
        }
      } else {
        // For vertical frames (1200x1800)
        height = Math.min(boundHeight, 1800);
        width = (height * frameSize.width) / frameSize.height;

        // Check if width exceeds bound width
        if (width > boundWidth) {
          width = boundWidth;
          height = (width * frameSize.height) / frameSize.width;
        }
      }

      setFrameDimensions({ width, height });
    };

    calculateDimensions();
    window.addEventListener("resize", calculateDimensions);
    return () => window.removeEventListener("resize", calculateDimensions);
  }, [frame?.imageUrl, frameSize]);
  
  return frameDimensions;
}

export default useFrameDimensions;
