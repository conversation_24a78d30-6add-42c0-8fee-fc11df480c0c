import {
  DocumentNode,
  OperationVariables,
  TypedDocumentNode,
  useMutation,
} from "@apollo/client";
import { useEffect, useMemo } from "react";
import { useApp } from "../useApp";

type Props = {
  gql: DocumentNode | TypedDocumentNode<any, OperationVariables>;
  options: {
    key: string;
    isLoadingAuto?: boolean;
    onCompleted?: (data: any, variables: any) => void;
    onError?: (error: any) => void;
  };
};

const useSMutation = ({ gql, options }: Props) => {
  const { increase, decrease, snapbox } = useApp();
  const [mutate, { data, loading, error }] = useMutation(gql, {
    notifyOnNetworkStatusChange: true,
    errorPolicy: "all",
    onCompleted: (data, clientOptions) => {
      options.onCompleted?.(data, clientOptions?.variables ?? {});
    },
    onError: (error: any, clientOptions: any) => {
      if (snapbox) {
        window.api.sendToLark({
          customer: snapbox?.client?.name ?? "SNAPBOX",
          machine: snapbox?.machine?.location ?? "SNAPBOX",
          code: snapbox?.machine?.machineCode ?? "SNAPBOX",
          time: new Date().toLocaleString(),
          payload: JSON.stringify(clientOptions?.variables ?? {}),
          error: JSON.stringify(error),
        });
      }

      options.onError?.(error);
    },
  });

  const graphqlError = useMemo(() => error?.graphQLErrors?.at(0), [error]);

  useEffect(() => {
    const _options = {
      isLoadingAuto: true,
      ...options,
    };

    if (_options?.isLoadingAuto) {
      loading ? increase() : decrease();
    }
  }, [loading, options.isLoadingAuto]);

  useEffect(() => {
    if (graphqlError) {
      decrease();
    }
  }, [graphqlError]);

  return {
    [`${options.key}Mutate`]: mutate,
    [`${options.key}Data`]: data,
    [`${options.key}Loading`]: loading,
    [`${options.key}Error`]: graphqlError,
  };
};

export default useSMutation;
