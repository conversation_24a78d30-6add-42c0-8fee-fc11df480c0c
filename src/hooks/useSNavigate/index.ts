import { isOverheadMachine } from "@/cores/constants";
import { useNavigate } from "react-router-dom";

type NavigateToProps = {
  primary?: any;
  secondary?: any;
  at: "primary" | "secondary" | "both";
};

const useSNavigate = () => {
  const navigate = useNavigate();

  const goto = (ipcNavigate: any, path: any) => {
    if (isOverheadMachine) {
      ipcNavigate(path);
    } else {
      navigate(path);
    }
  };

  const navigateTo = ({ primary, secondary, at }: NavigateToProps) => {
    switch (at) {
      case "primary":
        if (primary) {
          navigate(primary);
        }

        if (secondary) {
          goto(window.api.secondaryNavigate, secondary);
        }
        break;
      case "secondary":
        if (secondary) {
          navigate(secondary);
        }

        if (primary) {
          goto(window.api.primaryNavigate, primary);
        }
        break;
      case "both":
        if (primary) {
          navigate(primary);
        }

        if (secondary) {
          navigate(secondary);
        }
        break;
    }
  };

  return navigateTo;
};

export default useSNavigate;
