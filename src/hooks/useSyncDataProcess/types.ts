export interface ImageItem {
  url?: string;
  image?: string;
  imageUrl?: string;
  [key: string]: any;
}

export interface ApiData {
  clientAppWaitingScreen?: {
    images: ImageItem[];
    [key: string]: any;
  };
  clientAppGetSettingSizes?: any[];
  clientAppGetLayouts?: Array<{
    formats: Array<{
      layoutItems?: ImageItem[];
      [key: string]: any;
    }>;
    [key: string]: any;
  }>;
  clientAppGetFrames?: ImageItem[];
  clientAppGetStickers?: ImageItem[];
}

export interface UseSyncDataProcessProps {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  setErrorStep: (step: number | null) => void;
  syncCompleted: () => void;
}
