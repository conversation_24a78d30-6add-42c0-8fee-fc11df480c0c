import { cloneDeep } from "lodash";
import { useCallback } from "react";

interface UseSyncDataProcessProps {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  setErrorStep: (step: number | null) => void;
  onFinished: (apiData: any) => void;
  errorStep: number | null;
}

interface ImageItem {
  url?: string;
  image?: string;
  imageUrl?: string;
  [key: string]: any;
}

export const useSyncDataProcess = ({
  currentStep,
  setCurrentStep,
  setErrorStep,
  errorStep,
  onFinished,
}: UseSyncDataProcessProps) => {
  const downloadAndSaveImages = useCallback(
    async (images: ImageItem[], type: string) => {
      try {
        const imagesCopy = images.map(item => ({...item}));
        
        return await Promise.all(
          imagesCopy.map(async (item, index) => {
            const originalItem = images[index];
            const imageUrl = item.url || item.image || item.imageUrl;
            if (!imageUrl) return imageUrl;

            if (imageUrl.startsWith("https://")) {
              try {
                const filename = decodeURIComponent(imageUrl.split("/").pop() || "");
                const localPath = await window.api.downloadImage({
                  url: imageUrl,
                  type,
                  filename,
                });

                const path = localPath.slice(7);
                const encodedPath = `app:///${encodeURIComponent(path)}`;
                
                Object.keys(item).forEach(key => {
                  if (originalItem[key] === imageUrl) {
                    item[key] = encodedPath;
                  }
                });

                return encodedPath;
              } catch (error) {
                console.error(`Failed to download image: ${imageUrl}`, error);
                return imageUrl;
              }
            } else if (imageUrl.startsWith("app://")) {
              const path = imageUrl.slice(6);
              const encodedPath = `app:///${encodeURIComponent(decodeURIComponent(path))}`;
              
              Object.keys(item).forEach(key => {
                if (originalItem[key] === imageUrl) {
                  item[key] = encodedPath;
                }
              });

              return encodedPath;
            }
            
            return imageUrl;
          })
        );
      } catch (error) {
        console.error(`Error downloading images: ${error}`);

        // throw new Error(`Error downloading images: ${error}`);
      }
    },
    []
  );

  const processData = useCallback(
    async (apiData: any) => {
      if (!apiData || currentStep > 8 || errorStep) return;
      let step = currentStep || 1;

      try {
        setErrorStep(null);

        const stepProcessors: Record<number, () => Promise<void>> = {
          1: async () => {
            if (apiData.clientAppGetAppearanceSetting) {
              step++;
              setCurrentStep(step);
            }
          },
          2: async () => {
            if (apiData.clientAppWaitingScreen) {
              step++;
              setCurrentStep(step);
            }
          },
          3: async () => {
            console.log("Processing waiting screen...");
            if (!apiData.clientAppWaitingScreen?.images) {
              console.warn("No waiting screen images found");
              step++;
              setCurrentStep(step);
              return;
            }

            const images = cloneDeep(apiData.clientAppWaitingScreen.images);
            const downloadedUrls = await downloadAndSaveImages(
              images,
              "waiting-screen"
            );

            const updatedData = {
              ...apiData.clientAppWaitingScreen,
              images: images.map((img: any, index: number) => ({
                ...img,
                url: downloadedUrls[index],
              })),
            };

            await window.api.insertWaitingScreen(updatedData);
            step++;
            setCurrentStep(step);
          },
          4: async () => {
            const sizes = apiData.clientAppGetSettingSizes ?? [];
            const updatedSizes = [...sizes];

            if (updatedSizes.length) {
              await window.api.insertSizes(updatedSizes);
            }

            step++;
            setCurrentStep(step);
          },
          5: async () => {
            const layouts = apiData.clientAppGetLayouts ?? [];
            const updatedLayouts = cloneDeep(layouts);

            if (updatedLayouts.length) {
              for (let i = 0; i < updatedLayouts.length; i++) {
                const layout = updatedLayouts[i];
                for (let j = 0; j < layout.formats.length; j++) {
                  const format = layout.formats[j];
                  if (format.layoutItems?.length) {
                    const layoutItemsCopy = cloneDeep(format.layoutItems);
                    const downloadedUrls = await downloadAndSaveImages(
                      layoutItemsCopy,
                      "layouts"
                    );

                    const updatedLayoutItems = layoutItemsCopy.map(
                      (item: any, index: number) => ({
                        ...item,
                        imageUrl: downloadedUrls[index],
                      })
                    );
                    
                    format.layoutItems = updatedLayoutItems;
                  }
                }
              }

              await window.api.insertLayouts(updatedLayouts);
            }
            
            step++;
            setCurrentStep(step);
          },
          6: async () => {
            const frames = apiData.clientAppGetFrames ?? [];
            const updatedFrames = cloneDeep(frames);

            if (updatedFrames.length) {
              for (let i = 0; i < updatedFrames.length; i++) {
                const frame = updatedFrames[i];
                const downloadedUrls = await downloadAndSaveImages(
                  [frame],
                  "frames"
                );
                frame.imageUrl = downloadedUrls[0];
              }

              await window.api.insertFrames(updatedFrames);
            }

            step++;
            setCurrentStep(step);
          },
          7: async () => {
            console.log("Processing stickers...");
            const stickers = apiData.clientAppGetStickers ?? [];
            const updatedStickers = cloneDeep(stickers);

            if (updatedStickers.length) {
              for (let i = 0; i < updatedStickers.length; i++) {
                const sticker = updatedStickers[i];
                const downloadedUrls = await downloadAndSaveImages(
                  [sticker],
                  "stickers"
                );
                sticker.image = downloadedUrls[0];
              }

              await window.api.insertStickers(updatedStickers);
            }

            step++;
            setCurrentStep(step);
          },
          8: async () => {
            const printSetting = apiData.clientAppPrintSetting;

            console.log("printSetting", printSetting);
            await window.api.insertPrintSetting(printSetting);
            onFinished(apiData);
            step++;
            setCurrentStep(step);
            return;
          }
        };

        while (step <= 8 && !errorStep) {
          const processor = stepProcessors[step];
          
          if (!processor) {
            console.error(`No processor found for step ${step}`);
            break;
          }

          console.log("Processing step", step);
          await processor();
        }
      } catch (error) {
        console.error(`Error processing step ${step}:`, error);
        setErrorStep(step);
        throw new Error(`Error processing step ${step}: ${error}`);
      }
    },
    [currentStep, downloadAndSaveImages, errorStep]
  );

  return {
    processData,
    downloadAndSaveImages,
  };
};
