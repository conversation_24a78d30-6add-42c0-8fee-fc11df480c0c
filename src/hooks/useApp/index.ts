// src/hooks/useApp.ts
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  increase,
  decrease,
  syncCompleted,
  setWaitForSync,
  setRemotePrintJob,
  setIsResetAutoCaptureTimer,
  setSnapbox,
} from "@/redux/slices/appSlice";

export const useApp = () => {
  const dispatch = useAppDispatch();
  const appState = useAppSelector((state) => state.app);

  return {
    ...appState,
    increase: () => dispatch(increase()),
    decrease: () => dispatch(decrease()),
    syncCompleted: () => dispatch(syncCompleted()),
    setWaitForSync: (payload: boolean) => dispatch(setWaitForSync(payload)),
    setRemotePrintJob: (payload: any) => dispatch(setRemotePrintJob(payload)),
    setIsResetAutoCaptureTimer: (payload: boolean) =>
      dispatch(setIsResetAutoCaptureTimer(payload)),
    setSnapbox: (payload: any) => dispatch(setSnapbox(payload)),
  };
};
