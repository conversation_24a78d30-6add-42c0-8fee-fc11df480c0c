import { CLIENT_APP_WAITING_SCREEN } from "@/graphql/queries";
import useSQuery from "@/hooks/useSQuery";
interface UseSyncDataSubscriptionsProps {
  processData: (data: any) => void;
}

export const useSyncDataSubscriptions = ({
  processData,
}: UseSyncDataSubscriptionsProps) => {
  const { dummyMutate } = useSQuery({
    gql: CLIENT_APP_WAITING_SCREEN,
    options: {
      key: "dummy",
      onCompleted: (data) => {
        processData(data);
      },
    },
  });

  return { dummyMutate };
};
