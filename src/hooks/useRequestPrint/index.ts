import { useAuth } from "../useAuth";

type Props = {
  callback?: () => void;
};

const env = process.env.NODE_ENV as "development" | "production";
const API_URL = {
  development: process.env.DEV_API_URL,
  production: process.env.API_URL,
};
const apiUrl = API_URL[env];

const useRequestPrint = ({ callback }: Props) => {
  const { accessToken } = useAuth();

  const print = async (data: any) => {
    if (!data?.imgUrl) {
      return;
    }

    const payload = {
      imgUrl: data?.imgUrl,
      options: {
        quantity: data?.options?.quantity || 1,
        cutable: data?.options?.cutable || false,
        apiUrl,
        accessToken,
        paddings: {
          top: 16,
          left: 18,
          width: 1176,
          height: 1770,
        },
      },
    };

    try {
      const res = await window.api.print(payload);
      console.log("res", res);
    } catch (err) {
      console.log("err", err);
    } finally {
      callback?.();
    }
  };

  return { print };
};

export default useRequestPrint;
