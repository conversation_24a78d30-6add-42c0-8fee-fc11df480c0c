import { useMemo } from "react";

const useMixedMap = (frameItems: any) => {
  const mixedMap = useMemo(() => {
    const items = frameItems ?? [];
    const map = new Map();

    // Tạo map với key là itemId và value là true/false
    items
      ?.filter((item: any) => item.itemType === "SubFrame")
      .forEach((item: any) => {
        map.set(item.itemId, map.size % 2 !== 0);
      });

    items
      ?.filter((item: any) => item.itemType === "DubFrame")
      .forEach((item: any) => {
        const isMixed = !!map.get(item.parentId);
        map.set(item.itemId, isMixed);
      });

    return map;
  }, [frameItems]);

  return mixedMap;
};

export default useMixedMap;
