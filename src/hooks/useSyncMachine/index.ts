import { db } from "@/cores/utils/firebase";
import { doc, onSnapshot } from "firebase/firestore";
import { useEffect, useState } from "react";
import { useApp } from "../useApp";
import { useAuth } from "../useAuth";

const useSyncMachine = () => {
  const [error, setError] = useState(null);
  const { machineId } = useAuth();
  const { setWaitForSync } = useApp();

  useEffect(() => {
    if (machineId) {
      try {
        const ref = doc(
          db,
          "SyncMachines",
          machineId
        );

        const unsubscribe = onSnapshot(
          ref,
          (docRef: any) => {
            const data = docRef.data();

            if (data?.isUpdated) {
              setWaitForSync(true);
            }
          },
          (err) => {
            console.log(err);
            setError(err);
          }
        );
  
        return () => unsubscribe();
      } catch (err) {
        setError(err);
      }
    }
  }, [machineId]);

  return { error };
};

export default useSyncMachine;
