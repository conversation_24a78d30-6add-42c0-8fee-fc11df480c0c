import { DEFAULT_THEME } from "@/cores/constants";
import { useMemo } from "react";
import { useWaitingScreen } from "../useWaitingScreen";

export const useTheme = () => {
  const { data } = useWaitingScreen();

  const theme = useMemo(() => {
    if (!data?.clientAppGetAppearanceSetting?.id) {
      return DEFAULT_THEME;
    }

    return { ...DEFAULT_THEME, ...data?.clientAppGetAppearanceSetting };
  }, [data?.clientAppGetAppearanceSetting]);

  return theme;
};
