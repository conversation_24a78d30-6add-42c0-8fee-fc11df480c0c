import { db } from "@/cores/utils/firebase";
import { doc, onSnapshot } from "firebase/firestore";
import { useEffect } from "react";
import { useAuth } from "../useAuth";
import { apiUrl } from "@/cores/utils/config";

const useReupload = () => {
  const { machineId, accessToken } = useAuth();

  useEffect(() => {
    if (machineId) {
      try {
        const reupRef = doc(db, "ImageReup", machineId);
        const unsubscribe = onSnapshot(
          reupRef,
          (reup: any) => {
            const reupData = reup.data();

            if (reupData?.status) {
              console.log("[useReupload] Reuploading files:", reupData);

              window.api.reuploadFiles({
                orderId: reupData?.orderId,
                accessToken,
                apiUrl,
              });
            }
          },
          (err) => {
            console.log("[useReupload] error:", err);
          }
        );

        return () => {
          unsubscribe();
        };
      } catch (err) {
        console.log("[useReupload] error:", err);
      }
    } else {
      console.log("[useReupload] Missing required values:", {
        machineId,
      });
    }
  }, [machineId]);
};

export default useReupload;
