// src/hooks/useWaitingScreen.ts
import { useAppDispatch, useAppSelector } from '@/redux/hook';
import {
  setData,
  setBackupData,
  reset as resetWaitingScreen
} from '@/redux/slices/waitingScreenSlice';

export const useWaitingScreen = () => {
  const dispatch = useAppDispatch();
  const waitingScreenState = useAppSelector((state) => state.waitingScreen);

  return {
    ...waitingScreenState,
    setData: (payload: any) => dispatch(setData(payload)),
    setBackupData: (payload: any) => dispatch(setBackupData(payload)),
    reset: () => dispatch(resetWaitingScreen()),
  };
};
