export {};

import {
  IDownloadImageParams,
  MergePhotosToFrameParams,
  MergePhotosToFrameResult,
  IdleStateCallback,
  UpdateOrderCallback,
  LiveViewCallback,
  TakePictureCallback,
  TimelapseCompleteCallback,
  BillDetectedCallback,
  CameraNotConnectedCallback,
  RouterCallback,
  StopTimelapseParameters,
  ReuploadParameters,
} from "./src/cores/types";

declare global {
  interface Window {
    api: {
      // Camera
      onLiveView: (callback: LiveViewCallback) => () => void;
      onTakePicture: (callback: TakePictureCallback) => () => void;
      onRegisterCaptureWorkspace: (dir: string) => () => void;
      capture: (dir: string) => Promise<void>;
      print: (payloads: any) => Promise<any>;
      setupCamera: () => Promise<void>;
      onTimelapseCompleted: (callback: TimelapseCompleteCallback) => () => void;
      disconnectRelay: () => Promise<void>;
      getTempPath: () => Promise<string>;
      onCameraNotConnected: (
        callback: CameraNotConnectedCallback
      ) => () => void;
      restartLiveview: () => Promise<void>;
      turnOffCamera: () => Promise<void>;
      startTimelapse: () => Promise<void>;
      stopTimelapse: (params: StopTimelapseParameters) => Promise<void>;
      onCaptureError: (callback: CameraNotConnectedCallback) => () => void;

      // Camera Switching
      getCameras: () => Promise<void>;
      switchCamera: (cameraIndex: number) => Promise<void>;
      onCamerasList: (callback: any) => () => void;
      onCameraSwitched: (callback: any) => () => void;
      onCameraError: (callback: any) => () => void;

      // Bill Acceptor
      startBillingPolling: (callback: BillDetectedCallback) => () => void;

      // RX Database
      insertAppearence: (data: any) => Promise<void>;
      insertWaitingScreen: (data: any) => Promise<void>;
      insertLayouts: (data: any) => Promise<void>;
      insertSizes: (data: any) => Promise<void>;
      insertStickers: (data: any) => Promise<void>;
      insertFrames: (data: any) => Promise<void>;
      insertPrintSetting: (data: any) => Promise<void>;
      backup: () => Promise<void>;
      restore: (jsonPath: string) => Promise<void>;
      remove: () => Promise<void>;
      retrivedRecords: () => Promise<string>;

      downloadImage: (params: IDownloadImageParams) => Promise<string>;
      mergePhotosToFrame: (
        params: MergePhotosToFrameParams
      ) => Promise<MergePhotosToFrameResult>;
      updateOrder: (params: any) => Promise<void>;
      onUpdateOrder: (callback: UpdateOrderCallback) => () => void;
      reuploadFiles: (params: ReuploadParameters) => Promise<void>;

      // Network
      checkNetwork: () => Promise<boolean>;
      onNetworkStatusChanged: (
        callback: (status: boolean) => void
      ) => () => void;
      sendNetworkStatusToMain: (channel: any, ...args: any[]) => void;

      // Idle State
      onSubscribeToIdleState: (callback: IdleStateCallback) => () => void;

      // Router
      primaryNavigate: (path: string) => void;
      secondaryNavigate: (path: string) => void;
      onPrimaryNavigate: (callback: RouterCallback) => () => void;
      onSecondaryNavigate: (callback: RouterCallback) => () => void;

      // Lark
      sendToLark: (payloads: any) => Promise<void>;

      getInitialState: () => string;
      dispatchToMain: (action: any) => void;
      // API mới cho Redux persistence
      reduxPersistence: {
        saveState: (state: RootState) => Promise<boolean>;
        loadState: () => Promise<RootState | undefined>;
        clearState: () => Promise<boolean>;
      };

      // Password Dialog
      submitPassword: (password: string) => Promise<boolean>;
      cancelPassword: () => Promise<boolean>;
      onError: (callback: (message: string) => void) => void;
    };
  }
}
