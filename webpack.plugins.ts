import type IForkTsCheckerWebpackPlugin from 'fork-ts-checker-webpack-plugin';
import webpack from 'webpack';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const ForkTsCheckerWebpackPlugin: typeof IForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');

export const plugins = [
  new ForkTsCheckerWebpackPlugin({
    logger: {
      infrastructure: 'console',
      issues: 'console',
      devServer: true
    },
    typescript: {
      diagnosticOptions: {
        semantic: true,
        syntactic: true,
      },
      mode: 'write-references',
    },
    issue: {
      include: [
        { file: '**/src/**/*.{ts,tsx}' }
      ],
    },
  }),
  new webpack.DefinePlugin({
    'process.env.API_URL': JSON.stringify("https://api.snapboxvietnam.com/graphql"),
    'process.env.DEV_API_URL': JSON.stringify("https://api-stag.snapboxvietnam.com/graphql"),
    'process.env.NODE_OPTIONS': JSON.stringify("--no-warnings --max-old-space-size=2048"),
  }),
];
