description: Best practices for Snapbox React UI components
globs: **/*.{tsx,jsx}
alwaysApply: false
---

- Follow the atomic design pattern: atoms, molecules, organisms, templates, screens
- Create reusable components for consistent UI elements (SText, SButton, SIcon)
- Use functional components with hooks instead of class components
- Implement proper error boundaries for critical UI sections
- Create responsive layouts that adapt to different screen resolutions
- Use custom hooks to encapsulate business logic (useOrder, useLiveView, etc.)
- Implement clear user flow with guided navigation
