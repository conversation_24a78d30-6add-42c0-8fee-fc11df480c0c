description: Best practices for Snapbox state management
globs: **/*.{ts,tsx}
alwaysApply: false
---

- Use Redux slices for organizing related state (orderSlice, authSlice)
- Implement Redux-persist for critical state that needs to survive app restarts
- Use Redux Toolkit for simplified Redux implementation
- Create custom hooks for accessing Redux state (useOrder, useApp)
- Handle async operations with appropriate loading/error states
- Maintain clear separation between UI and state management logic