description: Best practices for camera, printer, relay and bill acceptor integration
globs: **/*camera*.ts, **/*printer*.ts, **/*relay*.ts, **/*bill-acceptor*.ts
alwaysApply: false
---

- Implement abstracted service classes for different hardware types
- Use event-based communication for hardware events (photo captured, print completed)
- Implement robust error handling and recovery mechanisms
- Create fallback options for hardware failures
- Use proper resource management and cleanup
- Handle reconnection attempts with exponential backoff
- Implement proper logging for hardware operations for debugging