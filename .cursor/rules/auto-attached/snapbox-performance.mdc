description: Best practices for application performance
globs: **/*.{ts,tsx}
alwaysApply: false
---

- Optimize image processing operations for performance
- Implement proper memory management for large files
- Use React.memo for expensive component renders
- Optimize hardware communication frequency
- Implement proper cleanup for unused resources
- Use debouncing/throttling for frequent events
- Monitor and log performance metrics