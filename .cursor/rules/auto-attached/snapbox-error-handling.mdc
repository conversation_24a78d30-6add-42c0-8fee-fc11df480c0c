description: Best practices for robust error management
globs: **/*.{ts,tsx}
alwaysApply: false
---

- Implement centralized error logging with electron-log
- Create user-friendly error messages for common issues
- Implement recovery mechanisms for hardware failures
- Use try/catch blocks for operations that might fail
- Log detailed error information for debugging purposes
- Handle network errors gracefully with offline fallbacks
- Implement proper state restoration after errors