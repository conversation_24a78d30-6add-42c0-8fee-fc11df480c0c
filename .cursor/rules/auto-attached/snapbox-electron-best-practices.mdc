description: Best practices for Snapbox Electron application
globs: **/*.{ts,tsx}
alwaysApply: false
---

- Use abstracted service classes for hardware interactions (cameras, printers)
- Implement proper error handling for hardware disconnections and reconnections
- Use IPC for communication between main and renderer processes
- Handle idle state detection for resetting the application to home screen
- Properly manage resources with cleanup on application exit
- Use protocol handlers for secure file access (app:// and video:// schemes)
- Implement dual window support for primary and secondary displays
