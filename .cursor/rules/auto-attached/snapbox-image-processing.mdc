description: Best practices for image manipulation and processing
globs: **/*.{ts,tsx}
alwaysApply: false
---

- Use Sharp for efficient image processing operations
- Implement caching strategies for processed images
- Handle memory efficiently when dealing with large image files
- Create modular functions for specific image operations
- Implement loading indicators for image processing tasks
- Use proper cleanup for temporary image files
- Optimize image quality for both display and printing
