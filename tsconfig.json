{"compilerOptions": {"jsx": "react-jsx", "target": "ES2021", "allowJs": true, "module": "commonjs", "skipLibCheck": true, "esModuleInterop": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "moduleResolution": "node", "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "paths": {"@/primary-screens/*": ["./src/screens/primary/*"], "@/common-screens/*": ["./src/screens/common/*"], "@/components/*": ["./src/components/*"], "@/assets/*": ["./src/assets/*"], "@/cores/*": ["./src/cores/*"], "@/hooks/*": ["./src/hooks/*"], "@/contexts/*": ["./src/contexts/*"], "@/graphql/*": ["./src/graphql/*"], "@/services/*": ["./src/services/*"], "@/ipc/*": ["src/services/ipc/*"], "@/redux/*": ["./src/redux/*"], "@/secondary-screens/*": ["./src/screens/secondary/*"]}, "typeRoots": ["node_modules/@types", "./global.d.ts", "./image.d.ts", "./worker.d.ts"], "types": ["react", "react-dom", "node"]}, "include": ["*.d.ts", "**/*.ts", "**/*.tsx", "printer-service/*.ts"]}