{"env": {"browser": true, "es6": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:import/electron", "plugin:import/typescript"], "parser": "@typescript-eslint/parser", "parserOptions": {"sourceType": "module", "ecmaVersion": 2020, "ecmaFeatures": {"jsx": true}}, "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unused-vars": "off"}}