{"name": "snapbox", "productName": "snapbox-app", "version": "1.8.3", "description": "My Electron application description", "author": "Snap Box Việt Nam", "homepage": "https://snapboxvietnam.com", "main": ".webpack/main", "icon": "./src/assets/snapbox.ico", "scripts": {"start": "electron-forge start", "package": "npm run rebuild && electron-forge package --arch x64", "make": "npm run rebuild && cross-env DEBUG=\"electron-forge:*,electron-rebuild:*\" electron-forge make --arch x64", "rebuild": "ts-node scripts/rebuild.ts", "publish": "electron-forge publish --arch x64", "lint": "eslint --ext .ts,.tsx .", "make-timelapse": "ts-node scripts/timelapse-maker.ts", "merge-img": "ts-node scripts/merge-img.ts", "print-double-s": "ts-node scripts/double-s-printer.ts", "upload-photo": "ts-node scripts/upload-photo.ts", "remove-bg": "ts-node scripts/remove-bg.ts"}, "devDependencies": {"@electron-forge/cli": "^7.2.0", "@electron-forge/maker-deb": "^7.2.0", "@electron-forge/maker-rpm": "^7.2.0", "@electron-forge/maker-squirrel": "^7.2.0", "@electron-forge/maker-zip": "^7.2.0", "@electron-forge/plugin-fuses": "^7.6.0", "@electron-forge/plugin-webpack": "^7.2.0", "@electron-forge/publisher-github": "^7.6.1", "@electron-forge/shared-types": "^7.6.0", "@electron-toolkit/eslint-config-prettier": "^2.0.0", "@electron/fuses": "^1.8.0", "@marshallofsound/webpack-asset-relocator-loader": "^0.5.0", "@serialport/parser-inter-byte-timeout": "^12.0.0", "@timfish/forge-externals-plugin": "^0.2.1", "@timfish/webpack-asset-relocator-loader": "^0.1.0", "@types/apollo-upload-client": "17.0.0", "@types/archiver": "^6.0.3", "@types/axios": "^0.14.4", "@types/cors": "^2", "@types/electron-squirrel-startup": "^1.0.2", "@types/fluent-ffmpeg": "^2.1.27", "@types/ini": "^4.1.1", "@types/jquery": "^3", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/mime-types": "^2.1.4", "@types/node": "^22.10.5", "@types/node-fetch": "^2.6.12", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-slick": "^0.23.13", "@types/redux-state-sync": "^3.1.10", "@types/semver": "^7", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vercel/webpack-asset-relocator-loader": "1.7.3", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "electron": "36.1.0", "electron-rebuild": "3.2.9", "electron-reloader": "^1.2.3", "electron-winstaller": "^5.4.0", "eslint": "^8.0.1", "eslint-plugin-import": "^2.25.0", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "fork-ts-checker-webpack-plugin": "6.5.3", "jquery": "^3.7.1", "node-loader": "^2.0.0", "postcss": "^8.4.47", "postcss-loader": "^8.1.1", "prettier": "^3.4.2", "protobufjs-loader": "^3.1.1", "semver": "^7.6.3", "serialport": "10.4.0", "sharp": "^0.33.5", "style-loader": "^4.0.0", "tailwindcss": "^3.4.14", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typescript": "^5.7.3", "webpack": "^5.97.1"}, "keywords": [], "license": "MIT", "dependencies": {"@apollo/client": "^3.12.4", "@cycjimmy/jsmpeg-player": "^6.1.2", "@electron-forge/plugin-auto-unpack-natives": "^7.6.0", "@grpc/grpc-js": "1.12.5", "@grpc/proto-loader": "0.7.13", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.3.4", "@larksuiteoapi/node-sdk": "^1.47.1", "@reduxjs/toolkit": "^2.6.0", "@vinacogroup/napi-canon-cameras": "0.0.12", "apollo-upload-client": "17.0.0", "archiver": "^7.0.1", "async-mutex": "^0.5.0", "axios": "^1.8.3", "bindings": "^1.5.0", "child_process": "^1.0.2", "clsx": "^2.1.1", "copy-webpack-plugin": "^12.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dayjs": "^1.11.13", "electron-log": "^5.2.4", "electron-packager": "^17.1.2", "electron-redux": "1.5.4", "electron-squirrel-startup": "^1.0.1", "electron-store": "^10.0.0", "express": "^4.21.2", "file-loader": "^6.2.0", "firebase": "^11.1.0", "fkill-cli": "^8.0.0", "form-data": "^4.0.2", "graphql": "^16.10.0", "html-to-image": "^1.11.11", "ini": "^5.0.0", "jimp": "^1.6.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "material-ripple-effects": "^2.0.1", "mime-types": "^3.0.1", "moment": "^2.30.1", "node-addon-api": "8.3.0", "node-fetch": "^3.3.2", "original-fs": "^1.2.0", "pako": "^2.1.0", "pm2": "^5.4.3", "qrcode": "^1.5.4", "react": "18.2.0", "react-dom": "18.2.0", "react-draggable": "^4.4.6", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.49.3", "react-qr-code": "^2.0.15", "react-redux": "^9.2.0", "react-router-dom": "6.28.0", "react-slick": "^0.30.2", "react-toastify": "^11.0.2", "redux-persist": "^6.0.0", "redux-state-sync": "^3.1.4", "sdp-transform": "^2.14.2", "slick-carousel": "^1.8.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "undici": "^7.10.0", "use-debounce": "^10.0.4", "web-gphoto2": "^0.4.1", "worker-loader": "^3.0.8", "ws": "^8.18.0", "yup": "^1.3.3"}, "config": {"forge": "./forge.config.ts"}, "ts-node": {"compilerOptions": {"module": "CommonJS"}}, "engines": {"node": "22.15.0"}}