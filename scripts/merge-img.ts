import { MergePhotosToFrameParams } from "@/cores/types";
import dayjs from "dayjs";
import fsPromises from "fs/promises";
import fetch from "node-fetch";
import path from "path";
import QRCode from "qrcode";
import sharp from "sharp";

// Thêm cache cho image buffers
const imageBufferCache = new Map<string, Buffer>();
const _isUrl = (string: string) => {
  try {
    return string?.startsWith("http://") || string?.startsWith("https://");
  } catch {
    return false;
  }
};

const _fetchImage = async (url: string) => {
  try {
    // Kiểm tra cache trước
    const cachedBuffer = imageBufferCache.get(url);
    if (cachedBuffer) {
      return cachedBuffer;
    }

    const response = await fetch(url);
    if (!response.ok)
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    const buffer = await response.buffer();
    imageBufferCache.set(url, buffer); // <PERSON><PERSON><PERSON> v<PERSON>o cache
    return buffer;
  } catch (error) {
    console.error("Error fetching image:", error);
    throw error;
  }
};

const _rotateImage: any = async (frame: any, processedImage: sharp.Sharp) => {
  try {
    // Normalize angle to 0-360 range
    const normalizedAngle = ((frame.angle % 360) + 360) % 360;
    // First resize to exact frame dimensions
    const resizedBuffer = await processedImage
      .resize(frame.width, frame.height, {
        fit: "cover",
        position: "center",
        background: { r: 255, g: 255, b: 255, alpha: 0 },
        withoutEnlargement: true,
        fastShrinkOnLoad: false,
      })
      .toBuffer();

    // Create new Sharp instance
    processedImage = sharp(resizedBuffer);

    // Normalize angle to -180 to 180 range for easier calculations
    const normalizedAngleAfterResize = Math.round(
      (((normalizedAngle % 360) + 540) % 360) - 180
    );

    // Apply rotation around center
    processedImage = processedImage.rotate(normalizedAngleAfterResize, {
      background: { r: 255, g: 255, b: 255, alpha: 0 },
    });

    const bufferProcessedImage = await processedImage.toBuffer();
    const boxMetadata = await sharp(bufferProcessedImage).metadata();

    const adjustedX =
      frame.x_coordinate - (boxMetadata.width - frame.width) / 2;
    const adjustedY =
      frame.y_coordinate - (boxMetadata.height - frame.height) / 2;

    processedImage = processedImage
    .withMetadata({ density: 300 })
    .sharpen({
      sigma: 0.3,
      m1: 0.1,
      m2: 0.2,
    })
    .png({
      quality: 100,
      compressionLevel: 9,
      adaptiveFiltering: true,
      force: true,
      palette: false,
    });

    // Return the processed image with adjusted coordinates
    return {
      data: await processedImage.toBuffer(),
      position: {
        left: Math.round(adjustedX),
        top: Math.round(adjustedY),
      },
    };
  } catch (error) {
    console.error("Detailed error in rotation processing:", error);
    throw error;
  }
};

const _processImage = async (
  imagePath: string,
  frame: any,
  isMixed: boolean,
  filterType?: string
) => {
  try {
    // Get buffer from URL, base64 or filesystem path
    let imageInput;
    if (_isBase64Image(imagePath)) {
      imageInput = _getBufferFromBase64(imagePath);
    } else if (_isUrl(imagePath)) {
      imageInput = await _fetchImage(imagePath);
    } else {
      imageInput = await sharp(imagePath).withMetadata().toBuffer();
    }

    // Lưu ảnh gốc để so sánh
    const originalFilename = path.basename(imagePath);
    const debugDir = path.join(process.cwd(), "debug-images");
    await fsPromises.mkdir(debugDir, { recursive: true });
    await sharp(imageInput).toFile(
      path.join(debugDir, `original_${originalFilename}`)
    );

    const { width: frameWidth, height: frameHeight } = frame;
    const metadata = await sharp(imageInput).metadata();
    const isVerticalFrame = frameHeight > frameWidth;

    const { extractOptions } = _calculateDimensions(
      metadata,
      frameWidth,
      frameHeight,
      isVerticalFrame
    );

    let processedImage = sharp(imageInput);
    if (extractOptions) {
      processedImage = processedImage.extract(extractOptions);
      // Lưu ảnh sau khi extract
      await processedImage
        .clone()
        .toFormat("png")
        .toFile(path.join(debugDir, `after_extract_${originalFilename}`));
    }

    if (filterType) {
      switch (filterType) {
        case "mono":
        case "mix":
          if (isMixed) {
            processedImage = processedImage.grayscale();
          }
          break;
        case "vivid_warm":
          processedImage = processedImage
            .modulate({
              brightness: 0.9,
              saturation: 1.2,
            })
            .linear(1.3, -50);
          break;
        case "vivid_cool":
          processedImage = processedImage.modulate({
            brightness: 0.9,
            saturation: 0.8,
            hue: 10,
          });
          break;
        case "dramatic":
          processedImage = processedImage
            .modulate({
              brightness: 0.9,
              saturation: 0.8,
              hue: -10,
            })
            .linear(1.45, -50)
            .recomb([
              [0.94, 0.06, 0.02],
              [0.05, 0.93, 0.02],
              [0.02, 0.05, 0.93],
            ]);
          break;
      }

      // Lưu ảnh sau khi áp dụng filter
      await processedImage
        .clone()
        .toFile(path.join(debugDir, `after_filter_${originalFilename}`));
    }

    // Thêm xử lý rotation trước khi resize
    if (frame.angle !== undefined && frame.angle !== 0) {
      return _rotateImage(frame, processedImage);
    }

    processedImage = processedImage.resize(frameWidth, frameHeight, {
      fit: "cover",
      withoutEnlargement: true,
      kernel: sharp.kernel.cubic,
      position: "center",
      background: { r: 255, g: 255, b: 255, alpha: 1 },
      fastShrinkOnLoad: false,
    });

    // Tiếp tục với các xử lý còn lại
    const finalImage = processedImage
      .withMetadata({ density: 300 })
      .sharpen({
        sigma: 0.3,
        m1: 0.1,
        m2: 0.2,
      })
      .png({
        quality: 100,
        compressionLevel: 9,
        adaptiveFiltering: true,
        force: true,
        palette: false,
      });

    // Lưu ảnh cuối cùng
    await finalImage
      .clone()
      .toFormat("png")
      .toFile(path.join(debugDir, `final_${originalFilename}`));

    // Cache kết quả
    const result = await finalImage.toBuffer();
    return { data: result, info: metadata };
  } catch (error) {
    console.error("Error in _processImage:", error);
    throw error;
  }
};

const _calculateDimensions = (
  metadata: any,
  frameWidth: number,
  frameHeight: number,
  isVerticalFrame: boolean
) => {
  return isVerticalFrame
    ? _calculateVerticalDimensions(metadata, frameWidth, frameHeight)
    : _calculateHorizontalDimensions(metadata, frameWidth, frameHeight);
};

const _calculateVerticalDimensions = (
  metadata: any,
  frameWidth: number,
  frameHeight: number
) => {
  const originalHeight = metadata.height;
  const targetWidth = Math.floor(originalHeight * (frameWidth / frameHeight));

  const extractOptions =
    targetWidth < metadata.width
      ? {
          left: Math.floor((metadata.width - targetWidth) / 2),
          top: 0,
          width: targetWidth,
          height: originalHeight,
        }
      : null;

  return {
    extractOptions,
    targetDimensions: { width: targetWidth, height: originalHeight },
  };
};

const _calculateHorizontalDimensions = (
  metadata: any,
  frameWidth: number,
  frameHeight: number
) => {
  const originalWidth = metadata.width;
  const targetHeight = Math.floor(originalWidth * (frameHeight / frameWidth));

  const extractOptions =
    targetHeight < metadata.height
      ? {
          left: 0,
          top: Math.floor((metadata.height - targetHeight) / 2),
          width: originalWidth,
          height: targetHeight,
        }
      : null;

  return {
    extractOptions,
    targetDimensions: { width: originalWidth, height: targetHeight },
  };
};

const calculateStickerPosition = (
  sticker: { x: number; y: number },
  viewWidth: number,
  viewHeight: number,
  frameWidth: number,
  frameHeight: number,
  stickerSize: number
) => {
  const scaleRatioX = frameWidth / viewWidth;
  const scaleRatioY = frameHeight / viewHeight;

  // Chuyển từ centered sang top-left trong hệ toạ độ view
  const viewTopLeftX = sticker.x + viewWidth / 2;
  const viewTopLeftY = sticker.y + viewHeight / 2;

  // Scale lên kích thước frame thực tế và căn giữa sticker
  const finalX = Math.round(viewTopLeftX * scaleRatioX - stickerSize / 2);
  const finalY = Math.round(viewTopLeftY * scaleRatioY - stickerSize / 2);

  return { finalX, finalY };
};

const _convertAppUrlToPath = (urlString: string) => {
  try {
    if (urlString.startsWith("app:///")) {
      // Remove 'app:///' and decode the remaining path
      const pathPart = decodeURIComponent(urlString.replace("app:///", ""));
      // Convert to proper file system path
      return path.normalize(pathPart);
    }
    return urlString;
  } catch (error) {
    console.error("Error converting app URL:", error);
    throw error;
  }
};

const _isBase64Image = (str: string) => {
  try {
    return str.startsWith("data:image/");
  } catch {
    return false;
  }
};

const _getBufferFromBase64 = (base64String: string) => {
  // Remove data URL prefix if present
  const base64Data = base64String.replace(/^data:image\/\w+;base64,/, "");
  return Buffer.from(base64Data, "base64");
};

const cleanup = (layers: any) => {
  imageBufferCache.clear(); // Clear the cache after processing
  layers.forEach((layer: any) => {
    if (layer.input instanceof Buffer) {
      layer.input = null;
    }
  });
};

const mergePhotosToFrame = async (
  _: any,
  {
    framePath,
    photoMaps,
    positions,
    filename,
    filterType,
    stickers,
    dates = [],
    viewWidth,
    viewHeight,
    qrCodes = [],
    qrUrl,
    dir,
    dateColor,
  }: MergePhotosToFrameParams
) => {
  let layers: any[] = [];
  let withoutQrLayers: any[] = [];

  try {
    let qrCodePromises: any[] = [];
    let datePromises: any[] = [];

    // Thêm code tạo thư mục nếu chưa tồn tại
    const outputDir = `C:\\Users\\<USER>\\snapbox-app\\scripts\\2f0b0c81-3729-4443-b3a6-4d241fd60005`;
    await fsPromises.mkdir(outputDir, { recursive: true });

    const localPath = path.join(outputDir, filename);
    const withoutQRLocalPath = path.join(outputDir, `without-qr-${filename}`);

    // Convert app:/// URL to file system path if necessary
    const resolvedFramePath = _convertAppUrlToPath(framePath);

    const frameBuffer = _isBase64Image(resolvedFramePath)
      ? _getBufferFromBase64(resolvedFramePath)
      : _isUrl(resolvedFramePath)
        ? await _fetchImage(resolvedFramePath)
        : resolvedFramePath;

    const frameMetadata = await sharp(frameBuffer).metadata();

    // Tạo một background trắng với kích thước của frame
    const baseImage = await sharp({
      create: {
        width: frameMetadata.width,
        height: frameMetadata.height,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 1 },
      },
    })
      .png()
      .toBuffer();

    // Xử lý ảnh song song
    const photoPromises = positions.map(async (frameItem) => {
      try {
        const { path } = photoMaps[frameItem.id] ?? {};
        const isMixed = frameItem.position % 2 === 0;

        const { data: resizedPhoto, position } = await _processImage(
          path,
          frameItem,
          isMixed,
          filterType
        );

        // Round position values to integers
        return {
          input: resizedPhoto,
          top: position ? Math.round(position.top) : frameItem.y_coordinate,
          left: position ? Math.round(position.left) : frameItem.x_coordinate,
        };
      } catch (err) {
        console.error(`Error processing photo:`, err);
        return null;
      }
    });

    datePromises =
      dates?.map(async (date) => {
        const w = date.width || 126;
        const h = date.height ? date.height * 1.5 : 30;

        const svgText = `
          <svg width="${w}" height="${h}">
            <text x="0%" y="80%" dominant-baseline="middle" style="fill:${dateColor};font-size:20px;font-family:Arial;">${dayjs().format("DD.MM.YYYY")}</text>
          </svg>
        `;

        const textBuffer = Buffer.from(svgText);
        const textProcessed = sharp(textBuffer);

        const { data: textProcessedBuffer, position } = await _rotateImage(
          date,
          textProcessed
        );
        return {
          input: textProcessedBuffer,
          top: position.top,
          left: position.left,
        };
      }) ?? [];

    if (qrCodes?.length > 0 && qrUrl) {
      qrCodePromises = qrCodes?.map(async (qrCode) => {
        const wQR = qrCode.width || 100;
        const hQR = qrCode.height || 100;

        const qrCodeImage = await QRCode.toDataURL(qrUrl, {
          margin: 1,
          width: wQR,
        });

        const qrBuffer = _getBufferFromBase64(qrCodeImage);
        const resizedQrCode = sharp(qrBuffer).resize(wQR, hQR, {
          fit: "cover",
          background: { r: 255, g: 255, b: 255, alpha: 0 },
        });

        const { data: resizedQrCodeBuffer, position } = await _rotateImage(
          qrCode,
          resizedQrCode
        );
        return {
          input: resizedQrCodeBuffer,
          top: position.top,
          left: position.left,
        };
      });
    }

    // Xử lý stickers song song
    const stickerPromises =
      stickers?.map(async (sticker) => {
        try {
          const stickerPath = _convertAppUrlToPath(sticker.image);
          const stickerBuffer = _isUrl(stickerPath)
            ? await _fetchImage(stickerPath)
            : stickerPath;

          const stickerSize = sticker.width || 50;
          const scaledSize = Math.round(
            stickerSize * (frameMetadata.width / viewWidth)
          );
          const { finalX, finalY } = calculateStickerPosition(
            sticker,
            viewWidth,
            viewHeight,
            frameMetadata.width,
            frameMetadata.height,
            scaledSize
          );

          const resizedSticker = await sharp(stickerBuffer)
            .resize(scaledSize, scaledSize)
            .rotate(sticker.rotation)
            .toBuffer();

          return {
            input: resizedSticker,
            top: finalY,
            left: finalX,
          };
        } catch (err) {
          console.error(`Error processing sticker:`, err);
          return null;
        }
      }) ?? [];

    // Chờ tất cả xử lý hoàn tất
    const [photoLayers, stickerLayers, qrCodeLayers, dateLayers] =
      await Promise.all([
        Promise.all(photoPromises),
        Promise.all(stickerPromises),
        Promise.all(qrCodePromises),
        Promise.all(datePromises),
      ]);

    // Kết hợp tất cả layers và lọc bỏ các layer null/undefined
    layers = [
      { input: baseImage }, // Thêm background trắng làm layer đầu tiên
      ...(photoLayers.filter(Boolean) as any[]),
      { input: frameBuffer },
      ...(stickerLayers.filter(Boolean) as any[]),
      ...dateLayers,
      ...qrCodeLayers,
    ];

    // Tạo composite một lần
    const composite = sharp(baseImage)
      .composite(layers)
      .withMetadata({ density: 300 })
      .sharpen({
        sigma: 0.3,
        m1: 0.1,
        m2: 0.2,
      })
      .png({
        quality: 100,
        compressionLevel: 9,
        adaptiveFiltering: true,
        force: true,
        palette: false,
      });

    console.log("localPath", localPath);
    console.log("withoutQRLocalPath", withoutQRLocalPath);

    await composite.toFile(localPath);

    if (qrCodes?.length && dates?.length) {
      withoutQrLayers = [
        { input: baseImage }, // Thêm background trắng làm layer đầu tiên
        ...(photoLayers.filter(Boolean) as any[]),
        { input: frameBuffer },
        ...(stickerLayers.filter(Boolean) as any[]),
      ];

      await composite
        .composite(withoutQrLayers)
        .withMetadata({ density: 300 })
        .toFormat("png")
        .png({
          quality: 100,
          compressionLevel: 9,
          adaptiveFiltering: true,
          force: true,
          palette: false,
        })
        .toFile(withoutQRLocalPath);

      return {
        localPath,
        withoutQRLocalPath,
      };
    }

    return {
      localPath,
      withoutQRLocalPath: localPath,
    };
  } catch (error) {
    console.error("Error merging photos with clipping:", error);
    console.error("Error details:", error.stack);
    throw error;
  } finally {
    cleanup(layers);
    cleanup(withoutQrLayers);
  }
};

const params: any = {
  framePath:
    "https://api-stag.snapboxvietnam.com/uploads/frames/a5e311bc-b8d5-4781-bc51-fd436170b93b/1743496138185/20250401-152628.png",
  photoMaps: {
    "3463efda-92cd-42f7-a075-2209c8794092": {
      path: "/Users/<USER>/Documents/Workspace/Vinaco/snapbox-app/scripts/2f0b0c81-3729-4443-b3a6-4d241fd60005/flipped_IMG_0001.JPG",
    },
    "63b90895-2349-4b66-ae61-58f327954e6d": {
      path: "/Users/<USER>/Documents/Workspace/Vinaco/snapbox-app/scripts/2f0b0c81-3729-4443-b3a6-4d241fd60005/flipped_IMG_0002.JPG",
    },
    "6ee13083-edc0-43f9-b82d-ed0b4b20d74b": {
      path: "/Users/<USER>/Documents/Workspace/Vinaco/snapbox-app/scripts/2f0b0c81-3729-4443-b3a6-4d241fd60005/flipped_IMG_0003.JPG",
    },
    "556b3d18-839c-4f62-8226-78bba0f37c32": {
      path: "/Users/<USER>/Documents/Workspace/Vinaco/snapbox-app/scripts/2f0b0c81-3729-4443-b3a6-4d241fd60005/flipped_IMG_0004.JPG",
    },
    "1f839c6f-76c1-4079-9f48-44395c3f1eb9": {
      path: "/Users/<USER>/Documents/Workspace/Vinaco/snapbox-app/scripts/2f0b0c81-3729-4443-b3a6-4d241fd60005/flipped_IMG_0005.JPG",
    },
    "1c187244-d9e2-4749-8033-acf7b658ccc9": {
      path: "/Users/<USER>/Documents/Workspace/Vinaco/snapbox-app/scripts/2f0b0c81-3729-4443-b3a6-4d241fd60005/flipped_IMG_0006.JPG",
    },
    "39c50fa9-01e2-47c6-8967-6582809c3939": {
      path: "/Users/<USER>/Documents/Workspace/Vinaco/snapbox-app/scripts/2f0b0c81-3729-4443-b3a6-4d241fd60005/flipped_IMG_0001.JPG",
    },
    "a664bed4-73b8-4d43-a114-5b4a93ac44b1": {
      path: "/Users/<USER>/Documents/Workspace/Vinaco/snapbox-app/scripts/2f0b0c81-3729-4443-b3a6-4d241fd60005/flipped_IMG_0001.JPG",
    },

    "61aaa341-6268-42c6-aa35-d2733cdbad4a": {
      path: "/Users/<USER>/Documents/Workspace/Vinaco/snapbox-app/scripts/2f0b0c81-3729-4443-b3a6-4d241fd60005/flipped_IMG_0001.JPG",
    },
  },
  positions: [
    {
      id: "3463efda-92cd-42f7-a075-2209c8794092",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "SubFrame",
      itemId: "69f091f0-fc28-4fac-bc99-e0fcadedcd80",
      x_coordinate: 270,
      parentId: null,
      y_coordinate: 512,
      width: 686,
      height: 616,
      position: 1,
      angle: 0,
    },
    {
      id: "63b90895-2349-4b66-ae61-58f327954e6d",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "SubFrame",
      itemId: "da1cd0d1-ca90-423f-9bd9-04672ef7b814",
      x_coordinate: 124,
      parentId: null,
      y_coordinate: 157,
      width: 287,
      height: 333,
      position: 2,
      angle: 20,
    },
    {
      id: "6ee13083-edc0-43f9-b82d-ed0b4b20d74b",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "SubFrame",
      itemId: "af03db4a-b0af-4f35-a0a5-24c5bc62cbdb",
      x_coordinate: 458,
      parentId: null,
      y_coordinate: 150,
      width: 285,
      height: 310,
      position: 3,
      angle: 0,
    },
    {
      id: "556b3d18-839c-4f62-8226-78bba0f37c32",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "SubFrame",
      itemId: "d313abc9-055a-415d-9614-e92400501b43",
      x_coordinate: 798,
      parentId: null,
      y_coordinate: 223,
      width: 256,
      height: 294,
      position: 4,
      angle: 349.534,
    },
    {
      id: "1f839c6f-76c1-4079-9f48-44395c3f1eb9",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "SubFrame",
      itemId: "a1f45541-20f8-41b6-bbb6-e34cc9cd5873",
      x_coordinate: 91,
      parentId: null,
      y_coordinate: 956,
      width: 265,
      height: 285,
      position: 5,
      angle: 337.131,
    },
    {
      id: "1c187244-d9e2-4749-8033-acf7b658ccc9",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "SubFrame",
      itemId: "29437246-fd67-4569-81df-e5cd257676a8",
      x_coordinate: 859,
      parentId: null,
      y_coordinate: 996,
      width: 249,
      height: 292,
      position: 6,
      angle: 21.4963,
    },
    {
      id: "39c50fa9-01e2-47c6-8967-6582809c3939",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "SubFrame",
      itemId: "f5776790-63c3-407e-930a-e3b9c05c97d3",
      x_coordinate: 186,
      parentId: null,
      y_coordinate: 1300,
      width: 248,
      height: 286,
      position: 7,
      angle: 16.6759,
    },
    {
      id: "a664bed4-73b8-4d43-a114-5b4a93ac44b1",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "SubFrame",
      itemId: "6eafb0f0-1629-4c66-9bbb-79376ae9b3b9",
      x_coordinate: 802,
      parentId: null,
      y_coordinate: 1342,
      width: 248,
      height: 289,
      position: 8,
      angle: 349.348,
    },
    {
      id: "61aaa341-6268-42c6-aa35-d2733cdbad4a",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "SubFrame",
      itemId: "16bb5f4d-9f39-4869-a1b5-2561796dc681",
      x_coordinate: 487,
      parentId: null,
      y_coordinate: 1193,
      width: 274,
      height: 319,
      position: 9,
      angle: 356.577,
    },
  ],
  dir: "orders/2f0b0c81-3729-4443-b3a6-4d241fd60005",
  filename: "merged-image-1741491763933.png",
  filterType: null,
  stickers: [],
  dateColor: "#000000",
  dates: [
    {
      id: "02d11f87-c010-426b-b1da-307f9c5e4e3b",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "Date",
      itemId: "10044f55-ec5d-4ec7-8eee-578265a4700d",
      x_coordinate: 322,
      parentId: null,
      y_coordinate: 41,
      width: 126,
      height: 22,
      position: 0,
      angle: 22.5623,
    }
  ],
  qrCodes: [
    {
      id: "2ab42a59-d5d0-43c0-8995-08293c80be57",
      frameId: "aa1bafdb-d33a-4197-80d6-56ce566dcbc8",
      itemType: "QrCode",
      itemId: "02b424b5-8c65-410a-8bb3-dbe5d128cee8",
      x_coordinate: 634,
      parentId: null,
      y_coordinate: 20,
      width: 100,
      height: 100,
      position: 0,
      angle: 330.913,
    },
  ],
  qrUrl:
    "https://memory-staging.snapboxvietnam.com/i/2f0b0c81-3729-4443-b3a6-4d241fd60005",
  viewWidth: 533.3333333333334,
  viewHeight: 800,
};

const execute = async () => {
  await mergePhotosToFrame(null, params);
};

execute();

// Thêm option để kiểm soát background khi xoay
interface RotationOptions {
  backgroundColor: {
    r: number;
    g: number;
    b: number;
    alpha: number;
  };
  quality: number;
}

const defaultRotationOptions: RotationOptions = {
  backgroundColor: { r: 255, g: 255, b: 255, alpha: 0 },
  quality: 100,
};

// Áp dụng trong xử lý xoay
const rotateImage = (
  image: sharp.Sharp,
  angle: number,
  options: Partial<RotationOptions> = {}
) => {
  const finalOptions = { ...defaultRotationOptions, ...options };

  return image.rotate(angle, {
    background: finalOptions.backgroundColor,
  });
};

const getCacheKey = (imagePath: string, angle: number) =>
  `${imagePath}_${angle}`;

const handleRotationError = (error: Error, frame: any) => {
  console.error(`Rotation failed for frame ${frame.id}:`, {
    angle: frame.angle,
    dimensions: `${frame.width}x${frame.height}`,
    error: error.message,
  });

  // Log chi tiết hơn cho debug
  if (process.env.DEBUG) {
    console.debug("Frame details:", frame);
    console.debug("Error stack:", error.stack);
  }

  throw error;
};
