import { exec, spawn } from "child_process";
import path from "path";

const snapboxAI = path.join(
  "C:",
  "Users",
  "M710q",
  "snapbox",
  "snapbox-ai.exe"
);

const workerData = {
  input:
    "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\8184096b-4146-49bf-8bcd-8ea9affa7e89/flipped_IMG_0001.jpg",
  output:
    "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\8184096b-4146-49bf-8bcd-8ea9affa7e89/no_background_flipped_IMG_0001.png",
};

const args = ["image", "-i", workerData.input, "-o", workerData.output];

const child = spawn(snapboxAI, args, {
  shell: false, // shell không cần nếu args sạch, nhưng để true cũng không sao
});

child.stdout.on("data", (data) => {
  console.log(`[stdout]: ${data}`);
});
child.stderr.on("data", (data) => {
  console.error(`[stderr]: ${data}`);
});
child.on("close", (code) => {
  console.log(`[close]: exited with code ${code}`);
});
