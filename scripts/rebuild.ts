import { exec as execCallback } from "child_process";
import { promisify } from "util";

const exec = promisify(execCallback);

const packages = [
  "serialport",
  "sharp",
  "@vinacogroup/napi-canon-cameras",
  "electron"
];

async function rebuildPackages() {
  try {
    for (const pkg of packages) {
      console.log(`Rebuilding ${pkg}...`);
      await exec(`electron-rebuild --arch=x64 -f -w ${pkg}`);
      console.log(`✓ ${pkg} rebuilt successfully`);
    }
    console.log("All packages rebuilt successfully!");
  } catch (error) {
    console.error("Error rebuilding packages:", error);
    process.exit(1);
  }
}

rebuildPackages();
