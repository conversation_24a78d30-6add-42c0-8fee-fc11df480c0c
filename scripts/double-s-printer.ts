import axios from "axios";
import path from "path";
import fs from "fs";
import sharp from "sharp";
import { downloadImage } from "../src/cores/utils/img";
import { getConfig } from "../src/cores/utils/config";

const config = getConfig();

const _isUrl = (string: string) => {
  try {
    return string?.startsWith("http://") || string?.startsWith("https://");
  } catch {
    return false;
  }
};

const _downloadImage = async (url: string) => {
  console.log(`[DEBUG] Đang tải ảnh từ URL: ${url}`);
  const localPath = path.join(__dirname, `temp_${Date.now()}.png`);
  const isDownloaded = await downloadImage({ url, localUrl: localPath });

  if (!isDownloaded) {
    throw new Error("[downloadImage] Không tải đ<PERSON>");
  }

  return localPath;
};

const updateMachineStatus = async (options: any) => {
  try {
    if (!options?.apiUrl) {
      throw new Error("[updateMachineStatus] Không tìm thấy apiUrl");
    }

    if (!options?.accessToken) {
      throw new Error("[updateMachineStatus] Không tìm thấy accessToken");
    }

    fs.readFile(
      "C:\\DNP\\HotFolderPrint\\Logs\\printer_status.txt",
      "utf8",
      async (err, data) => {
        if (err) {
          console.error("Error reading log file:", err);
          return;
        }

        const jsonData = JSON.parse(data);
        const rx1 = jsonData.find((item: any) => item.Name === "RX1HS");

        if (!rx1 || !rx1.MediaRemaining || !rx1.Status) {
          console.error("Không tìm thấy dữ liệu máy in RX1HS");
          return;
        }

        await axios.post(
          options?.apiUrl,
          {
            query: `
          mutation (
            $pendingPrints: Float!
            $remainingMedia: Float!
            $remainingInk: Float!
            $state: String!
          ) {
            clientAppUpdateMachine(
              input: {
                state: $state
                pendingPrints: $pendingPrints
                remainingInk: $remainingInk
                remainingMedia: $remainingMedia
              }
            ) {
              success
            }
          }
        `,
            variables: {
              pendingPrints: 0,
              remainingMedia: rx1.MediaRemaining - options.quantity,
              remainingInk: 0,
              state: rx1.Status,
            },
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${options.accessToken}`,
            },
          }
        );
      }
    );
  } catch (error) {
    console.error(
      "[Printer Worker] Lỗi khi cập nhật trạng thái máy in:",
      error
    );
  }
};

async function execute(
  inputPath: string,
  outputPath: string,
  copies = 1,
  paddings: any
) {
  let imageBuffer = await sharp(inputPath).toBuffer();
  const metadata = await sharp(imageBuffer).metadata();

  // Xoay ảnh nếu width > height
  if (metadata.width && metadata.height && metadata.width > metadata.height) {
    imageBuffer = await sharp(imageBuffer).rotate(90).toBuffer();
    // Cập nhật metadata sau khi xoay
    const newMetadata = await sharp(imageBuffer).metadata();
    metadata.width = newMetadata.width;
    metadata.height = newMetadata.height;
  }

  const width = metadata.width || 0;
  const height = metadata.height || 0;

  const originalFileName = path.basename(inputPath);
  const fileExtension = path.extname(originalFileName);
  const fileNameWithoutExt = originalFileName.slice(0, -fileExtension.length);
  const imagesToProcess = [];

  imagesToProcess.push({ buffer: imageBuffer, suffix: "" });

  // Xử lý và lưu từng ảnh
  for (let imageIndex = 0; imageIndex < imagesToProcess.length; imageIndex++) {
    const { buffer, suffix } = imagesToProcess[imageIndex];
    for (let i = 0; i < copies; i++) {
      // Tạo tên file cho mỗi bản sao
      const timestamp = Date.now();
      const copyFileName =
        copies > 1
          ? `${fileNameWithoutExt}${suffix}_${i + 1}_${timestamp}${fileExtension}`
          : `${fileNameWithoutExt}${suffix}_${timestamp}${fileExtension}`;

      // Tạo đường dẫn đầy đủ đến file trong thư mục output
      const copyOutputPath = `${outputPath}\\${copyFileName}`;
      console.log(`[DEBUG] copyOutputPath: ${copyOutputPath}`);
      await sharp({
        create: {
          width,
          height,
          channels: 3,
          background: { r: 255, g: 255, b: 255 }, // Nền trắng
        },
      })
        .composite([
          {
            input: await sharp(buffer)
              .resize(paddings.width, paddings.height) // Thu nhỏ ảnh
              .toBuffer(),
            top: paddings.top,
            left: paddings.left,
          },
        ])
        .toFile(copyOutputPath)
        .then((res) => {
          console.log(`[DEBUG] Lưu ảnh thành công: ${res}`);
        })
        .catch((err) => {
          console.error("❌ Lỗi khi lưu ảnh:", err);
        });
    }
  }
}

const checkInternetConnection = async () => {
  console.log(`[DEBUG] Đang kiểm tra kết nối internet...`);
  try {
    await axios.get("https://www.google.com", { timeout: 5000 });
    console.log(`[DEBUG] Kết nối internet OK`);
    return true;
  } catch (error) {
    console.log(`[DEBUG] Không có kết nối internet: ${error}`);
    return false;
  }
};

export const printDoubleS = async (payload: any) => {
  console.log(`[DEBUG] Nhận yêu cầu in ảnh:`, JSON.stringify(payload));
  try {
    const { imgUrl, options } = payload;
    const copies = options.quantity;
    const cutable = options.cutable;
    const paddings = options.paddings;
    let printUrl = imgUrl;
    const targetFolder = cutable ? "4x6_2IN" : "4x6";
    let output = path.join("C:", "DNP", "Hot Folder", "Prints", targetFolder);

    if (config.sharedPrinterDir) {
      output = `\\${config.sharedPrinterDir}\\${targetFolder}`;
    }

    console.log(`[DEBUG] config: ${JSON.stringify(config)}`);
    console.log(`[DEBUG] sharedPrinterDir: ${config.sharedPrinterDir}`);
    console.log(`[DEBUG] output: ${output}`);

    fs.access(output, fs.constants.W_OK, (err) => {
      if (err) {
        console.error("❌ Không có quyền ghi vào thư mục:", output);
        console.error(err.message);
      } else {
        console.log("✅ Có quyền ghi vào thư mục:", output);
      }
    });

    if (_isUrl(imgUrl)) {
      console.log(`[DEBUG] imgUrl là URL, đang kiểm tra kết nối internet...`);
      const hasInternet = await checkInternetConnection();
      if (!hasInternet) {
        console.log(`[DEBUG] Không có kết nối internet, hủy in ảnh`);
        return;
      }

      printUrl = await _downloadImage(imgUrl);
    }

    console.log(`[DEBUG] Bắt đầu thực hiện in ảnh...`);
    await execute(printUrl, output, copies, paddings);
    console.log(`[DEBUG] Hoàn thành in ảnh`);
    // updateMachineStatus(options);
  } catch (error) {
    console.error(`[DEBUG] Lỗi chi tiết khi in:`, error);
    console.error(
      `[DEBUG] Stack trace:`,
      error instanceof Error ? error.stack : "Không có stack trace"
    );
  }
};

printDoubleS({
  imgUrl:
    "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\8ca94b07-f39e-4359-912b-edb8ed119bf6\\merged-image-1745471999473.png",
  options: {
    quantity: 1,
    cutable: true,
    apiUrl: "https://api.snapboxvietnam.com/graphql",
    accessToken:
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImU1NDhjMDk2LTM1NmQtNDI3Yy1iNjZiLTdkNDJjNzliNTQzZSIsIm5hbWUiOiJTbmFwQm94IFZp4buHdCBOYW0iLCJlbWFpbCI6ImNvbnRhY3RAc25hcGJveHZpZXRuYW0uY29tIiwicGFzc3dvcmQiOiIkMmIkMTAkWHVNVGFLRmxON1l3ZXJ3WEZqSHZYT3NPRGJYVXk0Tm1pbkFCOHphWi8uU0hiNkJ4YjBMZE8iLCJwaG9uZSI6IjA4Njk3NTcwMzMiLCJwcm92aW5jZSI6IkjDoCBO4buZaSIsImFkZHJlc3MiOiJMb25nIEJpw6puIiwiY3JlYXRlZEF0IjoiMTczNzY4OTkzNzQyMCIsImNyZWF0ZWRCeSI6IjQ5ZmVkYzA5LWE0N2QtNDUxYy1hNmI4LWFjMmNhMDg4MTg2NCIsInVwZGF0ZWRBdCI6IjE3Mzk5MzUzNDQwNTQiLCJ1cGRhdGVkQnkiOiI0OWZlZGMwOS1hNDdkLTQ1MWMtYTZiOC1hYzJjYTA4ODE4NjQiLCJ0b3RhbE9yZGVycyI6MiwidG90YWxNYWNoaW5lcyI6MCwibWFjaGluZUlkIjoiYTk3NzlkNmItMmY3MC00NDYxLWE4MTYtZGUxZDc1ZWYxZWU5IiwibWFjaGluZUNvZGUiOiJTTkFQQk9YMTczNzY5ODc4Njg2OCIsImlhdCI6MTc0MjQ4Mjg5OX0.QXoRm6wtQ-tMZXw6-EjGJhOpNNr23nWd3CXDfi4cc_U",
    paddings: { top: 16, left: 18, width: 1176, height: 1770 },
  },
});
