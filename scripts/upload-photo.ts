import path from "path";
import archiver from "archiver";
import sharp from "sharp";
import fsPromises from "fs/promises";
import { createWriteStream } from "fs";
import { retryUpload } from "../src/cores/utils/upload";

const CLIENT_APP_UPDATE_ORDER = `
  mutation clientAppCreateImage($input: CreateImageInput!, $file: Upload!) {
    clientAppCreateImage(input: $input, file: $file) {
      message
      captureMode
      domain
    }
  }
`;

const tryForUpload = async () => {
  try {
    const params = {
      photos: [
        "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\bcaf4f1b-19d7-4316-b781-0f0b66ab58b3/flipped_IMG_0001.JPG",
        "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\bcaf4f1b-19d7-4316-b781-0f0b66ab58b3/flipped_IMG_0002.JPG",
        "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\bcaf4f1b-19d7-4316-b781-0f0b66ab58b3/flipped_IMG_0003.JPG",
        "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\bcaf4f1b-19d7-4316-b781-0f0b66ab58b3/flipped_IMG_0004.JPG",
        "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\bcaf4f1b-19d7-4316-b781-0f0b66ab58b3/flipped_IMG_0005.JPG",
        "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\bcaf4f1b-19d7-4316-b781-0f0b66ab58b3/flipped_IMG_0006.JPG",
        "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\bcaf4f1b-19d7-4316-b781-0f0b66ab58b3/flipped_IMG_0007.JPG",
        "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\bcaf4f1b-19d7-4316-b781-0f0b66ab58b3/flipped_IMG_0008.JPG",
      ],
      finalUrl:
        "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\orders\\bcaf4f1b-19d7-4316-b781-0f0b66ab58b3\\without-qr-merged-image-1748055592449.png",
      orderId: "bcaf4f1b-19d7-4316-b781-0f0b66ab58b3",
      captureMode: "AUTO",
      apiUrl: "https://api-stag.snapboxvietnam.com/graphql",
      accessToken:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImE1ZTMxMWJjLWI4ZDUtNDc4MS1iYzUxLWZkNDM2MTcwYjkzYiIsIm5hbWUiOiJTTkFQQk9YIERFViBQUk9EIiwib3duZXJOYW1lIjpudWxsLCJlbWFpbCI6InByb2RAc25hcGJveHZpZXRuYW0uY29tIiwicGFzc3dvcmQiOiIkMmIkMTAkbVJ5dG1jNjg0ZTkuLllvSUlYemFuZVNrN2dKcDhXTTdDT0R2RXFtalZPVTROem1pL2hrTm0iLCJwaG9uZSI6IjA5MDkwMDkwMDEiLCJwcm92aW5jZSI6IkjDoCBO4buZaSIsImFkZHJlc3MiOiJCMiBUVDYgS8SQVCBWxINuIFF1w6FuIiwiY3JlYXRlZEF0IjoiMTc0MTc3NzkxMjYwNyIsImNyZWF0ZWRCeSI6IjQ5ZmVkYzA5LWE0N2QtNDUxYy1hNmI4LWFjMmNhMDg4MTg2NCIsInVwZGF0ZWRBdCI6bnVsbCwidXBkYXRlZEJ5IjpudWxsLCJ0b3RhbE9yZGVycyI6MiwidG90YWxNYWNoaW5lcyI6MCwibWFjaGluZUlkIjoiZTlhYTk0NDgtMGNmNS00ZTA2LWJkNGQtMmM2YmVlNWM2ODliIiwibWFjaGluZUNvZGUiOiJTTkFQQk9YMTc0MTc3Nzk0NzA0OCIsImlhdCI6MTc0Nzg4MTgzMH0.3n9tfmJLUcH1F7mjR4Iyn-O17ypP00HZm7icecYClrk",
      baseDir: "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\temp",
    };

    await fsPromises.mkdir(params.baseDir, { recursive: true });

    const zipPath = path.join(params.baseDir, `order-${Date.now()}.zip`);
    const output = createWriteStream(zipPath);
    const archive = archiver("zip", {
      zlib: { level: 9 },
    });

    // Log archive progress
    archive.on("progress", (progress: any) => {
      console.log("Archive progress:", progress);
    });

    output.on("close", async () => {
      console.log("Archive created:", zipPath);
      try {
        const result = await retryUpload({
          orderId: params.orderId,
          captureMode: params.captureMode,
          fileUrl: zipPath,
          apiUrl: params.apiUrl,
          accessToken: params.accessToken,
          workerName: "upload-photo",
          query: CLIENT_APP_UPDATE_ORDER,
        });

        console.log("Upload result:", result);

        process.exit(0);
      } catch (error) {
        console.log("Error uploading:", error);
        throw error;
      }
    });

    archive.on("warning", (err: any) => {
      console.warn("Archive warning:", err);
    });

    archive.on("error", (err: any) => {
      console.log("Archive error:", err);
      throw err;
    });

    archive.pipe(output);

    const compressPromises = params.photos.map(async (photo: string) => {
      try {
        const compressedBuffer = await sharp(photo)
          .webp({ quality: 90 })
          .resize(1500, 1000, { fit: "cover" })
          .toBuffer();
        return { buffer: compressedBuffer, name: path.basename(photo) };
      } catch (err) {
        console.error("Error processing photo:", photo, err);
        throw err;
      }
    });

    const compressedFiles = await Promise.all(compressPromises);
    for (const { buffer, name } of compressedFiles) {
      archive.append(buffer, { name });
    }

    try {
      archive.file(params.finalUrl, {
        name: path.basename(params.finalUrl),
      });
    } catch (err) {
      console.error("Error processing final image:", err);
      throw err;
    }

    // Không cần Promise này nữa vì chúng ta đã xử lý trong output.on("close")
    // Chỉ cần xử lý lỗi
    archive.on("error", (err: any) => {
      console.error("Archive error:", err);
      throw err;
    });

    await archive.finalize();
    console.log("Archive finalized successfully");
  } catch (error) {
    console.error("Error in updateOrder:", error);
    console.error("Error stack:", error.stack);
    throw error;
  }
};

tryForUpload();
