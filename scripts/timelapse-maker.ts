import ffmpeg from "fluent-ffmpeg";
import fs from "fs";
import path from "path";

const ffmpegPath = path.join(__dirname, "..", ".webpack", "main", "ffmpeg.exe");

ffmpeg.setFfmpegPath(ffmpegPath);

// C<PERSON><PERSON> hình
const TIMELAPSE_FOLDER =
  "C:\\Users\\<USER>\\AppData\\Roaming\\snapbox-app\\timelapse"; // Thay đổi đường dẫn này nếu cần
// <PERSON><PERSON><PERSON> bảo thư mục tồn tại
if (!fs.existsSync(TIMELAPSE_FOLDER)) {
  fs.mkdirSync(TIMELAPSE_FOLDER, { recursive: true });
}

// Lấy đường dẫn tuyệt đối của thư mục timelapse
const absoluteTimelapseDir = path.resolve(TIMELAPSE_FOLDER);

// Kiểm tra và sắp xếp frames theo thứ tự
fs.readdirSync(absoluteTimelapseDir)
  .sort()
  .forEach((f) => console.log(f));

// Create a file list for FFmpeg
const fileListPath = path.join(absoluteTimelapseDir, "filelist.txt");
const fileListContent = fs
  .readdirSync(absoluteTimelapseDir)
  .filter((file) => file.endsWith(".jpg"))
  .sort()
  .map((file) => `file '${path.join(absoluteTimelapseDir, file)}'`)
  .join("\n");

fs.writeFileSync(fileListPath, fileListContent);

// Tạo video
const outputPath = path.join(__dirname, `timelapse_${Date.now()}.mp4`);
// Tạo ffmpeg command với các parameter được điều chỉnh
const ffmpegCommand = ffmpeg()
  .input(fileListPath)
  .inputOptions(["-f", "concat", "-safe", "0"])
  .outputOptions([
    "-c:v",
    "libx264",
    "-preset",
    "medium",
    "-crf",
    "28",
    "-pix_fmt",
    "yuv420p",
    "-movflags",
    "+faststart",
    "-vf",
    "scale=960:-2",
    "-framerate",
    "30",
  ])
  .output(outputPath);

// Thêm logging chi tiết hơn
ffmpegCommand.on("start", (commandLine) => {
  console.log("FFmpeg command:", commandLine);
  console.log("Input file list:", fileListPath);
  console.log("Output path:", outputPath);
});

ffmpegCommand.on("error", (err, stdout, stderr) => {
  console.error("FFmpeg error:", err);
  console.error("FFmpeg stdout:", stdout);
  console.error("FFmpeg stderr:", stderr);
});

ffmpegCommand.on("end", () => {
  console.log("FFmpeg processing completed");
  const stats = fs.statSync(outputPath);
  console.log("Output file size:", stats.size);

  // Kiểm tra file output có phải là file MP4 hợp lệ không
  const buffer = Buffer.alloc(8);
  const fd = fs.openSync(outputPath, "r");
  fs.readSync(fd, buffer, 0, 8, 0);
  fs.closeSync(fd);

  // Check MP4 signature
  console.log("Output file signature:", buffer.toString("hex"));

  // Clean up file list
  fs.unlinkSync(fileListPath);
});

// Chạy ffmpeg
ffmpegCommand.run();
