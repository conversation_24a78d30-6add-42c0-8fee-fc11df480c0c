import type { ForgeConfig } from "@electron-forge/shared-types";
import { WebpackPlugin } from "@electron-forge/plugin-webpack";
import { FusesPlugin } from "@electron-forge/plugin-fuses";
import { FuseV1Options, FuseVersion } from "@electron/fuses";
import { mainConfig } from "./webpack.main.config";
import { rendererConfig } from "./webpack.renderer.config";
import { spawn } from "child_process";
import path from "path";
import fs from "fs";

const config: ForgeConfig = {
  packagerConfig: {
    asar: false,
    name: "Snap Box",
    icon: "./src/assets/snapbox",
    executableName: "snapbox",
    appBundleId: "com.snapboxvietnam.app",
    win32metadata: {
      CompanyName: "Snap Box Vietnam",
      FileDescription: "Snap Box Application",
      ProductName: "Snap Box",
    },
    extraResource: [
      "./src/assets/fonts/SVN-Gilroy Bold.otf",
      "./src/assets/fonts/SVN-Gilroy Medium.otf",
      "./src/assets/fonts/SVN-Gilroy Regular.otf",
      "./src/assets/fonts/SVN-Gilroy SemiBold.otf",
    ],
  },
  makers: [
    {
      name: "@electron-forge/maker-squirrel",
      config: {
        loadingGif: "./src/assets/loading.gif",
        setupIcon: "./src/assets/setup.ico",
        iconUrl: "https://api.snapboxvietnam.com/uploads/snapbox.ico",
        skipUpdateIcon: true,
        arch: ["x64"],
        platform: ["win32"],
        authors: "Snap Box",
        companyName: "Snap Box Vietnam",
        description: "Snap Box Application",
        exe: "snapbox.exe",
        name: "snapbox",
        updateUrl: `https://update.snapboxvietnam.com/update/`,
        generateUpdatesFilesForAllChannels: true,
      },
    },
  ],
  plugins: [
    // {
    //   name: '@electron-forge/plugin-auto-unpack-natives',
    //   config: {
    //     platform: ['win32'],
    //     arch: ['x64']
    //   }
    // },
    new WebpackPlugin({
      mainConfig,
      devContentSecurityPolicy: "connect-src 'self' * 'unsafe-eval'",
      renderer: {
        config: rendererConfig,
        entryPoints: [
          {
            html: "./src/primary.html",
            js: "./src/screens/primary/renderer.ts",
            name: "main_window",
            preload: {
              js: "./src/screens/primary/preload.ts",
            },
          },
          {
            html: "./src/secondary.html",
            js: "./src/screens/secondary/renderer.ts",
            name: "secondary_window",
            preload: {
              js: "./src/screens/secondary/preload.ts",
            },
          },
        ],
      },
      port: 3000,
      loggerPort: 9000,
    }),
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: true,
      [FuseV1Options.EnableNodeCliInspectArguments]: true,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: false,
    }),
  ],
  hooks: {
    packageAfterPrune: async (
      _: any,
      buildPath: any,
      __: any,
      platform: any
    ) => {
      // Copy .npmrc file
      fs.copyFileSync(
        path.join(process.cwd(), ".npmrc"),
        path.join(buildPath, ".npmrc")
      );

      // Tạo package.json tạm thời cho native modules
      const tempPackageJson = {
        name: "snapbox",
        productName: "snapbox-app",
        version: "1.8.3",
        description: "Snap Box Application",
        main: ".webpack/main",
        dependencies: {
          serialport: "10.4.0",
          "@vinacogroup/napi-canon-cameras": "0.0.12",
          sharp: "0.33.5",
          "@grpc/grpc-js": "1.12.5",
          "@grpc/proto-loader": "0.7.13",
          "ffmpeg-static": "5.2.0",
          "fluent-ffmpeg": "2.1.3",
          pm2: "5.2.2",
        },
      };

      // Ghi file package.json tạm thời
      fs.writeFileSync(
        path.join(buildPath, "package.json"),
        JSON.stringify(tempPackageJson, null, 2)
      );

      return new Promise((resolve, reject) => {
        // Cài đặt tất cả dependencies cùng một lúc
        const npmInstall = spawn("npm", ["install", "--no-package-lock"], {
          cwd: buildPath,
          stdio: "inherit",
          shell: true,
        });

        npmInstall.on("close", (code) => {
          if (code === 0) {
            /**
             * On windows code signing fails for ARM binaries etc.,
             * we remove them here
             */
            if (platform === "win32") {
              const problematicPaths = [
                "android-arm",
                "android-arm64",
                "darwin-x64+arm64",
                "linux-arm",
                "linux-arm64",
                "linux-x64",
              ];

              problematicPaths.forEach((binaryFolder) => {
                fs.rmSync(
                  path.join(
                    buildPath,
                    "node_modules",
                    "@serialport",
                    "bindings-cpp",
                    "prebuilds",
                    binaryFolder
                  ),
                  { recursive: true, force: true }
                );
              });
            }

            resolve(void 0);
          } else {
            reject(new Error("Package installation failed with code " + code));
          }
        });

        npmInstall.on("error", (error) => {
          reject(error);
        });
      });
    },
  },
  publishers: [
    {
      name: "@electron-forge/publisher-github",
      config: {
        repository: {
          owner: "vinacogroup",
          name: "snapbox-app",
        },
        prerelease: false,
        draft: true,
      },
    },
  ],
};

export default config;
