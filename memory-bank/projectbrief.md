# Snapbox Project Brief

## Project Overview
Snapbox is a photo booth application developed by Snap Box Vietnam. It's an Electron-based desktop application that provides a complete photo booth experience, allowing users to capture photos, customize them with frames, filters, and stickers, and print the final compositions.

## Core Objectives
1. Provide a full-featured photo booth experience
2. Support hardware integration with cameras (Canon and built-in)
3. Enable efficient photo capture, editing, and printing workflow
4. Offer customization options including frames, filters, and stickers
5. Support both Vietnamese and English language interfaces
6. Enable local and remote printing capabilities
7. Integrate with online services for digital sharing and storage

## Key Features
1. **Photo Capture**
   - Canon camera integration
   - Built-in camera support (webcam/Macbook)
   - Multiple capture modes (manual/automatic)
   - Countdown timer functionality
   - Support for multiple displays (primary and secondary)

2. **Photo Customization**
   - Frame selection and application
   - Filter effects
   - Sticker placement and manipulation
   - Date stamp options
   - QR code generation for digital retrieval

3. **Printing Functionality**
   - Support for various print sizes
   - Multiple print quantity options
   - Print preview
   - Print quality settings

4. **Payment Processing**
   - Cash payment handling
   - QR code payment integration

5. **User Flow Management**
   - Guided user interface
   - Idle detection and reset
   - Multilingual support (Vietnamese/English)

6. **Additional Features**
   - Time-lapse video creation
   - Remote print job handling
   - Auto-update functionality
   - Offline operation capabilities
   - Synchronization with online services

## Technical Requirements
1. Cross-platform compatibility (primarily Windows)
2. Hardware integration with Canon cameras, printers
3. Efficient image processing capabilities
4. Stable performance for commercial use
5. Ability to handle high-resolution images
6. Robust error handling for hardware interactions

## Target Users
- Photo booth operators
- End users of photo booth services
- Event attendees
- Retail photo booth locations
