# System Patterns

## Architecture Overview
Snapbox is built using Electron with React for the UI layer. The application follows a layered architecture:

1. **UI Layer**: React components organized by screen and functionality
2. **State Management**: Redux for global state management
3. **Services Layer**: Abstracted services for hardware interaction
4. **Hardware Interface**: Native modules for camera and printer integration

## Key Technical Decisions
1. **Electron Framework**: Chosen for cross-platform desktop capabilities and hardware access
2. **React+Redux**: Used for component-based UI development and centralized state management
3. **TypeScript**: Employed for type safety and improved developer experience
4. **Hardware Abstraction**: Services designed to abstract hardware interaction details
5. **Dual Window Support**: Architecture supports primary and secondary windows for different displays

## Design Patterns
1. **Service Pattern**: Hardware interactions encapsulated in service classes
2. **Repository Pattern**: Data access abstracted through repository interfaces
3. **Factory Pattern**: Used for creating appropriate service instances based on configuration
4. **Observer Pattern**: Event-based communication between system components
5. **Middleware Pattern**: Used in Redux for handling side effects

## Component Relationships
1. **UI Hierarchy**: 
   - Atoms (basic UI elements)
   - Molecules (composite components)
   - Organisms (screen sections)
   - Screens (full screen implementations)

2. **Service Relationships**:
   - Camera Service → Hardware
   - Printer Service → Hardware
   - IPC Service → Inter-Process Communication
   - Storage Service → Data Persistence

3. **State Management**:
   - Redux Store
   - Action Creators
   - Reducers
   - Selectors
   - Hooks for component connection

## Critical Implementation Paths
1. **Photo Capture Flow**:
   - Camera setup → Live preview → Capture → Image processing → Display
   
2. **Printing Flow**:
   - Image composition → Print preview → Print job creation → Print monitoring

3. **Payment Processing**:
   - Order creation → Payment method selection → Payment processing → Confirmation

4. **Customization Flow**:
   - Frame selection → Filter application → Sticker placement → Final composition

5. **User Authentication**:
   - Login → Sync data → Session management

## Data Flow
1. **User Input → Redux Actions → State Updates → UI Updates**
2. **Hardware Events → Service Callbacks → Redux Actions → State Updates**
3. **API Requests → Data Processing → State Integration → UI Updates**

## Error Handling Strategy
1. **Hardware Errors**: Attempt recovery, provide user feedback, log detailed errors
2. **Network Errors**: Graceful degradation to offline mode, retry mechanisms
3. **Application Errors**: Capture, log, and present user-friendly messages
4. **Validation Errors**: Immediate feedback with clear guidance
